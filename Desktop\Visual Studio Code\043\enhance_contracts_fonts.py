#!/usr/bin/env python3
"""
Contract Font Enhancement Script for Qatar Car Dealership System
Enhances Arabic fonts in PDF and Word contracts
"""

import os
import shutil
from pathlib import Path

class ContractFontEnhancer:
    def __init__(self):
        self.fonts_dir = Path("static/fonts")
        self.contracts_dir = Path("templates/contracts")
        
    def check_font_availability(self):
        """Check available Arabic fonts for contracts"""
        print("🔍 فحص الخطوط المتاحة للعقود...")
        print("=" * 40)
        
        required_fonts = [
            "Cairo[slnt,wght].ttf",
            "NotoSansArabic[wdth,wght].ttf", 
            "NotoNaskhArabic-Regular.ttf",
            "Amiri-Regular.ttf"
        ]
        
        available_fonts = []
        missing_fonts = []
        
        for font in required_fonts:
            font_path = self.fonts_dir / font
            if font_path.exists():
                file_size = os.path.getsize(font_path)
                print(f"✅ {font} ({file_size:,} bytes)")
                available_fonts.append(font)
            else:
                print(f"❌ {font} - غير موجود")
                missing_fonts.append(font)
        
        print(f"\n📊 الخطوط المتاحة: {len(available_fonts)}/{len(required_fonts)}")
        
        if missing_fonts:
            print(f"⚠️  الخطوط المفقودة: {', '.join(missing_fonts)}")
            print("💡 استخدم download_fonts.py لتحميل الخطوط المفقودة")
        
        return available_fonts, missing_fonts
    
    def test_pdf_fonts(self):
        """Test PDF font rendering"""
        print("\n🔧 اختبار خطوط PDF...")
        print("=" * 25)
        
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.pdfbase import pdfmetrics
            import tempfile
            
            # Create test PDF
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            temp_path = temp_file.name
            temp_file.close()
            
            c = canvas.Canvas(temp_path, pagesize=A4)
            width, height = A4
            
            # Test each font
            fonts_to_test = [
                ("Cairo", "static/fonts/Cairo[slnt,wght].ttf"),
                ("NotoSansArabic", "static/fonts/NotoSansArabic[wdth,wght].ttf"),
                ("NotoNaskhArabic", "static/fonts/NotoNaskhArabic-Regular.ttf"),
                ("Amiri", "static/fonts/Amiri-Regular.ttf")
            ]
            
            y_position = height - 100
            
            for font_name, font_path in fonts_to_test:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont(font_name, font_path))
                        c.setFont(font_name, 16)
                        
                        # Test Arabic text
                        test_text = f"اختبار خط {font_name} - معرض بوخليفة للسيارات"
                        c.drawRightString(width - 50, y_position, test_text)
                        
                        print(f"✅ {font_name} - يعمل بشكل صحيح")
                        y_position -= 30
                        
                    except Exception as e:
                        print(f"❌ {font_name} - خطأ: {e}")
                else:
                    print(f"⚠️  {font_name} - الملف غير موجود")
            
            c.save()
            
            # Check file size
            file_size = os.path.getsize(temp_path)
            print(f"\n📄 تم إنشاء ملف اختبار PDF ({file_size:,} bytes)")
            print(f"📁 المسار: {temp_path}")
            
            # Clean up
            os.unlink(temp_path)
            
            return True
            
        except ImportError:
            print("❌ مكتبة reportlab غير مثبتة")
            print("💡 قم بتثبيتها: pip install reportlab")
            return False
        except Exception as e:
            print(f"❌ خطأ في اختبار PDF: {e}")
            return False
    
    def test_word_fonts(self):
        """Test Word font rendering"""
        print("\n📝 اختبار خطوط Word...")
        print("=" * 25)
        
        try:
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            import tempfile
            
            # Create test document
            doc = Document()
            
            # Test fonts
            fonts_to_test = ["Cairo", "Noto Sans Arabic", "Noto Naskh Arabic", "Amiri"]
            
            for font_name in fonts_to_test:
                # Add paragraph with font
                paragraph = doc.add_paragraph()
                paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                run = paragraph.add_run(f"اختبار خط {font_name} - معرض بوخليفة للسيارات")
                run.font.name = font_name
                run.font.size = Inches(0.2)
                
                print(f"✅ {font_name} - تم إضافته للمستند")
            
            # Save test document
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
            temp_path = temp_file.name
            temp_file.close()
            
            doc.save(temp_path)
            
            # Check file size
            file_size = os.path.getsize(temp_path)
            print(f"\n📄 تم إنشاء ملف اختبار Word ({file_size:,} bytes)")
            print(f"📁 المسار: {temp_path}")
            
            # Clean up
            os.unlink(temp_path)
            
            return True
            
        except ImportError:
            print("❌ مكتبة python-docx غير مثبتة")
            print("💡 قم بتثبيتها: pip install python-docx")
            return False
        except Exception as e:
            print(f"❌ خطأ في اختبار Word: {e}")
            return False
    
    def generate_font_samples(self):
        """Generate font samples for contracts"""
        print("\n🎨 إنشاء عينات الخطوط...")
        print("=" * 30)
        
        samples_dir = Path("static/font_samples")
        samples_dir.mkdir(exist_ok=True)
        
        # Sample texts
        sample_texts = {
            "title": "عقد بيع سيارة",
            "company": "معرض بوخليفة للسيارات - قطر",
            "content": "تم الاتفاق بين الطرف الأول والطرف الثاني على بيع السيارة",
            "terms": "الشروط والأحكام",
            "signature": "التوقيعات"
        }
        
        # Create HTML sample
        html_content = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عينات الخطوط العربية للعقود</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .font-sample { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .font-name { color: #666; font-size: 14px; margin-bottom: 10px; }
        .cairo { font-family: 'Cairo', sans-serif; }
        .noto-sans { font-family: 'Noto Sans Arabic', sans-serif; }
        .noto-naskh { font-family: 'Noto Naskh Arabic', serif; }
        .amiri { font-family: 'Amiri', serif; }
        .title { font-size: 24px; font-weight: bold; }
        .company { font-size: 18px; color: #333; }
        .content { font-size: 16px; line-height: 1.6; }
        .terms { font-size: 18px; font-weight: bold; color: #444; }
        .signature { font-size: 16px; }
    </style>
</head>
<body>
    <h1>عينات الخطوط العربية للعقود</h1>
"""
        
        fonts = [
            ("cairo", "Cairo"),
            ("noto-sans", "Noto Sans Arabic"), 
            ("noto-naskh", "Noto Naskh Arabic"),
            ("amiri", "Amiri")
        ]
        
        for font_class, font_name in fonts:
            html_content += f"""
    <div class="font-sample {font_class}">
        <div class="font-name">خط {font_name}</div>
        <div class="title">{sample_texts['title']}</div>
        <div class="company">{sample_texts['company']}</div>
        <div class="content">{sample_texts['content']}</div>
        <div class="terms">{sample_texts['terms']}</div>
        <div class="signature">{sample_texts['signature']}</div>
    </div>
"""
        
        html_content += """
</body>
</html>"""
        
        # Save HTML sample
        sample_file = samples_dir / "font_samples.html"
        with open(sample_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ تم إنشاء عينات HTML: {sample_file}")
        
        return sample_file
    
    def create_font_guide(self):
        """Create font usage guide for contracts"""
        guide_content = """# دليل استخدام الخطوط في العقود

## الخطوط المتاحة

### 1. Cairo (الخط الأساسي)
- **الاستخدام:** العناوين والنصوص الحديثة
- **المميزات:** واضح، عصري، متعدد الأوزان
- **الحجم المقترح:** 16-24px للعناوين، 12-16px للنصوص

### 2. Noto Sans Arabic
- **الاستخدام:** النصوص العامة والمحتوى الطويل
- **المميزات:** قراءة ممتازة، دعم شامل للعربية
- **الحجم المقترح:** 14-18px للنصوص العادية

### 3. Noto Naskh Arabic
- **الاستخدام:** النصوص التقليدية والرسمية
- **المميزات:** طابع تقليدي، مناسب للوثائق الرسمية
- **الحجم المقترح:** 14-16px للنصوص التقليدية

### 4. Amiri
- **الاستخدام:** النصوص الكلاسيكية والعناوين المميزة
- **المميزات:** جمالية عالية، طابع تراثي
- **الحجم المقترح:** 16-20px للعناوين الخاصة

## إرشادات الاستخدام

### في PDF:
```python
# تسجيل الخط
pdfmetrics.registerFont(TTFont('Cairo', 'static/fonts/Cairo[slnt,wght].ttf'))

# استخدام الخط
c.setFont('Cairo', 16)
c.drawRightString(x, y, text)
```

### في Word:
```python
# تطبيق الخط
run.font.name = 'Cairo'
run.font.size = Inches(0.2)
```

## أفضل الممارسات

1. **استخدم Cairo للعناوين الرئيسية**
2. **استخدم Noto Sans Arabic للنصوص العامة**
3. **استخدم Noto Naskh للشروط والأحكام**
4. **استخدم Amiri للتوقيعات والعناوين المميزة**

## حل المشاكل الشائعة

### مشكلة: الخط لا يظهر في PDF
- تأكد من تسجيل الخط قبل الاستخدام
- تحقق من مسار ملف الخط
- استخدم prepare_arabic_text() للنصوص العربية

### مشكلة: الخط لا يظهر في Word
- تأكد من تثبيت الخط في النظام
- استخدم الاسم الصحيح للخط
- تحقق من ترميز النص (UTF-8)

## الدعم الفني

للمساعدة في مشاكل الخطوط:
1. راجع هذا الدليل
2. استخدم enhance_contracts_fonts.py للاختبار
3. تحقق من سجلات الأخطاء
"""
        
        guide_file = Path("CONTRACTS_FONTS_GUIDE.md")
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"✅ تم إنشاء دليل الخطوط: {guide_file}")
        return guide_file

def main():
    """Main function"""
    print("🎨 تحسين خطوط العقود - معرض السيارات قطر")
    print("=" * 50)
    
    enhancer = ContractFontEnhancer()
    
    print("1. فحص الخطوط المتاحة")
    print("2. اختبار خطوط PDF")
    print("3. اختبار خطوط Word")
    print("4. إنشاء عينات الخطوط")
    print("5. إنشاء دليل الخطوط")
    print("6. تشغيل جميع الاختبارات")
    
    choice = input("\nاختر العملية (1-6): ").strip()
    
    if choice == "1":
        enhancer.check_font_availability()
    elif choice == "2":
        enhancer.test_pdf_fonts()
    elif choice == "3":
        enhancer.test_word_fonts()
    elif choice == "4":
        enhancer.generate_font_samples()
    elif choice == "5":
        enhancer.create_font_guide()
    elif choice == "6":
        print("🚀 تشغيل جميع الاختبارات...")
        enhancer.check_font_availability()
        enhancer.test_pdf_fonts()
        enhancer.test_word_fonts()
        enhancer.generate_font_samples()
        enhancer.create_font_guide()
        print("\n🎉 تم إكمال جميع الاختبارات!")
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
