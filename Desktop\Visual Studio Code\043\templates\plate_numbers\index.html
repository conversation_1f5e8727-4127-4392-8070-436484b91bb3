{% extends "base.html" %}

{% block title %}أرقام السيارات{% endblock %}

{% block extra_css %}
<style>
.plate-number-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #dee2e6;
}

.plate-number-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.plate-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.5rem;
    margin-bottom: 15px;
    position: relative;
}

.plate-display.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.plate-display.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.plate-display.auction {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(67, 233, 123, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(67, 233, 123, 0); }
    100% { box-shadow: 0 0 0 0 rgba(67, 233, 123, 0); }
}

.status-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 0.7rem;
    padding: 2px 6px;
}

.price-tag {
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    display: inline-block;
    margin-top: 10px;
}

.auction-timer {
    background: #dc3545;
    color: white;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    margin-top: 5px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.filter-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-id-card me-2"></i>أرقام السيارات</h2>
            <p class="text-muted">إدارة وبيع أرقام لوحات السيارات في قطر</p>
        </div>
        <div>
            <a href="{{ url_for('plate_numbers.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة رقم جديد
            </a>
            <a href="{{ url_for('plate_numbers.auctions') }}" class="btn btn-success">
                <i class="fas fa-gavel me-2"></i>المزايدات
            </a>
            <a href="{{ url_for('plate_numbers.search') }}" class="btn btn-info">
                <i class="fas fa-search me-2"></i>البحث المتقدم
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h3>{{ stats.total }}</h3>
                <p class="mb-0">إجمالي الأرقام</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <h3>{{ stats.available }}</h3>
                <p class="mb-0">متاحة للبيع</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <h3>{{ stats.sold }}</h3>
                <p class="mb-0">مباعة</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <h3>{{ stats.auction }}</h3>
                <p class="mb-0">في المزايدة</p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" value="{{ search }}" placeholder="رقم اللوحة أو الوصف">
            </div>
            <div class="col-md-2">
                <label class="form-label">الفئة</label>
                <select class="form-select" name="category">
                    <option value="">جميع الفئات</option>
                    <option value="vip" {{ 'selected' if category == 'vip' }}>VIP</option>
                    <option value="special" {{ 'selected' if category == 'special' }}>مميزة</option>
                    <option value="regular" {{ 'selected' if category == 'regular' }}>عادية</option>
                    <option value="custom" {{ 'selected' if category == 'custom' }}>مخصصة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="available" {{ 'selected' if status == 'available' }}>متاحة</option>
                    <option value="sold" {{ 'selected' if status == 'sold' }}>مباعة</option>
                    <option value="reserved" {{ 'selected' if status == 'reserved' }}>محجوزة</option>
                    <option value="blocked" {{ 'selected' if status == 'blocked' }}>محظورة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">ترتيب حسب</label>
                <select class="form-select" name="sort_by">
                    <option value="created_at" {{ 'selected' if sort_by == 'created_at' }}>تاريخ الإضافة</option>
                    <option value="price" {{ 'selected' if sort_by == 'price' }}>السعر</option>
                    <option value="number" {{ 'selected' if sort_by == 'number' }}>الرقم</option>
                    <option value="digits_count" {{ 'selected' if sort_by == 'digits_count' }}>عدد الأرقام</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">الاتجاه</label>
                <select class="form-select" name="sort_order">
                    <option value="desc" {{ 'selected' if sort_order == 'desc' }}>تنازلي</option>
                    <option value="asc" {{ 'selected' if sort_order == 'asc' }}>تصاعدي</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>تصفية
                    </button>
                    <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Plate Numbers Grid -->
    <div class="row">
        {% for plate in plate_numbers.items %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card plate-number-card h-100">
                <div class="card-body">
                    <!-- Plate Display -->
                    <div class="plate-display {{ plate.category }} {{ 'auction' if plate.is_auction and plate.status == 'available' }}">
                        {{ plate.number }}
                        <span class="status-badge badge bg-{{ 'success' if plate.status == 'available' else 'danger' if plate.status == 'sold' else 'warning' }}">
                            {% if plate.status == 'available' %}متاحة
                            {% elif plate.status == 'sold' %}مباعة
                            {% elif plate.status == 'reserved' %}محجوزة
                            {% else %}محظورة{% endif %}
                        </span>
                    </div>

                    <!-- Details -->
                    <div class="mb-2">
                        <small class="text-muted">الفئة:</small>
                        <span class="badge bg-{{ 'danger' if plate.category == 'vip' else 'info' if plate.category == 'special' else 'secondary' }}">
                            {% if plate.category == 'vip' %}VIP
                            {% elif plate.category == 'special' %}مميزة
                            {% elif plate.category == 'regular' %}عادية
                            {% else %}مخصصة{% endif %}
                        </span>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">النوع:</small>
                        <span>
                            {% if plate.type == 'private' %}خاصة
                            {% elif plate.type == 'taxi' %}تاكسي
                            {% elif plate.type == 'transport' %}نقل
                            {% else %}حكومية{% endif %}
                        </span>
                    </div>

                    {% if plate.code_letter %}
                    <div class="mb-2">
                        <small class="text-muted">الحرف:</small>
                        <span class="badge bg-primary">{{ plate.code_letter }}</span>
                    </div>
                    {% endif %}

                    <div class="mb-2">
                        <small class="text-muted">عدد الأرقام:</small>
                        <span class="badge bg-info">{{ plate.digits_count }}</span>
                    </div>

                    <!-- Special Features -->
                    {% if plate.is_sequential or plate.is_repeated or plate.is_mirror or plate.is_lucky %}
                    <div class="mb-2">
                        <small class="text-muted">مميزات خاصة:</small><br>
                        {% if plate.is_sequential %}<span class="badge bg-success me-1">متسلسل</span>{% endif %}
                        {% if plate.is_repeated %}<span class="badge bg-warning me-1">مكرر</span>{% endif %}
                        {% if plate.is_mirror %}<span class="badge bg-info me-1">مرآة</span>{% endif %}
                        {% if plate.is_lucky %}<span class="badge bg-primary me-1">محظوظ</span>{% endif %}
                    </div>
                    {% endif %}

                    <!-- Price -->
                    {% if plate.is_auction and plate.status == 'available' %}
                        <div class="price-tag bg-success">
                            المزايدة الحالية: {{ "{:,.0f}".format(plate.current_bid or plate.starting_bid) }} ريال
                        </div>
                        {% if plate.auction_end_date %}
                        <div class="auction-timer">
                            <i class="fas fa-clock me-1"></i>
                            ينتهي: {{ plate.auction_end_date.strftime('%Y-%m-%d %H:%M') }}
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="price-tag">
                            {{ "{:,.0f}".format(plate.price) }} ريال قطري
                        </div>
                    {% endif %}

                    <!-- Description -->
                    {% if plate.description %}
                    <p class="text-muted small mt-2 mb-2">{{ plate.description[:100] }}{% if plate.description|length > 100 %}...{% endif %}</p>
                    {% endif %}
                </div>

                <!-- Actions -->
                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100" role="group">
                        <a href="{{ url_for('plate_numbers.view', id=plate.id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% if current_user.has_permission('edit_all') %}
                        <a href="{{ url_for('plate_numbers.edit', id=plate.id) }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% endif %}
                        {% if plate.status == 'available' %}
                            {% if plate.is_auction %}
                            <button class="btn btn-outline-success btn-sm" onclick="showBidModal({{ plate.id }}, '{{ plate.number }}', {{ plate.current_bid or plate.starting_bid }})">
                                <i class="fas fa-gavel"></i> مزايدة
                            </button>
                            {% else %}
                            <a href="{{ url_for('plate_numbers.sell', id=plate.id) }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-handshake"></i> بيع
                            </a>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if plate_numbers.pages > 1 %}
    <nav aria-label="تصفح الصفحات">
        <ul class="pagination justify-content-center">
            {% if plate_numbers.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('plate_numbers.index', page=plate_numbers.prev_num, search=search, category=category, status=status, sort_by=sort_by, sort_order=sort_order) }}">السابق</a>
            </li>
            {% endif %}
            
            {% for page_num in plate_numbers.iter_pages() %}
                {% if page_num %}
                    {% if page_num != plate_numbers.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('plate_numbers.index', page=page_num, search=search, category=category, status=status, sort_by=sort_by, sort_order=sort_order) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if plate_numbers.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('plate_numbers.index', page=plate_numbers.next_num, search=search, category=category, status=status, sort_by=sort_by, sort_order=sort_order) }}">التالي</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Bid Modal -->
<div class="modal fade" id="bidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">مزايدة على رقم اللوحة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bidForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">رقم اللوحة</label>
                        <input type="text" class="form-control" id="plateNumber" readonly>
                        <input type="hidden" id="plateId">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المزايدة الحالية</label>
                        <input type="text" class="form-control" id="currentBid" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                        <select class="form-select" id="customer_id" name="customer_id" required>
                            <option value="">اختر العميل</option>
                            <!-- Will be populated via AJAX -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="bid_amount" class="form-label">مبلغ المزايدة <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="bid_amount" name="bid_amount" required min="0" step="100">
                        <div class="form-text">يجب أن يكون أعلى من المزايدة الحالية</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تأكيد المزايدة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showBidModal(plateId, plateNumber, currentBid) {
    $('#plateId').val(plateId);
    $('#plateNumber').val(plateNumber);
    $('#currentBid').val(currentBid.toLocaleString() + ' ريال');
    $('#bid_amount').attr('min', currentBid + 100);
    $('#bidModal').modal('show');
    
    // Load customers if not already loaded
    if ($('#customer_id option').length <= 1) {
        loadCustomers();
    }
}

function loadCustomers() {
    $.get('/customers/api/list', function(data) {
        const select = $('#customer_id');
        select.empty().append('<option value="">اختر العميل</option>');
        data.forEach(customer => {
            select.append(`<option value="${customer.id}">${customer.full_name} - ${customer.phone}</option>`);
        });
    });
}

$('#bidForm').on('submit', function(e) {
    e.preventDefault();
    
    const plateId = $('#plateId').val();
    const formData = new FormData(this);
    
    $.post(`/plate-numbers/${plateId}/bid`, formData)
        .done(function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                $('#bidModal').modal('hide');
                location.reload();
            } else {
                showNotification(response.message, 'error');
            }
        })
        .fail(function() {
            showNotification('حدث خطأ أثناء تسجيل المزايدة', 'error');
        });
});
</script>
{% endblock %}
