#!/bin/bash

echo "Starting Car Dealership System..."
echo ""

# Set environment variables
export FLASK_APP=app.py
export FLASK_ENV=development
export FLASK_DEBUG=1

echo "Environment variables set:"
echo "FLASK_APP=$FLASK_APP"
echo "FLASK_ENV=$FLASK_ENV"
echo "FLASK_DEBUG=$FLASK_DEBUG"
echo ""

echo "Starting Flask server..."
echo "Server will be available at: http://localhost:5000"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Try different Python commands
python3 -c "from app import create_app; app = create_app(); app.run(debug=True, host='0.0.0.0', port=5000)" 2>/dev/null

if [ $? -ne 0 ]; then
    echo ""
    echo "Failed to start with python3 command, trying python..."
    python -c "from app import create_app; app = create_app(); app.run(debug=True, host='0.0.0.0', port=5000)" 2>/dev/null
fi

if [ $? -ne 0 ]; then
    echo ""
    echo "Failed to start with python command, trying flask run..."
    flask run --host=0.0.0.0 --port=5000 --debug 2>/dev/null
fi

if [ $? -ne 0 ]; then
    echo ""
    echo "All startup methods failed. Please check:"
    echo "1. Python is installed and in PATH"
    echo "2. Required packages are installed: pip install -r requirements.txt"
    echo "3. No syntax errors in the code"
    echo ""
    read -p "Press Enter to continue..."
fi
