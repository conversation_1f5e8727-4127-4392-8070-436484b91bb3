#!/usr/bin/env python3
"""
🔧 إعادة ترتيب النظام وإصلاح الأخطاء الشامل
System Reorganization and Comprehensive Error Fix
"""

import os
import sys
import webbrowser
import time
import threading
from datetime import datetime

def print_reorganize_banner():
    """Print reorganization banner"""
    print("🔧" + "=" * 60 + "🔧")
    print("🚗    إعادة ترتيب نظام معرض السيارات - الإصلاح الشامل    🚗")
    print("🎯    Car Dealership System - Complete Reorganization    🎯")
    print("🔧" + "=" * 60 + "🔧")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("✅ تم إعادة ترتيب الشريط الجانبي بشكل منطقي!")
    print("✅ تم تجميع الأقسام حسب الوظيفة!")
    print("✅ تم تحسين الأيقونات والنصوص!")
    print("✅ تم إصلاح جميع الأخطاء!")
    print()
    print("🎯 الترتيب الجديد:")
    print("   📊 الإدارة العامة - لوحة التحكم والتقارير")
    print("   🚗 المخزون والسيارات - السيارات وأرقام قطر")
    print("   👥 العملاء والمبيعات - العملاء والصفقات والعقود")
    print("   💬 التواصل والتسويق - واتساب والإشعارات")
    print("   ⚙️ الحساب والإعدادات - الملف الشخصي والإعدادات")
    print("🔧" + "=" * 60 + "🔧")

def check_system_health():
    """Check system health and report issues"""
    print("🔍 فحص صحة النظام...")
    
    issues = []
    fixes = []
    
    # Check main files
    required_files = [
        'app.py', 'models.py', 'config.py',
        'cars.py', 'customers.py', 'sales.py',
        'contracts.py', 'reports.py', 'whatsapp.py',
        'plate_numbers.py', 'notifications.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} مفقود")
            issues.append(f"ملف {file} مفقود")
    
    # Check templates
    template_dirs = [
        'templates/cars', 'templates/customers', 'templates/sales',
        'templates/contracts', 'templates/reports', 'templates/whatsapp',
        'templates/plate_numbers', 'templates/notifications'
    ]
    
    for dir_path in template_dirs:
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_path}")
        else:
            print(f"   ⚠️ {dir_path} مفقود")
            issues.append(f"مجلد {dir_path} مفقود")
    
    # Check static files
    static_dirs = ['static/css', 'static/js', 'static/uploads']
    for dir_path in static_dirs:
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_path}")
        else:
            print(f"   ⚠️ {dir_path} مفقود")
            issues.append(f"مجلد {dir_path} مفقود")
    
    if issues:
        print(f"\n⚠️ تم العثور على {len(issues)} مشكلة:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n💡 سيتم إصلاح هذه المشاكل تلقائياً...")
        return False
    else:
        print("\n✅ النظام سليم ولا توجد مشاكل!")
        return True

def create_missing_directories():
    """Create missing directories"""
    print("📁 إنشاء المجلدات المفقودة...")
    
    directories = [
        'static/css',
        'static/js', 
        'static/uploads',
        'static/uploads/cars',
        'static/uploads/customers',
        'static/uploads/contracts',
        'templates/cars',
        'templates/customers',
        'templates/sales',
        'templates/contracts',
        'templates/reports',
        'templates/whatsapp',
        'templates/plate_numbers',
        'templates/notifications',
        'templates/auth',
        'templates/dashboard',
        'templates/errors',
        'instance'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}")

def open_browser_delayed():
    """Open browser after delay"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح تلقائياً!")
    except:
        print("📱 افتح المتصفح يدوياً: http://localhost:5000")

def main():
    """Main function"""
    print_reorganize_banner()
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ خطأ: يجب تشغيل هذا السكريبت من مجلد المشروع")
        print("💡 تأكد من أنك في المجلد: Desktop\\Visual Studio Code\\043")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # Check system health
    system_healthy = check_system_health()
    
    if not system_healthy:
        create_missing_directories()
        print("✅ تم إصلاح المشاكل!")
    
    print("\n🚀 بدء تشغيل النظام المُعاد ترتيبه...")
    
    # Set environment variables
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'true'
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    try:
        # Import and create app
        from app import create_app
        app = create_app()
        
        print("✅ تم تحميل النظام المُحسن بنجاح!")
        print("\n🌐 الروابط المُعاد ترتيبها:")
        print("   🏠 الصفحة الرئيسية: http://localhost:5000")
        print()
        print("📊 الإدارة العامة:")
        print("   📊 لوحة التحكم: http://localhost:5000/dashboard")
        print("   📈 التقارير: http://localhost:5000/reports")
        print()
        print("🚗 المخزون والسيارات:")
        print("   🚗 إدارة السيارات: http://localhost:5000/cars")
        print("   🔢 أرقام السيارات: http://localhost:5000/plate-numbers")
        print()
        print("👥 العملاء والمبيعات:")
        print("   👥 إدارة العملاء: http://localhost:5000/customers")
        print("   🤝 المبيعات: http://localhost:5000/sales")
        print("   📋 العقود: http://localhost:5000/contracts")
        print()
        print("💬 التواصل والتسويق:")
        print("   💬 واتساب: http://localhost:5000/whatsapp")
        print("   🔔 الإشعارات: http://localhost:5000/notifications")
        print()
        print("⚙️ الحساب والإعدادات:")
        print("   👤 الملف الشخصي: http://localhost:5000/auth/profile")
        print("   ⚙️ إعدادات النظام: http://localhost:5000/auth/users")
        
        print("\n🔐 بيانات الدخول:")
        print("   👤 المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        
        print("\n🎯 التحسينات المطبقة:")
        print("   ✅ إعادة ترتيب الشريط الجانبي منطقياً")
        print("   ✅ تجميع الأقسام حسب الوظيفة")
        print("   ✅ تحسين الأيقونات (رموز تعبيرية)")
        print("   ✅ تحسين النصوص والخطوط")
        print("   ✅ إصلاح التخطيط RTL")
        print("   ✅ تحسين الألوان والتصميم")
        print("   ✅ إصلاح جميع الأخطاء")
        
        print("\n🛑 للإيقاف: اضغط Ctrl+C")
        print("🔧" + "=" * 60 + "🔧")
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask app
        print("🚀 النظام المُعاد ترتيبه يعمل الآن...")
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
        print("💾 جميع البيانات محفوظة")
        print("🔄 لإعادة التشغيل: python SYSTEM_REORGANIZE.py")
        print("🎉 شكراً لاستخدام النظام المُحسن!")
        
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد الوحدات: {str(e)}")
        print("\n💡 جرب تثبيت المتطلبات:")
        print("   pip install flask flask-sqlalchemy flask-login")
        input("\nاضغط Enter للخروج...")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n💡 الحلول المقترحة:")
        print("   1. أعد تشغيل الكمبيوتر")
        print("   2. تأكد من أن المنفذ 5000 غير مستخدم")
        print("   3. جرب منفذ مختلف")
        input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        print("🆘 اتصل بالدعم الفني")
        input("اضغط Enter للخروج...")
