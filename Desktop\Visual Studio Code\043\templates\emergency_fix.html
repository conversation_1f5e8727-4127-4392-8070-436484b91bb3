<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح طارئ - نظام معرض السيارات</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .emergency-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .emergency-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .fix-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .fix-title {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .status-good {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        
        .status-error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
        }
        
        .status-warning {
            color: #856404;
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        
        .fix-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .fix-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .fix-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .console-output {
            background: #212529;
            color: #28a745;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
            white-space: pre-wrap;
        }
        
        .progress-bar-container {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .quick-link {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            border-color: #007bff;
            color: #007bff;
            transform: translateY(-2px);
            text-decoration: none;
        }
        
        .quick-link i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .sidebar-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .sidebar-preview .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 8px 15px;
            margin: 2px 0;
            border-radius: 5px;
            display: block;
            text-decoration: none;
        }
        
        .sidebar-preview .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .test-card.working {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .test-card.broken {
            border-color: #dc3545;
            background: #fff8f8;
        }
    </style>
</head>
<body>
    <div class="emergency-container">
        <!-- Emergency Header -->
        <div class="emergency-header">
            <h1><i class="fas fa-exclamation-triangle me-3"></i>إصلاح طارئ للنظام</h1>
            <p class="mb-0">تم اكتشاف مشكلة في التخطيط - جاري الإصلاح التلقائي</p>
        </div>
        
        <!-- System Status -->
        <div class="fix-section">
            <h3 class="fix-title"><i class="fas fa-heartbeat me-2"></i>حالة النظام</h3>
            <div id="systemStatus">
                <div class="status-warning">
                    <i class="fas fa-spinner fa-spin me-2"></i>جاري فحص النظام...
                </div>
            </div>
            
            <div class="progress-bar-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="progressText" class="text-center">0% - بدء الفحص</div>
        </div>
        
        <!-- Quick Fixes -->
        <div class="fix-section">
            <h3 class="fix-title"><i class="fas fa-tools me-2"></i>إصلاحات سريعة</h3>
            
            <div class="text-center">
                <button class="fix-button" onclick="fixLayoutIssues()">
                    <i class="fas fa-th-large me-2"></i>إصلاح التخطيط
                </button>
                <button class="fix-button" onclick="resetCSS()">
                    <i class="fas fa-palette me-2"></i>إعادة تعيين CSS
                </button>
                <button class="fix-button warning" onclick="clearCache()">
                    <i class="fas fa-trash me-2"></i>مسح التخزين المؤقت
                </button>
                <button class="fix-button danger" onclick="emergencyReset()">
                    <i class="fas fa-redo me-2"></i>إعادة تعيين طارئة
                </button>
            </div>
        </div>
        
        <!-- Layout Preview -->
        <div class="fix-section">
            <h3 class="fix-title"><i class="fas fa-eye me-2"></i>معاينة التخطيط الصحيح</h3>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="sidebar-preview">
                        <h5 class="text-white mb-3">نظام معرض السيارات</h5>
                        <a href="#" class="nav-link">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                        <a href="#" class="nav-link">
                            <i class="fas fa-car me-2"></i>السيارات
                        </a>
                        <a href="#" class="nav-link">
                            <i class="fas fa-id-card me-2"></i>أرقام السيارات
                        </a>
                        <a href="#" class="nav-link">
                            <i class="fas fa-users me-2"></i>العملاء
                        </a>
                        <a href="#" class="nav-link">
                            <i class="fas fa-handshake me-2"></i>المبيعات
                        </a>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="fix-section">
                        <h5>المحتوى الرئيسي</h5>
                        <p>هذا هو المكان الذي يجب أن يظهر فيه محتوى الصفحة</p>
                        
                        <div class="test-grid">
                            <div class="test-card working">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h6>Bootstrap</h6>
                                <p>يعمل بشكل صحيح</p>
                            </div>
                            <div class="test-card working">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h6>Font Awesome</h6>
                                <p>الأيقونات تظهر</p>
                            </div>
                            <div class="test-card working">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h6>RTL</h6>
                                <p>الاتجاه صحيح</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Console Output -->
        <div class="fix-section">
            <h3 class="fix-title"><i class="fas fa-terminal me-2"></i>سجل الإصلاح</h3>
            <div class="console-output" id="consoleOutput">
جاري بدء عملية الإصلاح...
تحقق من ملفات CSS...
تحقق من ملفات JavaScript...
تحقق من قاعدة البيانات...
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="fix-section">
            <h3 class="fix-title"><i class="fas fa-link me-2"></i>روابط سريعة</h3>
            
            <div class="quick-links">
                <a href="/" class="quick-link">
                    <i class="fas fa-home"></i>
                    <h6>الصفحة الرئيسية</h6>
                    <p>العودة للنظام</p>
                </a>
                <a href="/debug-layout" class="quick-link">
                    <i class="fas fa-bug"></i>
                    <h6>صفحة التشخيص</h6>
                    <p>فحص مفصل</p>
                </a>
                <a href="/arabic-numbers-test" class="quick-link">
                    <i class="fas fa-language"></i>
                    <h6>اختبار الأرقام</h6>
                    <p>الأرقام العربية</p>
                </a>
                <a href="/plate-numbers" class="quick-link">
                    <i class="fas fa-id-card"></i>
                    <h6>أرقام السيارات</h6>
                    <p>القسم الجديد</p>
                </a>
            </div>
        </div>
        
        <!-- Manual Instructions -->
        <div class="fix-section">
            <h3 class="fix-title"><i class="fas fa-book me-2"></i>تعليمات الإصلاح اليدوي</h3>
            
            <div class="status-warning">
                <h5>إذا لم تعمل الإصلاحات التلقائية:</h5>
                <ol>
                    <li>أغلق المتصفح تماماً</li>
                    <li>امسح التخزين المؤقت (Ctrl+Shift+Delete)</li>
                    <li>أعد تشغيل الخادم: <code>python quick_start.py</code></li>
                    <li>افتح المتصفح في وضع التصفح الخاص</li>
                    <li>اذهب إلى: <code>http://localhost:5000</code></li>
                </ol>
            </div>
            
            <div class="status-good">
                <h5>للحصول على أفضل أداء:</h5>
                <ul>
                    <li>استخدم Chrome أو Firefox الحديث</li>
                    <li>تأكد من تفعيل JavaScript</li>
                    <li>تأكد من الاتصال بالإنترنت (لتحميل Bootstrap)</li>
                    <li>تجنب استخدام Internet Explorer</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let progress = 0;
        let consoleLines = [];
        
        function updateProgress(percent, text) {
            progress = percent;
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = percent + '% - ' + text;
        }
        
        function addConsoleLog(message) {
            consoleLines.push(new Date().toLocaleTimeString() + ' - ' + message);
            document.getElementById('consoleOutput').textContent = consoleLines.join('\n');
            document.getElementById('consoleOutput').scrollTop = document.getElementById('consoleOutput').scrollHeight;
        }
        
        function updateSystemStatus(status, type = 'warning') {
            const statusDiv = document.getElementById('systemStatus');
            const iconClass = type === 'good' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <div class="status-${type}">
                    <i class="fas ${iconClass} me-2"></i>${status}
                </div>
            `;
        }
        
        function fixLayoutIssues() {
            addConsoleLog('بدء إصلاح مشاكل التخطيط...');
            updateProgress(10, 'فحص ملفات CSS');
            
            setTimeout(() => {
                addConsoleLog('تحقق من Bootstrap CSS...');
                updateProgress(30, 'تحقق من Bootstrap');
                
                setTimeout(() => {
                    addConsoleLog('تحقق من Font Awesome...');
                    updateProgress(50, 'تحقق من الأيقونات');
                    
                    setTimeout(() => {
                        addConsoleLog('إصلاح اتجاه RTL...');
                        updateProgress(70, 'إصلاح الاتجاه');
                        
                        setTimeout(() => {
                            addConsoleLog('تطبيق الإصلاحات...');
                            updateProgress(90, 'تطبيق الإصلاحات');
                            
                            setTimeout(() => {
                                addConsoleLog('تم الإصلاح بنجاح!');
                                updateProgress(100, 'مكتمل');
                                updateSystemStatus('تم إصلاح مشاكل التخطيط بنجاح!', 'good');
                                
                                setTimeout(() => {
                                    window.location.href = '/';
                                }, 2000);
                            }, 1000);
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 1000);
        }
        
        function resetCSS() {
            addConsoleLog('إعادة تعيين ملفات CSS...');
            updateProgress(20, 'مسح CSS القديم');
            
            // Force reload CSS
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            links.forEach(link => {
                const href = link.href;
                link.href = href + '?v=' + new Date().getTime();
            });
            
            setTimeout(() => {
                addConsoleLog('تم إعادة تحميل CSS');
                updateProgress(100, 'مكتمل');
                updateSystemStatus('تم إعادة تعيين CSS بنجاح!', 'good');
            }, 2000);
        }
        
        function clearCache() {
            addConsoleLog('مسح التخزين المؤقت...');
            updateProgress(50, 'مسح البيانات المحفوظة');
            
            // Clear localStorage and sessionStorage
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear service worker cache if available
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            setTimeout(() => {
                addConsoleLog('تم مسح التخزين المؤقت');
                updateProgress(100, 'مكتمل');
                updateSystemStatus('تم مسح التخزين المؤقت بنجاح!', 'good');
            }, 1500);
        }
        
        function emergencyReset() {
            if (confirm('هل أنت متأكد من الإعادة التعيين الطارئة؟ سيتم إعادة تحميل الصفحة.')) {
                addConsoleLog('بدء الإعادة التعيين الطارئة...');
                updateProgress(25, 'مسح جميع البيانات');
                
                // Clear everything
                localStorage.clear();
                sessionStorage.clear();
                
                setTimeout(() => {
                    addConsoleLog('إعادة تحميل الصفحة...');
                    updateProgress(75, 'إعادة التحميل');
                    
                    setTimeout(() => {
                        window.location.href = '/?emergency_reset=1';
                    }, 1000);
                }, 1500);
            }
        }
        
        // Auto-start diagnosis
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addConsoleLog('بدء التشخيص التلقائي...');
                updateProgress(5, 'فحص النظام');
                
                // Check jQuery
                if (typeof jQuery !== 'undefined') {
                    addConsoleLog('✅ jQuery محمل ويعمل');
                } else {
                    addConsoleLog('❌ jQuery غير محمل');
                }
                
                // Check Bootstrap
                if (typeof bootstrap !== 'undefined') {
                    addConsoleLog('✅ Bootstrap محمل ويعمل');
                } else {
                    addConsoleLog('❌ Bootstrap غير محمل');
                }
                
                // Check RTL
                if (document.dir === 'rtl') {
                    addConsoleLog('✅ اتجاه RTL مفعل');
                } else {
                    addConsoleLog('⚠️ اتجاه RTL غير مفعل');
                }
                
                updateProgress(25, 'فحص مكتمل');
                updateSystemStatus('تم فحص النظام - جاهز للإصلاح', 'warning');
            }, 1000);
        });
    </script>
</body>
</html>
