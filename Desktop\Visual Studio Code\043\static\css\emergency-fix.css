/* Emergency CSS Fix for Layout Issues */

/* Critical Reset */
* {
    box-sizing: border-box !important;
}

html {
    direction: rtl !important;
    text-align: right !important;
    font-size: 16px !important;
}

body {
    font-family: 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
    background-color: #f8f9fa !important;
    direction: rtl !important;
    text-align: right !important;
    line-height: 1.6 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden !important;
    min-height: 100vh !important;
}

/* Container Fix */
.container,
.container-fluid {
    max-width: 100% !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-right: auto !important;
    margin-left: auto !important;
    direction: rtl !important;
}

/* Row and Column Fix */
.row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin-right: -15px !important;
    margin-left: -15px !important;
    direction: rtl !important;
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-auto,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    position: relative !important;
    width: 100% !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
    direction: rtl !important;
}

/* Sidebar Fix */
.sidebar {
    background: #343a40 !important;
    min-height: 100vh !important;
    padding: 20px 0 !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    width: 250px !important;
    z-index: 1000 !important;
    overflow-y: auto !important;
    direction: rtl !important;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8) !important;
    padding: 10px 20px !important;
    border-radius: 5px !important;
    margin: 2px 10px !important;
    display: block !important;
    text-decoration: none !important;
    direction: rtl !important;
    text-align: right !important;
}

.sidebar .nav-link:hover {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
}

.sidebar .nav-link.active {
    background: #007bff !important;
    color: white !important;
}

/* Main Content Fix */
.main-content {
    margin-right: 250px !important;
    padding: 20px !important;
    min-height: 100vh !important;
    direction: rtl !important;
    text-align: right !important;
}

/* Card Fix */
.card {
    border-radius: 10px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    margin-bottom: 20px !important;
    background: white !important;
    border: 1px solid #dee2e6 !important;
    direction: rtl !important;
}

.card-header {
    background: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 15px 20px !important;
    direction: rtl !important;
    text-align: right !important;
}

.card-body {
    padding: 20px !important;
    direction: rtl !important;
    text-align: right !important;
}

.card-footer {
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
    padding: 15px 20px !important;
    direction: rtl !important;
    text-align: right !important;
}

/* Button Fix */
.btn {
    border-radius: 8px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    direction: rtl !important;
}

/* Form Fix */
.form-control {
    border-radius: 8px !important;
    border: 1px solid #ced4da !important;
    padding: 8px 12px !important;
    direction: rtl !important;
    text-align: right !important;
    width: 100% !important;
}

.form-label {
    direction: rtl !important;
    text-align: right !important;
    margin-bottom: 5px !important;
    font-weight: 500 !important;
}

.form-select {
    border-radius: 8px !important;
    border: 1px solid #ced4da !important;
    padding: 8px 12px !important;
    direction: rtl !important;
    text-align: right !important;
    width: 100% !important;
}

/* Table Fix */
.table {
    border-radius: 8px !important;
    overflow: hidden !important;
    direction: rtl !important;
    text-align: right !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
    background-color: transparent !important;
}

.table th,
.table td {
    padding: 12px !important;
    vertical-align: top !important;
    border-top: 1px solid #dee2e6 !important;
    direction: rtl !important;
    text-align: right !important;
}

.table thead th {
    vertical-align: bottom !important;
    border-bottom: 2px solid #dee2e6 !important;
    background: #f8f9fa !important;
    font-weight: 600 !important;
}

/* Navigation Fix */
.navbar {
    direction: rtl !important;
    text-align: right !important;
}

.navbar-brand {
    direction: rtl !important;
    text-align: right !important;
}

.navbar-nav {
    direction: rtl !important;
    text-align: right !important;
}

.nav-link {
    direction: rtl !important;
    text-align: right !important;
}

/* Text Alignment Fix */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

.text-center {
    text-align: center !important;
}

/* RTL Specific Fixes */
.me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.pe-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
}

.ps-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
}

/* Responsive Fix */
@media (max-width: 768px) {
    .sidebar {
        position: relative !important;
        width: 100% !important;
        min-height: auto !important;
    }
    
    .main-content {
        margin-right: 0 !important;
        padding: 10px !important;
    }
    
    .container-fluid {
        padding: 10px !important;
    }
}

/* Emergency Override */
.emergency-override {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

/* Force RTL for all elements */
* {
    direction: inherit !important;
}

/* Ensure proper display */
.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.d-grid {
    display: grid !important;
}

/* Fix any floating issues */
.clearfix::after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}

/* Ensure proper positioning */
.position-relative {
    position: relative !important;
}

.position-absolute {
    position: absolute !important;
}

.position-fixed {
    position: fixed !important;
}

/* Final safety net */
body * {
    direction: rtl !important;
    text-align: inherit !important;
}
