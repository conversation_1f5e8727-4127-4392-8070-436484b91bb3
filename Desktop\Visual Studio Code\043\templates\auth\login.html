{% extends "base.html" %}

{% block title %}تسجيل الدخول - {{ company_name }}{% endblock %}

{% block login_content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Login form -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 400px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">{{ company_name }}</h2>
                        <p class="text-muted">تسجيل الدخول إلى النظام</p>
                    </div>
                    
                    <!-- Flash messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 py-2">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>
                    
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            للحصول على حساب جديد، يرجى الاتصال بالمدير
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right side - Branding -->
        <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white">
                <i class="fas fa-car fa-5x mb-4"></i>
                <h1 class="display-4 fw-bold mb-3">{{ company_name }}</h1>
                <p class="lead mb-4">نظام إدارة معرض السيارات</p>
                <div class="row text-center">
                    <div class="col-4">
                        <i class="fas fa-car fa-2x mb-2"></i>
                        <p>إدارة السيارات</p>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>إدارة العملاء</p>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <p>التقارير</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }
    
    .form-control {
        border-left: none;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: none;
    }
    
    .form-control:focus + .input-group-text {
        border-color: #667eea;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-focus on username field
    document.getElementById('username').focus();
    
    // Add some animation to the login form
    $(document).ready(function() {
        $('.card').hide().fadeIn(1000);
    });
</script>
{% endblock %}
