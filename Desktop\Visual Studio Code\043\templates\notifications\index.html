{% extends "base.html" %}

{% block title %}الإشعارات - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-bell me-2"></i>
        الإشعارات
        {% if unread_notifications > 0 %}
        <span class="badge bg-danger ms-2">{{ unread_notifications }}</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('notifications.mark_all_read') }}" class="btn btn-outline-primary">
                <i class="fas fa-check-double me-2"></i>
                تحديد الكل كمقروء
            </a>
            <a href="{{ url_for('notifications.delete_all_read') }}" class="btn btn-outline-danger"
               onclick="return confirm('هل أنت متأكد من حذف جميع الإشعارات المقروءة؟')">
                <i class="fas fa-trash me-2"></i>
                حذف المقروءة
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">إجمالي الإشعارات</h5>
                        <h2 class="mb-0">{{ total_notifications }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">غير مقروءة</h5>
                        <h2 class="mb-0">{{ unread_notifications }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">اليوم</h5>
                        <h2 class="mb-0">{{ today_notifications }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="type" class="form-label">نوع الإشعار</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    <option value="info" {% if filter_type == 'info' %}selected{% endif %}>معلومات</option>
                    <option value="success" {% if filter_type == 'success' %}selected{% endif %}>نجاح</option>
                    <option value="warning" {% if filter_type == 'warning' %}selected{% endif %}>تحذير</option>
                    <option value="error" {% if filter_type == 'error' %}selected{% endif %}>خطأ</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="read" class="form-label">حالة القراءة</label>
                <select class="form-select" id="read" name="read">
                    <option value="">الكل</option>
                    <option value="unread" {% if filter_read == 'unread' %}selected{% endif %}>غير مقروءة</option>
                    <option value="read" {% if filter_read == 'read' %}selected{% endif %}>مقروءة</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <div class="card-body">
        {% if notifications.items %}
        <div class="list-group list-group-flush">
            {% for notification in notifications.items %}
            <div class="list-group-item notification-item {% if not notification.is_read %}unread{% endif %}" 
                 data-notification-id="{{ notification.id }}">
                <div class="d-flex w-100 justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-{% if notification.type == 'success' %}check-circle text-success{% elif notification.type == 'warning' %}exclamation-triangle text-warning{% elif notification.type == 'error' %}times-circle text-danger{% else %}info-circle text-info{% endif %} me-2"></i>
                            <h6 class="mb-0 notification-title">{{ notification.title }}</h6>
                            {% if not notification.is_read %}
                            <span class="badge bg-primary ms-2">جديد</span>
                            {% endif %}
                        </div>
                        <p class="mb-2 notification-message">{{ notification.message }}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            {{ notification.created_at.strftime('%Y/%m/%d %H:%M') }}
                        </small>
                    </div>
                    <div class="notification-actions">
                        {% if not notification.is_read %}
                        <button class="btn btn-sm btn-outline-primary me-1" 
                                onclick="markAsRead({{ notification.id }})"
                                title="تحديد كمقروء">
                            <i class="fas fa-check"></i>
                        </button>
                        {% endif %}
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="deleteNotification({{ notification.id }})"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if notifications.pages > 1 %}
        <nav aria-label="تنقل الإشعارات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if notifications.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.index', page=notifications.prev_num, type=filter_type, read=filter_read) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in notifications.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != notifications.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('notifications.index', page=page_num, type=filter_type, read=filter_read) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if notifications.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('notifications.index', page=notifications.next_num, type=filter_type, read=filter_read) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5>لا توجد إشعارات</h5>
            <p class="text-muted">
                {% if filter_type or filter_read %}
                لا توجد إشعارات تطابق الفلتر المحدد.
                <a href="{{ url_for('notifications.index') }}">عرض جميع الإشعارات</a>
                {% else %}
                لم يتم إنشاء أي إشعارات بعد.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.notification-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.notification-item.unread:hover {
    background-color: #ffeaa7;
}

.notification-title {
    font-weight: 600;
    color: #2d3748;
}

.notification-message {
    color: #4a5568;
    line-height: 1.5;
    white-space: pre-line;
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    border-radius: 8px;
}

.badge {
    font-size: 0.75em;
}

@media (max-width: 768px) {
    .notification-actions {
        opacity: 1;
    }
    
    .btn-toolbar {
        flex-direction: column;
    }
    
    .btn-group {
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function markAsRead(notificationId) {
    fetch(`{{ url_for('notifications.mark_read', notification_id=0) }}`.replace('0', notificationId), {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationItem.classList.remove('unread');
            
            const badge = notificationItem.querySelector('.badge');
            if (badge) {
                badge.remove();
            }
            
            const markButton = notificationItem.querySelector('.btn-outline-primary');
            if (markButton) {
                markButton.remove();
            }
            
            // Update unread count
            updateUnreadCount();
            
            showAlert('تم تحديد الإشعار كمقروء', 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء تحديث الإشعار', 'danger');
    });
}

function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        return;
    }
    
    fetch(`{{ url_for('notifications.delete', notification_id=0) }}`.replace('0', notificationId), {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationItem.style.transition = 'all 0.3s ease';
            notificationItem.style.opacity = '0';
            notificationItem.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                notificationItem.remove();
                
                // Check if no notifications left
                const remainingNotifications = document.querySelectorAll('.notification-item');
                if (remainingNotifications.length === 0) {
                    location.reload();
                }
            }, 300);
            
            // Update unread count
            updateUnreadCount();
            
            showAlert('تم حذف الإشعار', 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء حذف الإشعار', 'danger');
    });
}

function updateUnreadCount() {
    fetch('{{ url_for("notifications.unread_count") }}')
    .then(response => response.json())
    .then(data => {
        // Update navbar notification badge
        const navBadge = document.querySelector('.navbar .notification-badge');
        if (navBadge) {
            if (data.count > 0) {
                navBadge.textContent = data.count;
                navBadge.style.display = 'inline';
            } else {
                navBadge.style.display = 'none';
            }
        }
        
        // Update page title badge
        const titleBadge = document.querySelector('h1 .badge');
        if (titleBadge) {
            if (data.count > 0) {
                titleBadge.textContent = data.count;
                titleBadge.style.display = 'inline';
            } else {
                titleBadge.style.display = 'none';
            }
        }
    })
    .catch(error => {
        console.error('Error updating unread count:', error);
    });
}

// Auto-refresh unread count every 30 seconds
setInterval(updateUnreadCount, 30000);
</script>
{% endblock %}
