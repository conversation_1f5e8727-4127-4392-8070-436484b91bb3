{% extends "base.html" %}

{% block title %}إدارة الواتساب - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة الواتساب</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('whatsapp.send_message') }}" class="btn btn-success">
                <i class="fab fa-whatsapp me-2"></i>
                إرسال رسالة
            </a>
            <a href="{{ url_for('whatsapp.bulk_reminders') }}" class="btn btn-outline-warning">
                <i class="fas fa-bell me-2"></i>
                تذكيرات جماعية
            </a>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('whatsapp.templates') }}" class="btn btn-outline-info">
                <i class="fas fa-file-alt me-2"></i>
                القوالب
            </a>
            <a href="{{ url_for('whatsapp.templates_enhanced') }}" class="btn btn-info">
                <i class="fas fa-magic me-2"></i>
                القوالب المحسنة
            </a>
            <div class="btn-group">
                <a href="{{ url_for('whatsapp.settings') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <a href="{{ url_for('whatsapp.settings_enhanced') }}" class="btn btn-secondary">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات محسنة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-md-12">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="sent" {{ 'selected' if status == 'sent' }}>مرسلة</option>
                    <option value="pending" {{ 'selected' if status == 'pending' }}>معلقة</option>
                    <option value="failed" {{ 'selected' if status == 'failed' }}>فشلت</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="message_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="reminder" {{ 'selected' if message_type == 'reminder' }}>تذكير</option>
                    <option value="contract" {{ 'selected' if message_type == 'contract' }}>عقد</option>
                    <option value="delay_notice" {{ 'selected' if message_type == 'delay_notice' }}>إشعار تأخير</option>
                    <option value="custom" {{ 'selected' if message_type == 'custom' }}>مخصص</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-filter me-2"></i>
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Messages Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">سجل الرسائل</h5>
    </div>
    <div class="card-body">
        {% if messages.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>رقم الهاتف</th>
                        <th>نوع الرسالة</th>
                        <th>الحالة</th>
                        <th>الرسالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for message in messages.items %}
                    <tr>
                        <td>
                            {{ message.created_at.strftime('%Y/%m/%d %H:%M') }}
                            {% if message.sent_at %}
                            <br><small class="text-muted">أرسلت: {{ message.sent_at.strftime('%H:%M') }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if message.customer %}
                            <a href="{{ url_for('customers.view', customer_id=message.customer.id) }}" class="text-decoration-none">
                                {{ message.customer.full_name }}
                            </a>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="https://wa.me/{{ message.phone_number.replace('+', '') }}" target="_blank" class="text-decoration-none">
                                {{ message.phone_number }}
                            </a>
                        </td>
                        <td>
                            {% if message.message_type == 'reminder' %}
                                <span class="badge bg-warning">تذكير</span>
                            {% elif message.message_type == 'contract' %}
                                <span class="badge bg-info">عقد</span>
                            {% elif message.message_type == 'delay_notice' %}
                                <span class="badge bg-danger">إشعار تأخير</span>
                            {% else %}
                                <span class="badge bg-secondary">مخصص</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if message.status == 'sent' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    مرسلة
                                </span>
                            {% elif message.status == 'pending' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock"></i>
                                    معلقة
                                </span>
                            {% else %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times"></i>
                                    فشلت
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="message-preview" style="max-width: 300px;">
                                {{ message.message[:100] }}
                                {% if message.message|length > 100 %}...{% endif %}
                            </div>
                            {% if message.error_message %}
                            <small class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                {{ message.error_message }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-info" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#messageModal"
                                    data-message="{{ message.message }}"
                                    data-phone="{{ message.phone_number }}"
                                    data-customer="{{ message.customer.full_name if message.customer else 'غير محدد' }}">
                                <i class="fas fa-eye"></i>
                            </button>
                            
                            {% if message.status == 'failed' and message.customer %}
                            <a href="{{ url_for('whatsapp.send_message') }}?customer_id={{ message.customer.id }}" 
                               class="btn btn-sm btn-outline-success" title="إعادة الإرسال">
                                <i class="fas fa-redo"></i>
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if messages.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if messages.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('whatsapp.index', page=messages.prev_num, status=status, message_type=message_type) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in messages.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != messages.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('whatsapp.index', page=page_num, status=status, message_type=message_type) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if messages.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('whatsapp.index', page=messages.next_num, status=status, message_type=message_type) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fab fa-whatsapp fa-3x text-muted mb-3"></i>
            <h5>لا توجد رسائل</h5>
            <p class="text-muted">لم يتم إرسال أي رسائل واتساب بعد.</p>
            <a href="{{ url_for('whatsapp.send_message') }}" class="btn btn-success">
                <i class="fab fa-whatsapp me-2"></i>
                إرسال أول رسالة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Message Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الرسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>العميل:</strong></label>
                    <p id="modalCustomer"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>رقم الهاتف:</strong></label>
                    <p id="modalPhone"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>نص الرسالة:</strong></label>
                    <div class="border p-3 bg-light rounded">
                        <pre id="modalMessage" style="white-space: pre-wrap; margin: 0;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Handle message modal
    $('#messageModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const message = button.data('message');
        const phone = button.data('phone');
        const customer = button.data('customer');
        
        $('#modalMessage').text(message);
        $('#modalPhone').text(phone);
        $('#modalCustomer').text(customer);
    });
    
    // Auto-refresh every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
