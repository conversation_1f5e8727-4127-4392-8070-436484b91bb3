#!/usr/bin/env python3
"""
Migration script to update customer data format for Qatar standards
- National ID: 11 digits
- Phone numbers: 8 digits

Run this script after updating the validation rules.
"""

import sqlite3
import re
from datetime import datetime

def migrate_qatar_format():
    """Migrate existing customer data to Qatar format"""
    
    # Connect to database
    conn = sqlite3.connect('instance/car_dealership.db')
    cursor = conn.cursor()
    
    print("Starting migration to Qatar format...")
    print("=" * 50)
    
    # Get all customers
    cursor.execute("SELECT id, full_name, national_id, phone, whatsapp_number, guarantor_id, guarantor_phone FROM customer")
    customers = cursor.fetchall()
    
    migration_log = []
    
    for customer in customers:
        customer_id, full_name, national_id, phone, whatsapp_number, guarantor_id, guarantor_phone = customer
        
        print(f"\nProcessing customer: {full_name} (ID: {customer_id})")
        
        # Check national ID format
        if national_id and len(national_id) != 11:
            print(f"  ⚠️  National ID '{national_id}' is not 11 digits")
            migration_log.append(f"Customer {full_name}: National ID '{national_id}' needs manual review")
        
        # Check phone format
        if phone:
            phone_digits = re.sub(r'[^\d]', '', phone)
            if len(phone_digits) != 8:
                print(f"  ⚠️  Phone '{phone}' is not 8 digits")
                migration_log.append(f"Customer {full_name}: Phone '{phone}' needs manual review")
        
        # Check WhatsApp format
        if whatsapp_number:
            whatsapp_digits = re.sub(r'[^\d]', '', whatsapp_number)
            if len(whatsapp_digits) != 8:
                print(f"  ⚠️  WhatsApp '{whatsapp_number}' is not 8 digits")
                migration_log.append(f"Customer {full_name}: WhatsApp '{whatsapp_number}' needs manual review")
        
        # Check guarantor ID format
        if guarantor_id and len(guarantor_id) != 11:
            print(f"  ⚠️  Guarantor ID '{guarantor_id}' is not 11 digits")
            migration_log.append(f"Customer {full_name}: Guarantor ID '{guarantor_id}' needs manual review")
        
        # Check guarantor phone format
        if guarantor_phone:
            guarantor_phone_digits = re.sub(r'[^\d]', '', guarantor_phone)
            if len(guarantor_phone_digits) != 8:
                print(f"  ⚠️  Guarantor phone '{guarantor_phone}' is not 8 digits")
                migration_log.append(f"Customer {full_name}: Guarantor phone '{guarantor_phone}' needs manual review")
    
    # Close database connection
    conn.close()
    
    # Print summary
    print("\n" + "=" * 50)
    print("Migration Summary:")
    print(f"Total customers processed: {len(customers)}")
    print(f"Issues found: {len(migration_log)}")
    
    if migration_log:
        print("\nIssues that need manual review:")
        print("-" * 30)
        for issue in migration_log:
            print(f"• {issue}")
        
        # Save log to file
        log_filename = f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(log_filename, 'w', encoding='utf-8') as f:
            f.write("Qatar Format Migration Log\n")
            f.write("=" * 30 + "\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total customers: {len(customers)}\n")
            f.write(f"Issues found: {len(migration_log)}\n\n")
            f.write("Issues:\n")
            f.write("-" * 10 + "\n")
            for issue in migration_log:
                f.write(f"• {issue}\n")
        
        print(f"\nDetailed log saved to: {log_filename}")
    else:
        print("\n✅ All customer data is already in Qatar format!")
    
    print("\nMigration completed.")

def validate_qatar_format():
    """Validate that all customer data follows Qatar format"""
    
    conn = sqlite3.connect('instance/car_dealership.db')
    cursor = conn.cursor()
    
    print("Validating Qatar format compliance...")
    print("=" * 40)
    
    # Check national IDs
    cursor.execute("SELECT COUNT(*) FROM customer WHERE LENGTH(national_id) != 11 OR national_id NOT GLOB '[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]'")
    invalid_national_ids = cursor.fetchone()[0]
    
    # Check phone numbers
    cursor.execute("SELECT COUNT(*) FROM customer WHERE LENGTH(REPLACE(REPLACE(REPLACE(REPLACE(phone, '+', ''), '-', ''), ' ', ''), '(', '')) != 8")
    invalid_phones = cursor.fetchone()[0]
    
    conn.close()
    
    print(f"Invalid national IDs: {invalid_national_ids}")
    print(f"Invalid phone numbers: {invalid_phones}")
    
    if invalid_national_ids == 0 and invalid_phones == 0:
        print("\n✅ All data is compliant with Qatar format!")
        return True
    else:
        print("\n❌ Some data needs to be corrected.")
        return False

if __name__ == "__main__":
    print("Qatar Format Migration Tool")
    print("=" * 30)
    print("1. Migrate existing data")
    print("2. Validate format compliance")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        migrate_qatar_format()
    elif choice == "2":
        validate_qatar_format()
    elif choice == "3":
        print("Goodbye!")
    else:
        print("Invalid choice. Please run the script again.")
