{% extends "base.html" %}

{% block title %}تعديل رقم اللوحة {{ plate_number.number }}{% endblock %}

{% block extra_css %}
<style>
.plate-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 2rem;
    margin-bottom: 20px;
}

.plate-preview.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.plate-preview.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.plate-preview.regular {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.form-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.section-title {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.features-display {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.feature-badge {
    display: inline-block;
    margin: 2px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.image-item .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 0.8rem;
    cursor: pointer;
}

.image-item .remove-btn:hover {
    background: #dc3545;
}

.status-section {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.danger-zone {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.action-buttons {
    position: sticky;
    top: 20px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Form -->
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-edit me-2"></i>تعديل رقم اللوحة</h2>
                    <p class="text-muted">تعديل بيانات رقم اللوحة {{ plate_number.number }}</p>
                </div>
                <a href="{{ url_for('plate_numbers.view', id=plate_number.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتفاصيل
                </a>
            </div>

            <!-- Current Status -->
            <div class="status-section">
                <h6><i class="fas fa-info-circle me-2"></i>الحالة الحالية</h6>
                <div class="row">
                    <div class="col-md-3">
                        <strong>الحالة:</strong>
                        <span class="badge bg-{{ 'success' if plate_number.status == 'available' else 'danger' if plate_number.status == 'sold' else 'warning' }}">
                            {% if plate_number.status == 'available' %}متاحة
                            {% elif plate_number.status == 'sold' %}مباعة
                            {% elif plate_number.status == 'reserved' %}محجوزة
                            {% else %}محظورة{% endif %}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>الفئة:</strong>
                        <span class="badge bg-info">
                            {% if plate_number.category == 'vip' %}VIP
                            {% elif plate_number.category == 'special' %}مميزة
                            {% elif plate_number.category == 'regular' %}عادية
                            {% else %}مخصصة{% endif %}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>السعر:</strong>
                        {{ "{:,.0f}".format(plate_number.price) }} ريال
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الإضافة:</strong>
                        {{ plate_number.created_at.strftime('%Y-%m-%d') }}
                    </div>
                </div>
            </div>

            <form method="POST" enctype="multipart/form-data" id="editForm">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="number" class="form-label">رقم اللوحة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="number" name="number" 
                                       value="{{ plate_number.number }}" required>
                                <div class="form-text">تأكد من صحة الرقم قبل التعديل</div>
                                <div id="numberStatus" class="mt-2"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="vip" {{ 'selected' if plate_number.category == 'vip' }}>VIP - أرقام مميزة جداً</option>
                                    <option value="special" {{ 'selected' if plate_number.category == 'special' }}>مميزة - أرقام خاصة</option>
                                    <option value="regular" {{ 'selected' if plate_number.category == 'regular' }}>عادية - أرقام عادية</option>
                                    <option value="custom" {{ 'selected' if plate_number.category == 'custom' }}>مخصصة - أرقام مخصصة</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="type" class="form-label">النوع <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="private" {{ 'selected' if plate_number.type == 'private' }}>خاصة</option>
                                    <option value="taxi" {{ 'selected' if plate_number.type == 'taxi' }}>تاكسي</option>
                                    <option value="transport" {{ 'selected' if plate_number.type == 'transport' }}>نقل</option>
                                    <option value="government" {{ 'selected' if plate_number.type == 'government' }}>حكومية</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="code_letter" class="form-label">الحرف</label>
                                <input type="text" class="form-control" id="code_letter" name="code_letter" 
                                       value="{{ plate_number.code_letter or '' }}" maxlength="5">
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="series" class="form-label">السلسلة</label>
                                <input type="text" class="form-control" id="series" name="series" 
                                       value="{{ plate_number.series or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="2">{{ plate_number.description or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Management -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-toggle-on me-2"></i>إدارة الحالة</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة الرقم <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="available" {{ 'selected' if plate_number.status == 'available' }}>متاحة للبيع</option>
                                    <option value="sold" {{ 'selected' if plate_number.status == 'sold' }}>مباعة</option>
                                    <option value="reserved" {{ 'selected' if plate_number.status == 'reserved' }}>محجوزة</option>
                                    <option value="blocked" {{ 'selected' if plate_number.status == 'blocked' }}>محظورة</option>
                                </select>
                                <div class="form-text">تغيير الحالة يؤثر على إمكانية البيع والمزايدة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Special Features -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-star me-2"></i>المميزات الخاصة</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_sequential" name="is_sequential"
                                       {{ 'checked' if plate_number.is_sequential }}>
                                <label class="form-check-label" for="is_sequential">
                                    <strong>أرقام متسلسلة</strong> - مثل 1234، 5678
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_repeated" name="is_repeated"
                                       {{ 'checked' if plate_number.is_repeated }}>
                                <label class="form-check-label" for="is_repeated">
                                    <strong>أرقام مكررة</strong> - مثل 1111، 7777
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_mirror" name="is_mirror"
                                       {{ 'checked' if plate_number.is_mirror }}>
                                <label class="form-check-label" for="is_mirror">
                                    <strong>أرقام مرآة</strong> - مثل 1221، 3443
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_birthday" name="is_birthday"
                                       {{ 'checked' if plate_number.is_birthday }}>
                                <label class="form-check-label" for="is_birthday">
                                    <strong>تاريخ ميلاد</strong> - أرقام تمثل تواريخ
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_lucky" name="is_lucky"
                                       {{ 'checked' if plate_number.is_lucky }}>
                                <label class="form-check-label" for="is_lucky">
                                    <strong>أرقام محظوظة</strong> - أرقام تجلب الحظ
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="featuresDisplay" class="features-display" style="display: none;">
                        <strong>المميزات المحددة:</strong>
                        <div id="selectedFeatures"></div>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-money-bill-wave me-2"></i>التسعير</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="{{ plate_number.price }}" required min="0" step="100">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                           value="{{ plate_number.cost_price or '' }}" min="0" step="100">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">اختياري - للتقارير المالية</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images Management -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-images me-2"></i>إدارة الصور</h5>
                    
                    <!-- Current Images -->
                    {% if images %}
                    <div class="mb-3">
                        <label class="form-label">الصور الحالية</label>
                        <div class="image-gallery">
                            {% for image in images %}
                            <div class="image-item">
                                <img src="{{ url_for('static', filename=image) }}" alt="صورة رقم اللوحة">
                                <button type="button" class="remove-btn" onclick="removeImage('{{ image }}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Add New Images -->
                    <div class="mb-3">
                        <label for="images" class="form-label">إضافة صور جديدة</label>
                        <input type="file" class="form-control" id="images" name="images" multiple accept="image/*">
                        <div class="form-text">يمكنك اختيار عدة صور (PNG, JPG, JPEG, GIF)</div>
                        <div id="imagePreview" class="mt-3 row"></div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>حفظ التعديلات
                        </button>
                    </div>
                    <div>
                        <button type="reset" class="btn btn-outline-warning me-2">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <a href="{{ url_for('plate_numbers.view', id=plate_number.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>

            <!-- Danger Zone -->
            {% if current_user.has_permission('delete_all') %}
            <div class="danger-zone">
                <h5 class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>منطقة الخطر</h5>
                <p class="text-muted">العمليات التالية لا يمكن التراجع عنها. تأكد من رغبتك في المتابعة.</p>
                <form method="POST" action="{{ url_for('plate_numbers.delete', id=plate_number.id) }}" 
                      onsubmit="return confirm('هل أنت متأكد من حذف رقم اللوحة {{ plate_number.number }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>حذف رقم اللوحة نهائياً
                    </button>
                </form>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="action-buttons">
                <!-- Plate Preview -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة اللوحة</h5>
                    </div>
                    <div class="card-body">
                        <div id="platePreview" class="plate-preview {{ plate_number.category }}">
                            {{ plate_number.number }}
                        </div>
                        <div id="analysisResults"></div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('plate_numbers.view', id=plate_number.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                            </a>
                            {% if plate_number.status == 'available' %}
                            <a href="{{ url_for('plate_numbers.sell', id=plate_number.id) }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-handshake me-2"></i>بيع الرقم
                            </a>
                            {% endif %}
                            <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-list me-2"></i>جميع الأرقام
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Edit History -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>معلومات التعديل</h5>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <strong>تاريخ الإنشاء:</strong><br>
                            {{ plate_number.created_at.strftime('%Y-%m-%d %H:%M') }}<br><br>
                            
                            <strong>آخر تحديث:</strong><br>
                            {{ plate_number.updated_at.strftime('%Y-%m-%d %H:%M') }}<br><br>
                            
                            <strong>أنشأ بواسطة:</strong><br>
                            المستخدم رقم {{ plate_number.created_by }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update plate preview when number changes
    $('#number').on('input', function() {
        const number = $(this).val().trim();
        updatePlatePreview(number);
        
        if (number.length >= 2 && number !== '{{ plate_number.number }}') {
            checkNumberExists(number);
        }
    });
    
    // Update preview when category changes
    $('#category').on('change', function() {
        updatePlatePreview($('#number').val());
    });
    
    // Feature checkboxes
    $('input[type="checkbox"][name^="is_"]').on('change', function() {
        updateFeaturesDisplay();
    });
    
    // Image preview
    $('#images').on('change', function() {
        previewImages(this.files);
    });
    
    // Initialize features display
    updateFeaturesDisplay();
});

function updatePlatePreview(number) {
    const preview = $('#platePreview');
    const category = $('#category').val() || '{{ plate_number.category }}';
    
    preview.removeClass('vip special regular custom')
           .addClass(category)
           .text(number || '{{ plate_number.number }}');
}

function checkNumberExists(number) {
    $.get('/plate-numbers/api/check-number', { number: number })
        .done(function(data) {
            const status = $('#numberStatus');
            if (data.exists) {
                status.html('<div class="alert alert-danger alert-sm">⚠️ هذا الرقم موجود مسبقاً</div>');
            } else {
                status.html('<div class="alert alert-success alert-sm">✅ الرقم متاح</div>');
            }
        });
}

function updateFeaturesDisplay() {
    const features = [];
    
    if ($('#is_sequential').is(':checked')) features.push('<span class="feature-badge bg-success text-white">متسلسل</span>');
    if ($('#is_repeated').is(':checked')) features.push('<span class="feature-badge bg-warning text-dark">مكرر</span>');
    if ($('#is_mirror').is(':checked')) features.push('<span class="feature-badge bg-info text-white">مرآة</span>');
    if ($('#is_birthday').is(':checked')) features.push('<span class="feature-badge bg-primary text-white">تاريخ ميلاد</span>');
    if ($('#is_lucky').is(':checked')) features.push('<span class="feature-badge bg-danger text-white">محظوظ</span>');
    
    if (features.length > 0) {
        $('#selectedFeatures').html(features.join(' '));
        $('#featuresDisplay').show();
    } else {
        $('#featuresDisplay').hide();
    }
}

function previewImages(files) {
    const preview = $('#imagePreview');
    preview.empty();
    
    Array.from(files).forEach((file, index) => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.append(`
                    <div class="col-md-6 mb-2">
                        <img src="${e.target.result}" class="img-thumbnail" style="max-height: 100px;">
                        <small class="d-block text-muted">${file.name}</small>
                    </div>
                `);
            };
            reader.readAsDataURL(file);
        }
    });
}

function removeImage(imagePath) {
    if (confirm('هل تريد حذف هذه الصورة؟')) {
        // This would require an AJAX call to remove the image
        // For now, just hide the image element
        event.target.closest('.image-item').style.display = 'none';
        showNotification('سيتم حذف الصورة عند حفظ التعديلات', 'info');
    }
}

// Form validation
$('#editForm').on('submit', function(e) {
    const number = $('#number').val().trim();
    const price = parseFloat($('#price').val());
    
    if (!number) {
        e.preventDefault();
        showNotification('يجب إدخال رقم اللوحة', 'error');
        return;
    }
    
    if (!price || price < 0) {
        e.preventDefault();
        showNotification('يجب إدخال سعر صحيح', 'error');
        return;
    }
    
    // Confirm changes
    if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
