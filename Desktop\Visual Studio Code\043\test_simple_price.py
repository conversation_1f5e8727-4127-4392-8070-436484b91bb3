#!/usr/bin/env python3
"""
Test Simple Price Handler solution
"""

import os
import sys

def test_files_exist():
    """Test if required files exist"""
    print("🔍 Testing file existence...")
    
    files_to_check = [
        'static/js/simple-price-handler.js',
        'templates/price-test.html',
        'SIMPLE_PRICE_SOLUTION.md'
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_exist = False
    
    return all_exist

def test_simple_price_handler():
    """Test Simple Price Handler content"""
    print("\n🔍 Testing Simple Price Handler...")
    
    try:
        with open('static/js/simple-price-handler.js', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for key components
        checks = [
            ('class SimplePriceHandler', 'Class definition'),
            ('setupPriceInputs', 'Setup function'),
            ('cleanPriceInput', 'Clean function'),
            ('formatPrice', 'Format function'),
            ('currency-input', 'Currency input selector'),
            ('window.simplePriceHandler', 'Global assignment')
        ]
        
        all_good = True
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - NOT FOUND")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def test_base_html_integration():
    """Test if base.html includes the new script"""
    print("\n🔍 Testing base.html integration...")
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'simple-price-handler.js' in content:
            print("✅ Simple Price Handler script included in base.html")
            return True
        else:
            print("❌ Simple Price Handler script NOT included in base.html")
            return False
            
    except Exception as e:
        print(f"❌ Error reading base.html: {e}")
        return False

def test_app_import():
    """Test if app can be imported"""
    print("\n🔍 Testing app import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app
        app = create_app()
        
        print("✅ App created successfully")
        print(f"✅ App name: {app.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ App import error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 اختبار حل Simple Price Handler")
    print("=" * 60)
    
    tests = [
        test_files_exist,
        test_simple_price_handler,
        test_base_html_integration,
        test_app_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📱 للاختبار الفعلي:")
        print("1. شغل الخادم: python quick_start.py")
        print("2. اذهب إلى: http://localhost:5000/price-test")
        print("3. تحقق من معلومات التشخيص")
        print("4. جرب البيانات التجريبية")
        print("\n💰 اختبار الأسعار:")
        print("- أدخل 50000 في أي حقل")
        print("- اضغط خارج الحقل")
        print("- يجب أن يصبح 50,000")
        return True
    else:
        print("❌ بعض الاختبارات فشلت.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للمتابعة...")
    sys.exit(0 if success else 1)
