{% extends "base.html" %}

{% block title %}بيع رقم اللوحة {{ plate_number.number }}{% endblock %}

{% block extra_css %}
<style>
.plate-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
}

.plate-preview.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.plate-preview.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.plate-display {
    font-family: 'Courier New', monospace;
    font-size: 3rem;
    font-weight: bold;
    margin: 15px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.form-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.section-title {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.payment-method-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method-card:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.payment-method-card.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.installment-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 15px;
    display: none;
}

.price-summary {
    background: #e8f5e8;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #28a745;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #dee2e6;
}

.summary-row:last-child {
    border-bottom: none;
    font-weight: bold;
    font-size: 1.1rem;
}

.customer-info {
    background: #fff3cd;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
    margin-top: 15px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Form -->
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-handshake me-2"></i>بيع رقم اللوحة</h2>
                    <p class="text-muted">إتمام عملية بيع رقم اللوحة للعميل</p>
                </div>
                <a href="{{ url_for('plate_numbers.view', id=plate_number.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتفاصيل
                </a>
            </div>

            <!-- Plate Preview -->
            <div class="plate-preview {{ plate_number.category }}">
                <div class="plate-display">{{ plate_number.number }}</div>
                <div class="row">
                    <div class="col-md-4">
                        <h6>الفئة</h6>
                        <span class="badge bg-light text-dark">
                            {% if plate_number.category == 'vip' %}VIP
                            {% elif plate_number.category == 'special' %}مميزة
                            {% elif plate_number.category == 'regular' %}عادية
                            {% else %}مخصصة{% endif %}
                        </span>
                    </div>
                    <div class="col-md-4">
                        <h6>النوع</h6>
                        <span class="badge bg-light text-dark">
                            {% if plate_number.type == 'private' %}خاصة
                            {% elif plate_number.type == 'taxi' %}تاكسي
                            {% elif plate_number.type == 'transport' %}نقل
                            {% else %}حكومية{% endif %}
                        </span>
                    </div>
                    <div class="col-md-4">
                        <h6>السعر المحدد</h6>
                        <span class="badge bg-light text-dark">{{ "{:,.0f}".format(plate_number.price) }} ريال</span>
                    </div>
                </div>
            </div>

            <form method="POST" id="saleForm">
                <!-- Customer Selection -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-user me-2"></i>بيانات العميل</h5>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" 
                                            data-name="{{ customer.full_name }}"
                                            data-phone="{{ customer.phone }}"
                                            data-id-number="{{ customer.id_number }}"
                                            data-email="{{ customer.email }}">
                                        {{ customer.full_name }} - {{ customer.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <a href="{{ url_for('customers.add') }}" class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-plus me-2"></i>عميل جديد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="customerInfo" class="customer-info" style="display: none;">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات العميل</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>الاسم:</strong> <span id="customerName"></span><br>
                                <strong>الهاتف:</strong> <span id="customerPhone"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>رقم الهوية:</strong> <span id="customerIdNumber"></span><br>
                                <strong>البريد:</strong> <span id="customerEmail"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-money-bill-wave me-2"></i>التسعير</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sale_price" class="form-label">سعر البيع النهائي <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="sale_price" name="sale_price" 
                                           value="{{ plate_number.price }}" required min="0" step="100">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">يمكن تعديل السعر حسب التفاوض</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">السعر الأصلي</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ '{:,.0f}'.format(plate_number.price) }}" readonly>
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">السعر المحدد مسبقاً</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-credit-card me-2"></i>طريقة الدفع</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="payment-method-card" data-method="cash">
                                <div class="text-center">
                                    <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                                    <h6>دفع نقدي</h6>
                                    <p class="text-muted small">دفع كامل فوري</p>
                                </div>
                                <input type="radio" name="payment_method" value="cash" class="d-none">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="payment-method-card" data-method="bank_transfer">
                                <div class="text-center">
                                    <i class="fas fa-university fa-2x text-primary mb-2"></i>
                                    <h6>تحويل بنكي</h6>
                                    <p class="text-muted small">تحويل مباشر للحساب</p>
                                </div>
                                <input type="radio" name="payment_method" value="bank_transfer" class="d-none">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="payment-method-card" data-method="installment">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
                                    <h6>أقساط</h6>
                                    <p class="text-muted small">دفع على فترات</p>
                                </div>
                                <input type="radio" name="payment_method" value="installment" class="d-none">
                            </div>
                        </div>
                    </div>

                    <!-- Installment Details -->
                    <div id="installmentSection" class="installment-section">
                        <h6><i class="fas fa-calculator me-2"></i>تفاصيل الأقساط</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="down_payment" class="form-label">الدفعة الأولى</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="down_payment" name="down_payment" 
                                               min="0" step="100" value="0">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installment_count" class="form-label">عدد الأقساط</label>
                                    <select class="form-select" id="installment_count" name="installment_count">
                                        <option value="">اختر عدد الأقساط</option>
                                        <option value="3">3 أقساط</option>
                                        <option value="6">6 أقساط</option>
                                        <option value="12">12 قسط</option>
                                        <option value="24">24 قسط</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div id="installmentCalculation" class="mt-3"></div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-sticky-note me-2"></i>معلومات إضافية</h5>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات خاصة بعملية البيع..."></textarea>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-handshake me-2"></i>إتمام البيع
                        </button>
                    </div>
                    <div>
                        <a href="{{ url_for('plate_numbers.view', id=plate_number.id) }}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="previewSale()">
                            <i class="fas fa-eye me-2"></i>معاينة
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Summary Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 20px;">
                <!-- Price Summary -->
                <div class="price-summary">
                    <h5><i class="fas fa-calculator me-2"></i>ملخص السعر</h5>
                    
                    <div class="summary-row">
                        <span>سعر البيع:</span>
                        <span id="summaryPrice">{{ "{:,.0f}".format(plate_number.price) }} ريال</span>
                    </div>
                    
                    <div class="summary-row" id="downPaymentRow" style="display: none;">
                        <span>الدفعة الأولى:</span>
                        <span id="summaryDownPayment">0 ريال</span>
                    </div>
                    
                    <div class="summary-row" id="installmentRow" style="display: none;">
                        <span>قسط شهري:</span>
                        <span id="summaryInstallment">0 ريال</span>
                    </div>
                    
                    <div class="summary-row" id="remainingRow" style="display: none;">
                        <span>المبلغ المتبقي:</span>
                        <span id="summaryRemaining">0 ريال</span>
                    </div>
                    
                    <div class="summary-row">
                        <span>المبلغ الإجمالي:</span>
                        <span id="summaryTotal">{{ "{:,.0f}".format(plate_number.price) }} ريال</span>
                    </div>
                </div>

                <!-- Plate Info -->
                <div class="form-section mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات الرقم</h6>
                    <div class="summary-row">
                        <span>رقم اللوحة:</span>
                        <span>{{ plate_number.number }}</span>
                    </div>
                    <div class="summary-row">
                        <span>الفئة:</span>
                        <span>
                            {% if plate_number.category == 'vip' %}VIP
                            {% elif plate_number.category == 'special' %}مميزة
                            {% elif plate_number.category == 'regular' %}عادية
                            {% else %}مخصصة{% endif %}
                        </span>
                    </div>
                    <div class="summary-row">
                        <span>عدد الأرقام:</span>
                        <span>{{ plate_number.digits_count }}</span>
                    </div>
                    {% if plate_number.cost_price %}
                    <div class="summary-row">
                        <span>هامش الربح:</span>
                        <span class="text-success">{{ "{:,.0f}".format(plate_number.price - plate_number.cost_price) }} ريال</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Quick Actions -->
                <div class="form-section mt-3">
                    <h6><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="fillSampleData()">
                            <i class="fas fa-magic me-2"></i>بيانات تجريبية
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="calculateProfit()">
                            <i class="fas fa-chart-line me-2"></i>حساب الربح
                        </button>
                        <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-2"></i>جميع الأرقام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Payment method selection
    $('.payment-method-card').on('click', function() {
        $('.payment-method-card').removeClass('selected');
        $(this).addClass('selected');
        
        const method = $(this).data('method');
        $('input[name="payment_method"]').prop('checked', false);
        $(this).find('input[name="payment_method"]').prop('checked', true);
        
        if (method === 'installment') {
            $('#installmentSection').slideDown();
        } else {
            $('#installmentSection').slideUp();
            updateSummary();
        }
    });
    
    // Customer selection
    $('#customer_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
            $('#customerName').text(selectedOption.data('name'));
            $('#customerPhone').text(selectedOption.data('phone'));
            $('#customerIdNumber').text(selectedOption.data('id-number'));
            $('#customerEmail').text(selectedOption.data('email'));
            $('#customerInfo').slideDown();
        } else {
            $('#customerInfo').slideUp();
        }
    });
    
    // Price change
    $('#sale_price').on('input', updateSummary);
    
    // Installment calculation
    $('#down_payment, #installment_count').on('change', calculateInstallments);
});

function updateSummary() {
    const salePrice = parseFloat($('#sale_price').val()) || 0;
    const paymentMethod = $('input[name="payment_method"]:checked').val();
    
    $('#summaryPrice').text(salePrice.toLocaleString() + ' ريال');
    $('#summaryTotal').text(salePrice.toLocaleString() + ' ريال');
    
    if (paymentMethod === 'installment') {
        calculateInstallments();
    } else {
        $('#downPaymentRow, #installmentRow, #remainingRow').hide();
    }
}

function calculateInstallments() {
    const salePrice = parseFloat($('#sale_price').val()) || 0;
    const downPayment = parseFloat($('#down_payment').val()) || 0;
    const installmentCount = parseInt($('#installment_count').val()) || 0;
    
    if (salePrice > 0 && installmentCount > 0) {
        const remaining = salePrice - downPayment;
        const monthlyInstallment = remaining / installmentCount;
        
        $('#summaryDownPayment').text(downPayment.toLocaleString() + ' ريال');
        $('#summaryInstallment').text(monthlyInstallment.toLocaleString() + ' ريال');
        $('#summaryRemaining').text(remaining.toLocaleString() + ' ريال');
        
        $('#downPaymentRow, #installmentRow, #remainingRow').show();
        
        // Update calculation display
        $('#installmentCalculation').html(`
            <div class="alert alert-info">
                <h6>حساب الأقساط:</h6>
                <p class="mb-1"><strong>إجمالي السعر:</strong> ${salePrice.toLocaleString()} ريال</p>
                <p class="mb-1"><strong>الدفعة الأولى:</strong> ${downPayment.toLocaleString()} ريال</p>
                <p class="mb-1"><strong>المبلغ المتبقي:</strong> ${remaining.toLocaleString()} ريال</p>
                <p class="mb-0"><strong>القسط الشهري:</strong> ${monthlyInstallment.toLocaleString()} ريال × ${installmentCount} شهر</p>
            </div>
        `);
    }
}

function fillSampleData() {
    if (confirm('هل تريد ملء النموذج ببيانات تجريبية؟')) {
        // Select first customer if available
        if ($('#customer_id option').length > 1) {
            $('#customer_id').val($('#customer_id option:eq(1)').val()).trigger('change');
        }
        
        // Set payment method to cash
        $('.payment-method-card[data-method="cash"]').click();
        
        // Add sample notes
        $('#notes').val('بيع رقم لوحة مميز - عملية تجريبية');
        
        showNotification('تم ملء البيانات التجريبية', 'success');
    }
}

function calculateProfit() {
    const salePrice = parseFloat($('#sale_price').val()) || 0;
    const costPrice = {{ plate_number.cost_price or 0 }};
    
    if (costPrice > 0) {
        const profit = salePrice - costPrice;
        const profitPercentage = ((profit / costPrice) * 100).toFixed(1);
        
        showNotification(`الربح المتوقع: ${profit.toLocaleString()} ريال (${profitPercentage}%)`, 'info');
    } else {
        showNotification('لم يتم تحديد سعر التكلفة', 'warning');
    }
}

function previewSale() {
    const customer = $('#customer_id option:selected').text();
    const salePrice = $('#sale_price').val();
    const paymentMethod = $('input[name="payment_method"]:checked').val();
    
    if (!customer || customer === 'اختر العميل') {
        showNotification('يجب اختيار العميل أولاً', 'error');
        return;
    }
    
    if (!paymentMethod) {
        showNotification('يجب اختيار طريقة الدفع', 'error');
        return;
    }
    
    let paymentText = '';
    if (paymentMethod === 'cash') paymentText = 'نقد';
    else if (paymentMethod === 'bank_transfer') paymentText = 'تحويل بنكي';
    else paymentText = 'أقساط';
    
    const preview = `
        <strong>معاينة البيع:</strong><br>
        رقم اللوحة: {{ plate_number.number }}<br>
        العميل: ${customer}<br>
        السعر: ${parseFloat(salePrice).toLocaleString()} ريال<br>
        طريقة الدفع: ${paymentText}
    `;
    
    showNotification(preview, 'info');
}

// Form validation
$('#saleForm').on('submit', function(e) {
    const customerId = $('#customer_id').val();
    const salePrice = parseFloat($('#sale_price').val());
    const paymentMethod = $('input[name="payment_method"]:checked').val();
    
    if (!customerId) {
        e.preventDefault();
        showNotification('يجب اختيار العميل', 'error');
        return;
    }
    
    if (!salePrice || salePrice <= 0) {
        e.preventDefault();
        showNotification('يجب إدخال سعر بيع صحيح', 'error');
        return;
    }
    
    if (!paymentMethod) {
        e.preventDefault();
        showNotification('يجب اختيار طريقة الدفع', 'error');
        return;
    }
    
    if (paymentMethod === 'installment') {
        const installmentCount = $('#installment_count').val();
        if (!installmentCount) {
            e.preventDefault();
            showNotification('يجب تحديد عدد الأقساط', 'error');
            return;
        }
    }
    
    // Confirm sale
    if (!confirm('هل أنت متأكد من إتمام عملية البيع؟')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
