# نظام إدارة معرض السيارات - Car Dealership Management System

نظام شامل لإدارة معارض السيارات يدعم البيع النقدي والتقسيط مع إدارة العملاء والعقود والتقارير.

## المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن
- أدوار متعددة: مدير، موظف مبيعات، محاسب
- صلاحيات مختلفة لكل دور

### 🚗 إدارة السيارات
- إضافة وتعديل وحذف السيارات
- تفاصيل شاملة: الماركة، الموديل، السنة، رقم الشاسيه، اللوحة
- رفع صور متعددة للسيارة
- تتبع حالة السيارة (متاحة، مباعة، محجوزة)

### 👥 إدارة العملاء
- بيانات العملاء الكاملة
- معلومات الضامن
- رفع صور الهوية والمستندات
- ربط رقم الواتساب

### 💰 المبيعات والأقساط
- بيع نقدي وبالتقسيط
- حساب الأقساط تلقائياً
- تتبع المدفوعات
- تنبيهات الأقساط المتأخرة

### 📄 إدارة العقود
- إنشاء عقود PDF و Word تلقائياً
- دعم النصوص العربية
- قوالب عقود قابلة للتخصيص
- توقيع إلكتروني

### 📊 التقارير والإحصائيات
- تقارير المبيعات
- تقارير الأقساط
- تقارير السيارات
- تقارير مالية
- تصدير Excel

### 📱 تكامل الواتساب
- إرسال تذكيرات الأقساط
- إشعارات العقود
- رسائل التأخير
- قوالب رسائل جاهزة

### 📈 لوحة التحكم
- إحصائيات شاملة
- تنبيهات فورية
- أقساط مستحقة
- مبيعات حديثة

## متطلبات النظام

- Python 3.8+
- Flask
- SQLite/PostgreSQL
- Bootstrap 5
- Font Awesome

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعداد قاعدة البيانات
```bash
python app.py
```
سيتم إنشاء قاعدة البيانات تلقائياً مع مستخدم افتراضي:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 3. إعداد الواتساب (اختياري)
قم بإعداد متغيرات البيئة التالية:
```bash
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
```

### 4. تشغيل التطبيق
```bash
python app.py
```

الموقع سيكون متاحاً على: `http://localhost:5000`

## الاستخدام

### تسجيل الدخول الأول
1. افتح المتصفح وانتقل إلى `http://localhost:5000`
2. استخدم بيانات المدير الافتراضية
3. قم بتغيير كلمة المرور من الملف الشخصي

### إضافة المستخدمين
1. انتقل إلى الإعدادات > المستخدمين
2. أضف مستخدمين جدد مع الأدوار المناسبة

### إضافة السيارات
1. انتقل إلى السيارات > إضافة سيارة جديدة
2. أدخل تفاصيل السيارة
3. ارفع صور السيارة

### إضافة العملاء
1. انتقل إلى العملاء > إضافة عميل جديد
2. أدخل بيانات العميل والضامن
3. ارفع صور الهوية

### إنشاء عملية بيع
1. انتقل إلى المبيعات > عملية بيع جديدة
2. اختر السيارة والعميل
3. حدد نوع البيع (نقدي أو تقسيط)
4. أدخل تفاصيل الدفع

## الأدوار والصلاحيات

### المدير
- جميع الصلاحيات
- إدارة المستخدمين
- الإعدادات العامة
- حذف البيانات

### موظف المبيعات
- إدارة السيارات
- إدارة العملاء
- إنشاء المبيعات
- عرض التقارير

### المحاسب
- عرض جميع البيانات
- إدارة المدفوعات
- التقارير المالية
- متابعة الأقساط

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**ملاحظة**: تأكد من تغيير كلمة المرور الافتراضية وإعداد النسخ الاحتياطي قبل الاستخدام في بيئة الإنتاج.
