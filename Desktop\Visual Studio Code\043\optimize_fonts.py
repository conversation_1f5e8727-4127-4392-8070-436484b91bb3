#!/usr/bin/env python3
"""
Font Optimization Script for Qatar Car Dealership System
Optimizes Arabic fonts for better performance and loading
"""

import os
import shutil
import subprocess
from pathlib import Path

class FontOptimizer:
    def __init__(self, fonts_dir="static/fonts"):
        self.fonts_dir = Path(fonts_dir)
        self.optimized_dir = self.fonts_dir / "optimized"
        self.backup_dir = self.fonts_dir / "backup"
        
    def create_directories(self):
        """Create necessary directories"""
        self.optimized_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        print(f"✅ Created directories: {self.optimized_dir}, {self.backup_dir}")
    
    def backup_fonts(self):
        """Backup original fonts"""
        font_files = list(self.fonts_dir.glob("*.ttf")) + list(self.fonts_dir.glob("*.otf"))
        
        for font_file in font_files:
            if font_file.parent != self.backup_dir:
                backup_path = self.backup_dir / font_file.name
                shutil.copy2(font_file, backup_path)
                print(f"📦 Backed up: {font_file.name}")
    
    def get_font_info(self, font_path):
        """Get font information"""
        try:
            # This would require fonttools library
            # pip install fonttools
            from fontTools.ttLib import TTFont
            
            font = TTFont(font_path)
            
            # Get font name
            name_table = font['name']
            font_name = None
            for record in name_table.names:
                if record.nameID == 1:  # Font Family name
                    font_name = record.toUnicode()
                    break
            
            # Get file size
            file_size = os.path.getsize(font_path)
            
            # Get number of glyphs
            glyph_count = len(font.getGlyphSet())
            
            font.close()
            
            return {
                'name': font_name or font_path.stem,
                'size': file_size,
                'glyphs': glyph_count,
                'path': font_path
            }
            
        except ImportError:
            print("⚠️  fonttools not installed. Install with: pip install fonttools")
            return {
                'name': font_path.stem,
                'size': os.path.getsize(font_path),
                'glyphs': 'Unknown',
                'path': font_path
            }
        except Exception as e:
            print(f"❌ Error reading font {font_path}: {e}")
            return None
    
    def subset_font(self, font_path, output_path, unicode_ranges=None):
        """Subset font to include only necessary characters"""
        if unicode_ranges is None:
            # Arabic Unicode ranges + Latin basics + numbers
            unicode_ranges = [
                "U+0020-007F",  # Basic Latin
                "U+00A0-00FF",  # Latin-1 Supplement
                "U+0600-06FF",  # Arabic
                "U+0750-077F",  # Arabic Supplement
                "U+08A0-08FF",  # Arabic Extended-A
                "U+FB50-FDFF",  # Arabic Presentation Forms-A
                "U+FE70-FEFF",  # Arabic Presentation Forms-B
                "U+1EE00-1EEFF", # Arabic Mathematical Alphabetic Symbols
                "U+2000-206F",  # General Punctuation
                "U+2070-209F",  # Superscripts and Subscripts
                "U+20A0-20CF",  # Currency Symbols
            ]
        
        try:
            # This would require pyftsubset from fonttools
            cmd = [
                "pyftsubset",
                str(font_path),
                f"--output-file={output_path}",
                f"--unicodes={','.join(unicode_ranges)}",
                "--layout-features=*",
                "--glyph-names",
                "--symbol-cmap",
                "--legacy-cmap",
                "--notdef-glyph",
                "--notdef-outline",
                "--recommended-glyphs",
                "--name-legacy",
                "--drop-tables=",
                "--no-hinting"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                original_size = os.path.getsize(font_path)
                optimized_size = os.path.getsize(output_path)
                reduction = ((original_size - optimized_size) / original_size) * 100
                
                print(f"✅ Optimized {font_path.name}:")
                print(f"   Original: {original_size:,} bytes")
                print(f"   Optimized: {optimized_size:,} bytes")
                print(f"   Reduction: {reduction:.1f}%")
                
                return True
            else:
                print(f"❌ Error optimizing {font_path.name}: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("⚠️  pyftsubset not found. Install with: pip install fonttools")
            return False
        except Exception as e:
            print(f"❌ Error optimizing {font_path.name}: {e}")
            return False
    
    def generate_woff2(self, ttf_path, output_path):
        """Convert TTF to WOFF2 for web optimization"""
        try:
            # This would require woff2 tools
            cmd = ["woff2_compress", str(ttf_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # woff2_compress creates .woff2 file in same directory
                woff2_path = ttf_path.with_suffix('.woff2')
                if woff2_path.exists():
                    shutil.move(woff2_path, output_path)
                    
                    original_size = os.path.getsize(ttf_path)
                    woff2_size = os.path.getsize(output_path)
                    reduction = ((original_size - woff2_size) / original_size) * 100
                    
                    print(f"✅ Created WOFF2: {output_path.name}")
                    print(f"   Size reduction: {reduction:.1f}%")
                    return True
            
            return False
            
        except FileNotFoundError:
            print("⚠️  woff2_compress not found. Install woff2 tools.")
            return False
        except Exception as e:
            print(f"❌ Error creating WOFF2: {e}")
            return False
    
    def analyze_fonts(self):
        """Analyze all fonts in the directory"""
        print("🔍 Analyzing fonts...")
        print("=" * 50)
        
        font_files = list(self.fonts_dir.glob("*.ttf")) + list(self.fonts_dir.glob("*.otf"))
        
        total_size = 0
        font_info = []
        
        for font_file in font_files:
            if font_file.parent not in [self.backup_dir, self.optimized_dir]:
                info = self.get_font_info(font_file)
                if info:
                    font_info.append(info)
                    total_size += info['size']
                    
                    print(f"📝 {info['name']}")
                    print(f"   File: {font_file.name}")
                    print(f"   Size: {info['size']:,} bytes")
                    print(f"   Glyphs: {info['glyphs']}")
                    print()
        
        print(f"📊 Total fonts: {len(font_info)}")
        print(f"📊 Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        
        return font_info
    
    def optimize_all_fonts(self):
        """Optimize all fonts"""
        print("🚀 Starting font optimization...")
        print("=" * 50)
        
        self.create_directories()
        self.backup_fonts()
        
        font_files = list(self.fonts_dir.glob("*.ttf"))
        
        for font_file in font_files:
            if font_file.parent not in [self.backup_dir, self.optimized_dir]:
                print(f"\n🔧 Processing: {font_file.name}")
                
                # Subset the font
                subset_path = self.optimized_dir / f"subset_{font_file.name}"
                if self.subset_font(font_file, subset_path):
                    
                    # Generate WOFF2
                    woff2_path = self.optimized_dir / f"{font_file.stem}.woff2"
                    self.generate_woff2(subset_path, woff2_path)
    
    def generate_css(self):
        """Generate optimized CSS for fonts"""
        css_content = """/* Optimized Arabic Fonts */

/* Cairo Font */
@font-face {
    font-family: 'Cairo';
    src: url('../fonts/optimized/Cairo.woff2') format('woff2'),
         url('../fonts/optimized/subset_Cairo-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
    unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF, U+FB50-FDFF, U+FE70-FEFF, U+0020-007F;
}

/* Noto Naskh Arabic Font */
@font-face {
    font-family: 'Noto Naskh Arabic';
    src: url('../fonts/optimized/NotoNaskhArabic.woff2') format('woff2'),
         url('../fonts/optimized/subset_NotoNaskhArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
    unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF, U+FB50-FDFF, U+FE70-FEFF, U+0020-007F;
}

/* Optimized font stack */
:root {
    --font-arabic-optimized: 'Cairo', 'Noto Sans Arabic', 'Noto Naskh Arabic', system-ui, sans-serif;
    --font-arabic-traditional: 'Noto Naskh Arabic', 'Amiri', serif;
}

/* Apply optimized fonts */
body {
    font-family: var(--font-arabic-optimized);
}

.text-traditional {
    font-family: var(--font-arabic-traditional);
}
"""
        
        css_path = Path("static/css/optimized-fonts.css")
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        print(f"✅ Generated optimized CSS: {css_path}")
    
    def generate_report(self):
        """Generate optimization report"""
        report = """# Font Optimization Report

## Summary
- Fonts analyzed and optimized for Arabic text
- Subset to include only necessary Unicode ranges
- Generated WOFF2 versions for web optimization
- Created optimized CSS with font-display: swap

## Optimizations Applied
1. **Subsetting**: Removed unused glyphs
2. **WOFF2 Compression**: Better compression than TTF
3. **Unicode Ranges**: Specified ranges for better loading
4. **Font Display**: Used 'swap' for better performance

## Recommended Usage
1. Use optimized fonts for production
2. Keep original fonts as backup
3. Test thoroughly across different browsers
4. Monitor loading performance

## Next Steps
1. Implement font loading strategies
2. Add font preloading for critical fonts
3. Consider variable fonts for future updates
4. Monitor Core Web Vitals impact
"""
        
        report_path = Path("FONT_OPTIMIZATION_REPORT.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Generated report: {report_path}")

def main():
    """Main function"""
    print("🎨 Font Optimization Tool for Qatar Car Dealership")
    print("=" * 55)
    
    optimizer = FontOptimizer()
    
    print("1. Analyze fonts")
    print("2. Optimize fonts")
    print("3. Generate CSS")
    print("4. Generate report")
    print("5. Full optimization (all steps)")
    
    choice = input("\nChoose an option (1-5): ").strip()
    
    if choice == "1":
        optimizer.analyze_fonts()
    elif choice == "2":
        optimizer.optimize_all_fonts()
    elif choice == "3":
        optimizer.generate_css()
    elif choice == "4":
        optimizer.generate_report()
    elif choice == "5":
        optimizer.analyze_fonts()
        optimizer.optimize_all_fonts()
        optimizer.generate_css()
        optimizer.generate_report()
        print("\n🎉 Full optimization completed!")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
