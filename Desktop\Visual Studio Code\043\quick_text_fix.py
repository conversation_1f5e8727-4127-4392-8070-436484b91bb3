#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة النصوص
Quick fix for text visibility issues
"""

import os
import shutil
from datetime import datetime

def create_font_fix_css():
    """Create a powerful font fix CSS"""
    css_content = """
/* ULTIMATE FONT AND TEXT FIX */

/* Import Google Fonts for better Arabic support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* Force font loading and visibility */
* {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #212529 !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inherit !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

html {
    font-size: 16px !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #212529 !important;
    background-color: #f8f9fa !important;
}

/* Headers with proper sizing */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-weight: 600 !important;
    color: inherit !important;
    margin-bottom: 0.5rem !important;
}

h1 { font-size: 2.5rem !important; }
h2 { font-size: 2rem !important; }
h3 { font-size: 1.75rem !important; }
h4 { font-size: 1.5rem !important; }
h5 { font-size: 1.25rem !important; }
h6 { font-size: 1rem !important; }

/* Paragraphs and text */
p {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    color: inherit !important;
    margin-bottom: 1rem !important;
}

/* Sidebar specific fixes */
.sidebar {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: white !important;
}

.sidebar * {
    color: white !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.sidebar h4 {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: white !important;
}

.sidebar .nav-link {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: rgba(255,255,255,0.9) !important;
    padding: 12px 20px !important;
    display: block !important;
    text-decoration: none !important;
}

.sidebar .nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1) !important;
}

.sidebar .nav-link i {
    font-size: 1rem !important;
    width: 20px !important;
    margin-left: 10px !important;
    color: inherit !important;
}

/* Card fixes */
.card-header {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: white !important;
}

.card-header * {
    color: white !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.card-body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

.card-body * {
    color: #212529 !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

/* Button fixes */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    padding: 10px 20px !important;
    border-radius: 8px !important;
}

.btn * {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: inherit !important;
}

/* Form fixes */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: #212529 !important;
}

.form-control,
.form-select {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: #212529 !important;
}

/* Table fixes */
.table {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.table th,
.table td {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: #212529 !important;
}

/* Icon fixes */
i,
.fa,
.fas,
.far,
.fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    font-size: 1rem !important;
    color: inherit !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-block !important;
}

/* Force text to be visible */
* {
    text-indent: 0 !important;
    letter-spacing: normal !important;
    word-spacing: normal !important;
    white-space: normal !important;
    text-transform: none !important;
    text-shadow: none !important;
    outline: none !important;
}

/* Ensure proper contrast */
.text-white,
.text-white * {
    color: white !important;
}

.text-dark,
.text-dark * {
    color: #212529 !important;
}

/* Main content area */
.main-content {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

.main-content * {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

/* Override any hiding styles */
.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

/* Ensure all text is selectable */
* {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Force font loading */
@font-face {
    font-family: 'Fallback Arabic';
    src: local('Segoe UI'), local('Tahoma'), local('Arial');
    font-display: swap;
}

/* Backup font stack */
.backup-font {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Arial', 'Fallback Arabic', sans-serif !important;
}

/* Apply backup font to everything */
* {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
}
"""
    
    with open('static/css/font-fix.css', 'w', encoding='utf-8') as f:
        f.write(css_content)
    
    print("✅ تم إنشاء ملف إصلاح الخطوط")

def update_base_template():
    """Update base template with font fix"""
    base_path = 'templates/base.html'
    
    if not os.path.exists(base_path):
        print("❌ ملف base.html غير موجود")
        return False
    
    # Read current content
    with open(base_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add font fix CSS if not already added
    if 'font-fix.css' not in content:
        # Find the CSS section and add our fix
        css_insert = '    <!-- Font Fix CSS -->\n    <link href="{{ url_for(\'static\', filename=\'css/font-fix.css\') }}" rel="stylesheet">\n'
        
        if '<!-- Text Fix CSS -->' in content:
            content = content.replace(
                '<!-- Text Fix CSS -->',
                css_insert + '    <!-- Text Fix CSS -->'
            )
        else:
            # Insert before closing head tag
            content = content.replace('</head>', f'    {css_insert}</head>')
        
        # Create backup
        backup_path = f"{base_path}.backup_font_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(base_path, backup_path)
        
        # Write updated content
        with open(base_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم تحديث {base_path}")
        print(f"💾 نسخة احتياطية: {backup_path}")
    else:
        print("✅ ملف إصلاح الخطوط موجود بالفعل")
    
    return True

def main():
    """Main function"""
    print("🔤 إصلاح سريع لمشكلة النصوص والخطوط")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ يجب تشغيل هذا السكريبت من مجلد المشروع")
        return False
    
    # Create directories if needed
    os.makedirs('static/css', exist_ok=True)
    
    # Create font fix CSS
    try:
        create_font_fix_css()
    except Exception as e:
        print(f"❌ خطأ في إنشاء CSS: {str(e)}")
        return False
    
    # Update base template
    try:
        update_base_template()
    except Exception as e:
        print(f"❌ خطأ في تحديث القالب: {str(e)}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 تم تطبيق إصلاح الخطوط بنجاح!")
    print("🌐 اختبر النتيجة على:")
    print("   • اختبار النصوص: http://localhost:5000/text-test")
    print("   • اختبار التخطيط: http://localhost:5000/layout-test")
    print("   • الصفحة الرئيسية: http://localhost:5000")
    print("\n💡 إذا لم تظهر النصوص:")
    print("   1. أعد تحميل الصفحة (Ctrl+F5)")
    print("   2. امسح التخزين المؤقت")
    print("   3. تأكد من اتصال الإنترنت (للخطوط)")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    main()
