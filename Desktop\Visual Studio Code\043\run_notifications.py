#!/usr/bin/env python3
"""
Run Notifications Script for Qatar Car Dealership System
Creates sample notifications and runs the notification scheduler
"""

import os
import sys
from datetime import datetime, date, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Sale, Customer, Car, Installment
from notifications import NotificationManager

def create_sample_notifications():
    """Create sample notifications for testing"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔔 إنشاء إشعارات تجريبية...")
            
            # Get users
            users = User.query.all()
            if not users:
                print("❌ لا يوجد مستخدمون في النظام")
                return
            
            user = users[0]  # Use first user
            
            # Create welcome notification
            NotificationManager.create_notification(
                user.id,
                "مرحباً بك في نظام الإشعارات",
                """🎉 تم تفعيل نظام الإشعارات بنجاح!

الميزات الجديدة:
• إشعارات فورية للأقساط المتأخرة
• تذكيرات بالمواعيد المستحقة
• إشعارات المبيعات الجديدة
• تحديثات النظام

يمكنك الآن متابعة جميع الأنشطة المهمة من خلال قسم الإشعارات.""",
                'success'
            )
            
            # Create payment reminder notification
            NotificationManager.create_notification(
                user.id,
                "تذكير - فحص الأقساط المستحقة",
                """⏰ تذكير يومي

يرجى مراجعة الأقساط المستحقة اليوم:
• فحص الأقساط المتأخرة
• متابعة العملاء
• تحديث حالات الدفع

للوصول السريع: قسم المبيعات > الأقساط المتأخرة""",
                'warning'
            )
            
            # Create system notification
            NotificationManager.create_notification(
                user.id,
                "تحديث النظام",
                """🔧 تم تحديث النظام بنجاح

التحسينات الجديدة:
• تحسين الخطوط العربية في العقود
• تفعيل قوالب الواتساب
• نظام الإشعارات الجديد
• تحسينات الأداء

جميع الميزات متاحة الآن للاستخدام.""",
                'info'
            )
            
            # Check for real overdue installments
            today = date.today()
            overdue_installments = Installment.query.join(Sale).join(Customer).filter(
                Installment.due_date < today,
                Installment.status == 'pending'
            ).limit(3).all()
            
            for installment in overdue_installments:
                days_overdue = (today - installment.due_date).days
                NotificationManager.create_notification(
                    user.id,
                    f"قسط متأخر - {installment.sale.customer.full_name}",
                    f"""⚠️ قسط متأخر يحتاج متابعة

تفاصيل القسط:
• العميل: {installment.sale.customer.full_name}
• رقم الهاتف: {installment.sale.customer.phone}
• رقم القسط: {installment.installment_number}
• تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
• عدد أيام التأخير: {days_overdue} يوم
• المبلغ: {installment.amount:,.0f} ريال قطري

يرجى التواصل مع العميل لتحصيل القسط.""",
                    'error'
                )
            
            print(f"✅ تم إنشاء {4 + len(overdue_installments)} إشعار تجريبي")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الإشعارات التجريبية: {e}")

def test_notification_system():
    """Test the notification system"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🧪 اختبار نظام الإشعارات...")
            
            # Test notification creation
            users = User.query.all()
            if users:
                user = users[0]
                
                notification = NotificationManager.create_notification(
                    user.id,
                    "اختبار النظام",
                    "هذا إشعار اختبار للتأكد من عمل النظام بشكل صحيح.",
                    'info'
                )
                
                if notification:
                    print("✅ تم إنشاء إشعار اختبار بنجاح")
                else:
                    print("❌ فشل في إنشاء إشعار الاختبار")
            
            # Test system notification
            count = NotificationManager.create_system_notification(
                "إشعار عام للنظام",
                "هذا إشعار عام لجميع المستخدمين في النظام.",
                'info'
            )
            
            print(f"✅ تم إنشاء إشعار عام لـ {count} مستخدم")
            
            # Test overdue check
            from notifications import check_overdue_payments
            success = check_overdue_payments()
            
            if success:
                print("✅ تم فحص الأقساط المتأخرة بنجاح")
            else:
                print("⚠️ لم يتم العثور على أقساط متأخرة")
            
            print("🎉 اختبار النظام مكتمل!")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار النظام: {e}")

def show_notification_stats():
    """Show notification statistics"""
    app = create_app()
    
    with app.app_context():
        try:
            from models import Notification
            
            print("📊 إحصائيات الإشعارات:")
            print("=" * 30)
            
            # Total notifications
            total = Notification.query.count()
            print(f"إجمالي الإشعارات: {total}")
            
            # Unread notifications
            unread = Notification.query.filter_by(is_read=False).count()
            print(f"الإشعارات غير المقروءة: {unread}")
            
            # Notifications by type
            types = ['info', 'success', 'warning', 'error']
            for notification_type in types:
                count = Notification.query.filter_by(type=notification_type).count()
                print(f"إشعارات {notification_type}: {count}")
            
            # Today's notifications
            today = date.today()
            today_count = Notification.query.filter(
                Notification.created_at >= today
            ).count()
            print(f"إشعارات اليوم: {today_count}")
            
            # Notifications by user
            print("\nالإشعارات حسب المستخدم:")
            users = User.query.all()
            for user in users:
                user_notifications = Notification.query.filter_by(user_id=user.id).count()
                user_unread = Notification.query.filter_by(user_id=user.id, is_read=False).count()
                print(f"  {user.full_name}: {user_notifications} (غير مقروءة: {user_unread})")
            
        except Exception as e:
            print(f"❌ خطأ في عرض الإحصائيات: {e}")

def cleanup_notifications():
    """Clean up old notifications"""
    app = create_app()
    
    with app.app_context():
        try:
            from models import Notification
            
            print("🧹 تنظيف الإشعارات القديمة...")
            
            # Delete read notifications older than 30 days
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            deleted_count = Notification.query.filter(
                Notification.is_read == True,
                Notification.created_at < cutoff_date
            ).delete()
            
            db.session.commit()
            
            print(f"✅ تم حذف {deleted_count} إشعار قديم")
            
        except Exception as e:
            print(f"❌ خطأ في تنظيف الإشعارات: {e}")

def main():
    """Main function"""
    print("🔔 إدارة الإشعارات - معرض بوخليفة للسيارات")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'create':
            create_sample_notifications()
        elif command == 'test':
            test_notification_system()
        elif command == 'stats':
            show_notification_stats()
        elif command == 'cleanup':
            cleanup_notifications()
        elif command == 'schedule':
            from notification_scheduler import NotificationScheduler
            scheduler = NotificationScheduler()
            scheduler.run_scheduler()
        else:
            print("❌ أمر غير معروف")
            print_usage()
    else:
        print_usage()

def print_usage():
    """Print usage instructions"""
    print("\nالاستخدام:")
    print("  python run_notifications.py create   - إنشاء إشعارات تجريبية")
    print("  python run_notifications.py test     - اختبار النظام")
    print("  python run_notifications.py stats    - عرض الإحصائيات")
    print("  python run_notifications.py cleanup  - تنظيف الإشعارات القديمة")
    print("  python run_notifications.py schedule - تشغيل الجدولة")
    print("\nأمثلة:")
    print("  python run_notifications.py create")
    print("  python run_notifications.py stats")

if __name__ == "__main__":
    main()
