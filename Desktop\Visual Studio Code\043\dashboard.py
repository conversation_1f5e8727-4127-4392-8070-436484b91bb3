from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from models import db, Car, Customer, Sale, Installment, Payment, Notification
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, or_

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    # Get dashboard statistics
    stats = get_dashboard_stats()
    
    # Get recent activities
    recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(5).all()
    
    # Get overdue installments
    today = date.today()
    overdue_installments = Installment.query.filter(
        and_(
            Installment.due_date < today,
            Installment.status == 'pending'
        )
    ).order_by(Installment.due_date.asc()).limit(10).all()
    
    # Get installments due this month
    start_of_month = today.replace(day=1)
    if today.month == 12:
        end_of_month = date(today.year + 1, 1, 1) - timedelta(days=1)
    else:
        end_of_month = date(today.year, today.month + 1, 1) - timedelta(days=1)
    
    monthly_installments = Installment.query.filter(
        and_(
            Installment.due_date >= start_of_month,
            Installment.due_date <= end_of_month,
            Installment.status == 'pending'
        )
    ).order_by(Installment.due_date.asc()).all()
    
    # Get notifications
    notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).order_by(Notification.created_at.desc()).limit(5).all()
    
    return render_template('dashboard/index.html',
                         stats=stats,
                         recent_sales=recent_sales,
                         overdue_installments=overdue_installments,
                         monthly_installments=monthly_installments,
                         notifications=notifications)

@dashboard_bp.route('/stats')
@login_required
def stats():
    """API endpoint for dashboard statistics"""
    return jsonify(get_dashboard_stats())

def get_dashboard_stats():
    """Calculate dashboard statistics"""
    today = date.today()
    start_of_month = today.replace(day=1)
    start_of_year = today.replace(month=1, day=1)
    
    # Car statistics
    total_cars = Car.query.count()
    available_cars = Car.query.filter_by(status='available').count()
    sold_cars = Car.query.filter_by(status='sold').count()
    reserved_cars = Car.query.filter_by(status='reserved').count()
    
    # Customer statistics
    total_customers = Customer.query.count()
    new_customers_this_month = Customer.query.filter(
        Customer.created_at >= start_of_month
    ).count()
    
    # Sales statistics
    total_sales = Sale.query.count()
    sales_this_month = Sale.query.filter(
        Sale.sale_date >= start_of_month
    ).count()
    
    sales_this_year = Sale.query.filter(
        Sale.sale_date >= start_of_year
    ).count()
    
    # Revenue statistics
    total_revenue = db.session.query(func.sum(Sale.sale_price)).scalar() or 0
    
    monthly_revenue = db.session.query(func.sum(Sale.sale_price)).filter(
        Sale.sale_date >= start_of_month
    ).scalar() or 0
    
    yearly_revenue = db.session.query(func.sum(Sale.sale_price)).filter(
        Sale.sale_date >= start_of_year
    ).scalar() or 0
    
    # Installment statistics
    total_installments = Installment.query.count()
    pending_installments = Installment.query.filter_by(status='pending').count()
    overdue_installments = Installment.query.filter(
        and_(
            Installment.due_date < today,
            Installment.status == 'pending'
        )
    ).count()
    
    # Monthly installments due
    if today.month == 12:
        end_of_month = date(today.year + 1, 1, 1) - timedelta(days=1)
    else:
        end_of_month = date(today.year, today.month + 1, 1) - timedelta(days=1)
    
    monthly_installments_due = Installment.query.filter(
        and_(
            Installment.due_date >= start_of_month,
            Installment.due_date <= end_of_month,
            Installment.status == 'pending'
        )
    ).count()
    
    monthly_installments_amount = db.session.query(func.sum(Installment.amount)).filter(
        and_(
            Installment.due_date >= start_of_month,
            Installment.due_date <= end_of_month,
            Installment.status == 'pending'
        )
    ).scalar() or 0
    
    # Payment statistics
    total_payments = Payment.query.count()
    payments_this_month = Payment.query.filter(
        Payment.payment_date >= start_of_month
    ).count()
    
    monthly_payments_amount = db.session.query(func.sum(Payment.amount)).filter(
        Payment.payment_date >= start_of_month
    ).scalar() or 0
    
    # Cash flow statistics
    cash_sales_amount = db.session.query(func.sum(Sale.sale_price)).filter(
        and_(
            Sale.sale_type == 'cash',
            Sale.sale_date >= start_of_month
        )
    ).scalar() or 0
    
    down_payments_amount = db.session.query(func.sum(Sale.down_payment)).filter(
        Sale.sale_date >= start_of_month
    ).scalar() or 0
    
    monthly_cash_flow = cash_sales_amount + down_payments_amount + monthly_payments_amount
    
    return {
        'cars': {
            'total': total_cars,
            'available': available_cars,
            'sold': sold_cars,
            'reserved': reserved_cars
        },
        'customers': {
            'total': total_customers,
            'new_this_month': new_customers_this_month
        },
        'sales': {
            'total': total_sales,
            'this_month': sales_this_month,
            'this_year': sales_this_year
        },
        'revenue': {
            'total': total_revenue,
            'monthly': monthly_revenue,
            'yearly': yearly_revenue
        },
        'installments': {
            'total': total_installments,
            'pending': pending_installments,
            'overdue': overdue_installments,
            'monthly_due': monthly_installments_due,
            'monthly_amount': monthly_installments_amount
        },
        'payments': {
            'total': total_payments,
            'this_month': payments_this_month,
            'monthly_amount': monthly_payments_amount
        },
        'cash_flow': {
            'monthly': monthly_cash_flow,
            'cash_sales': cash_sales_amount,
            'down_payments': down_payments_amount,
            'installment_payments': monthly_payments_amount
        }
    }

@dashboard_bp.route('/notifications')
@login_required
def notifications():
    """Get user notifications"""
    page = request.args.get('page', 1, type=int)
    notifications = Notification.query.filter_by(
        user_id=current_user.id
    ).order_by(Notification.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('dashboard/notifications.html', notifications=notifications)

@dashboard_bp.route('/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Mark notification as read"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=current_user.id
    ).first_or_404()
    
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})

@dashboard_bp.route('/notifications/mark_all_read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).update({'is_read': True})
    
    db.session.commit()
    
    return jsonify({'success': True})

@dashboard_bp.route('/charts/sales')
@login_required
def sales_chart():
    """Get sales chart data"""
    # Get sales data for the last 12 months
    today = date.today()
    months_data = []
    
    for i in range(12):
        if today.month - i <= 0:
            month = today.month - i + 12
            year = today.year - 1
        else:
            month = today.month - i
            year = today.year
        
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        sales_count = Sale.query.filter(
            and_(
                Sale.sale_date >= start_date,
                Sale.sale_date <= end_date
            )
        ).count()
        
        revenue = db.session.query(func.sum(Sale.sale_price)).filter(
            and_(
                Sale.sale_date >= start_date,
                Sale.sale_date <= end_date
            )
        ).scalar() or 0
        
        months_data.append({
            'month': f"{year}-{month:02d}",
            'sales': sales_count,
            'revenue': float(revenue)
        })
    
    return jsonify(list(reversed(months_data)))

@dashboard_bp.route('/charts/installments')
@login_required
def installments_chart():
    """Get installments chart data"""
    today = date.today()
    
    # Get installments data for the next 6 months
    months_data = []
    
    for i in range(6):
        if today.month + i > 12:
            month = today.month + i - 12
            year = today.year + 1
        else:
            month = today.month + i
            year = today.year
        
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        installments_count = Installment.query.filter(
            and_(
                Installment.due_date >= start_date,
                Installment.due_date <= end_date
            )
        ).count()
        
        amount = db.session.query(func.sum(Installment.amount)).filter(
            and_(
                Installment.due_date >= start_date,
                Installment.due_date <= end_date
            )
        ).scalar() or 0
        
        months_data.append({
            'month': f"{year}-{month:02d}",
            'installments': installments_count,
            'amount': float(amount)
        })
    
    return jsonify(months_data)
