{% extends "base.html" %}

{% block title %}اختبار إدخال الأرقام - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-keyboard me-2"></i>
                        اختبار إدخال الأرقام والتحقق من صحة البيانات
                    </h3>
                </div>
                <div class="card-body">
                    
                    <!-- Input Testing Form -->
                    <form id="testForm" novalidate>
                        <div class="row">
                            
                            <!-- Currency Inputs -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>إدخال العملة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="price1" class="form-label">سعر السيارة <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control currency-input" id="price1"
                                                       name="price1" data-format="currency" required
                                                       placeholder="أدخل السعر">
                                                <span class="input-group-text">ريال قطري</span>
                                            </div>
                                            <div class="form-text">جرب كتابة: 50000 أو ٥٠٠٠٠ أو 50,000</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="salary" class="form-label">الراتب الشهري</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control currency-input" id="salary"
                                                       name="salary" data-format="currency"
                                                       placeholder="أدخل الراتب">
                                                <span class="input-group-text">ريال قطري</span>
                                            </div>
                                            <div class="form-text">جرب كتابة: 15000.50 أو ١٥٠٠٠.٥٠</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="downPayment" class="form-label">الدفعة المقدمة</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control currency-input" id="downPayment"
                                                       name="downPayment" data-format="currency"
                                                       placeholder="أدخل الدفعة المقدمة">
                                                <span class="input-group-text">ريال قطري</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Number Inputs -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>إدخال الأرقام</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="year" class="form-label">سنة الصنع <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="year" name="year"
                                                   min="1990" max="2025" required
                                                   placeholder="أدخل سنة الصنع">
                                            <div class="form-text">جرب كتابة: 2023 أو ٢٠٢٣</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="mileage" class="form-label">المسافة المقطوعة (كم)</label>
                                            <input type="number" class="form-control" id="mileage" name="mileage"
                                                   min="0" placeholder="أدخل المسافة">
                                            <div class="form-text">جرب كتابة: 50000 أو ٥٠٠٠٠</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="installments" class="form-label">عدد الأقساط</label>
                                            <input type="number" class="form-control" id="installments" name="installments"
                                                   min="1" max="60" placeholder="أدخل عدد الأقساط">
                                            <div class="form-text">من 1 إلى 60 قسط</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Phone and ID Inputs -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>أرقام الهاتف والهوية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control phone-input" id="phone" name="phone"
                                                   data-format="phone" maxlength="8" required
                                                   placeholder="أدخل رقم الهاتف">
                                            <div class="form-text">8 أرقام - جرب: 55123456 أو ٥٥١٢٣٤٥٦</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="whatsapp" class="form-label">رقم الواتساب</label>
                                            <input type="text" class="form-control phone-input" id="whatsapp" name="whatsapp"
                                                   data-format="phone" maxlength="8"
                                                   placeholder="أدخل رقم الواتساب">
                                            <div class="form-text">8 أرقام - يبدأ بـ 3، 4، 5، 6، أو 7</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="nationalId" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control id-input" id="nationalId" name="nationalId"
                                                   data-format="qatar-id" maxlength="11" required
                                                   placeholder="أدخل رقم الهوية">
                                            <div class="form-text">11 رقم - جرب: 12345678901 أو ١٢٣٤٥٦٧٨٩٠١</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Mixed Inputs -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>مدخلات مختلطة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="chassisNumber" class="form-label">رقم الشاسيه <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="chassisNumber" name="chassisNumber"
                                                   required placeholder="أدخل رقم الشاسيه">
                                            <div class="form-text">أحرف وأرقام - مثال: ABC123456789</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="plateNumber" class="form-label">رقم اللوحة</label>
                                            <input type="text" class="form-control" id="plateNumber" name="plateNumber"
                                                   placeholder="أدخل رقم اللوحة">
                                            <div class="form-text">مثال: 123456 أو ABC123</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="engineSize" class="form-label">حجم المحرك</label>
                                            <input type="text" class="form-control" id="engineSize" name="engineSize"
                                                   placeholder="أدخل حجم المحرك">
                                            <div class="form-text">مثال: 2.5L أو 3.0L</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>إجراءات الاختبار</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <button type="button" class="btn btn-primary mb-2 me-2" onclick="validateForm()">
                                                    <i class="fas fa-check me-2"></i>
                                                    فحص النموذج
                                                </button>
                                                <button type="button" class="btn btn-secondary mb-2 me-2" onclick="clearForm()">
                                                    <i class="fas fa-eraser me-2"></i>
                                                    مسح النموذج
                                                </button>
                                                <button type="button" class="btn btn-info mb-2 me-2" onclick="fillSampleData()">
                                                    <i class="fas fa-fill me-2"></i>
                                                    بيانات تجريبية
                                                </button>
                                                <button type="submit" class="btn btn-success mb-2">
                                                    <i class="fas fa-save me-2"></i>
                                                    حفظ (اختبار)
                                                </button>
                                            </div>
                                            <div class="col-md-6">
                                                <div id="validationResults" class="alert alert-info">
                                                    <strong>نتائج الفحص:</strong>
                                                    <div id="validationOutput">انقر على "فحص النموذج" لاختبار التحقق من صحة البيانات</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </form>
                    
                    <!-- Instructions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>تعليمات الاختبار</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-money-bill-wave me-2 text-success"></i>العملة:</h6>
                                            <ul>
                                                <li>يقبل الأرقام العربية والإنجليزية</li>
                                                <li>يتم تنسيق المبلغ تلقائياً بالفواصل</li>
                                                <li>يدعم الأرقام العشرية (مثال: 1500.50)</li>
                                                <li>لا يقبل القيم السالبة</li>
                                            </ul>
                                            
                                            <h6><i class="fas fa-phone me-2 text-primary"></i>الهاتف:</h6>
                                            <ul>
                                                <li>8 أرقام فقط للأرقام القطرية</li>
                                                <li>يجب أن يبدأ بـ 3، 4، 5، 6، أو 7</li>
                                                <li>يتم تحويل الأرقام العربية تلقائياً</li>
                                                <li>لا يقبل الأحرف أو الرموز</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-id-card me-2 text-warning"></i>الهوية:</h6>
                                            <ul>
                                                <li>11 رقم بالضبط للهوية القطرية</li>
                                                <li>يتم تحويل الأرقام العربية تلقائياً</li>
                                                <li>لا يقبل الأحرف أو الرموز</li>
                                                <li>فحص تلقائي للطول الصحيح</li>
                                            </ul>
                                            
                                            <h6><i class="fas fa-hashtag me-2 text-info"></i>الأرقام العامة:</h6>
                                            <ul>
                                                <li>يدعم الحد الأدنى والأقصى</li>
                                                <li>فحص تلقائي للنطاق المسموح</li>
                                                <li>تحويل الأرقام العربية للإنجليزية</li>
                                                <li>رسائل خطأ واضحة بالعربية</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation function
function validateForm() {
    const form = document.getElementById('testForm');
    let output = '<strong>نتائج فحص النموذج:</strong><br>';
    let isValid = true;
    
    // Use InputValidator if available
    if (window.inputValidator) {
        isValid = window.inputValidator.validateForm(form);
        
        if (isValid) {
            output += '<span class="text-success">✅ جميع الحقول صحيحة!</span><br>';
        } else {
            output += '<span class="text-danger">❌ يوجد أخطاء في النموذج</span><br>';
        }
        
        // Show individual field values
        const fields = [
            { id: 'price1', label: 'سعر السيارة' },
            { id: 'salary', label: 'الراتب' },
            { id: 'phone', label: 'الهاتف' },
            { id: 'nationalId', label: 'الهوية' },
            { id: 'year', label: 'السنة' }
        ];
        
        fields.forEach(field => {
            const input = document.getElementById(field.id);
            if (input && input.value) {
                const value = input.value;
                const isFieldValid = !input.classList.contains('is-invalid');
                const status = isFieldValid ? '✅' : '❌';
                output += `${status} ${field.label}: ${value}<br>`;
            }
        });
        
    } else {
        output += '<span class="text-warning">⚠️ InputValidator غير متاح</span>';
    }
    
    document.getElementById('validationOutput').innerHTML = output;
}

// Clear form function
function clearForm() {
    const form = document.getElementById('testForm');
    form.reset();
    
    // Clear all validation errors
    form.querySelectorAll('.is-invalid').forEach(input => {
        input.classList.remove('is-invalid');
    });
    
    form.querySelectorAll('.invalid-feedback').forEach(error => {
        error.remove();
    });
    
    document.getElementById('validationOutput').innerHTML = 'تم مسح النموذج';
}

// Fill sample data function
function fillSampleData() {
    document.getElementById('price1').value = '75000';
    document.getElementById('salary').value = '15000';
    document.getElementById('downPayment').value = '20000';
    document.getElementById('year').value = '2023';
    document.getElementById('mileage').value = '25000';
    document.getElementById('installments').value = '24';
    document.getElementById('phone').value = '55123456';
    document.getElementById('whatsapp').value = '66789012';
    document.getElementById('nationalId').value = '12345678901';
    document.getElementById('chassisNumber').value = 'ABC123456789';
    document.getElementById('plateNumber').value = '123456';
    document.getElementById('engineSize').value = '2.5L';
    
    // Trigger input events to format the values
    document.querySelectorAll('#testForm input').forEach(input => {
        input.dispatchEvent(new Event('input'));
        input.dispatchEvent(new Event('blur'));
    });
    
    document.getElementById('validationOutput').innerHTML = 'تم ملء البيانات التجريبية';
}

// Form submit handler
document.getElementById('testForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    let output = '<strong>محاولة إرسال النموذج:</strong><br>';
    
    if (window.inputValidator && window.inputValidator.validateForm(this)) {
        output += '<span class="text-success">✅ النموذج صحيح - يمكن الإرسال</span><br>';
        
        // Show form data
        const formData = new FormData(this);
        output += '<strong>البيانات المرسلة:</strong><br>';
        for (let [key, value] of formData.entries()) {
            if (value) {
                output += `${key}: ${value}<br>`;
            }
        }
    } else {
        output += '<span class="text-danger">❌ النموذج يحتوي على أخطاء - لا يمكن الإرسال</span>';
    }
    
    document.getElementById('validationOutput').innerHTML = output;
});

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for InputValidator to load
    setTimeout(() => {
        if (window.inputValidator) {
            console.log('✅ InputValidator loaded successfully');
            document.getElementById('validationOutput').innerHTML = 'InputValidator جاهز للاستخدام';
        } else {
            console.error('❌ InputValidator not loaded');
            document.getElementById('validationOutput').innerHTML = 'خطأ: InputValidator غير متاح';
        }
    }, 1000);
});
</script>
{% endblock %}
