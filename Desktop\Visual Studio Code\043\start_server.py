#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Server Starter for Car Dealership System
معرض بوخليفة للسيارات - تشغيل مبسط للخادم
"""

import os
import sys

def main():
    """Main function to start the server"""
    print("🚗 معرض بوخليفة للسيارات - Car Dealership System")
    print("🚀 Starting server...")
    
    try:
        # Import and run the app
        import app
        
        print("✅ Application loaded successfully")
        print("🌐 Server running on: http://localhost:1212")
        print("📊 Dashboard: http://localhost:1212/")
        print("🔧 Test page: http://localhost:1212/test")
        print("🩺 Diagnosis: http://localhost:1212/diagnosis")
        print("📋 Minimal: http://localhost:1212/minimal")
        print("\n⚠️  Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Run the Flask app
        app.app.run(
            host='0.0.0.0',
            port=1212,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure all dependencies are installed:")
        print("pip install flask flask-sqlalchemy flask-login flask-wtf")
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("Try running: python app.py")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
