#!/usr/bin/env python3
"""
Test Car Price Fix
"""

import os
import sys

def test_car_add_file():
    """Test if cars/add.html has the price fix"""
    print("🔍 Testing cars/add.html for price fix...")
    
    try:
        with open('templates/cars/add.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for key components
        checks = [
            ('setupPriceInput', 'Price input setup function'),
            ('setupCostPriceInput', 'Cost price input setup function'),
            ('DIRECT PRICE INPUT FIX', 'Direct fix comment'),
            ('toLocaleString', 'Number formatting'),
            ('٠١٢٣٤٥٦٧٨٩', 'Arabic digit conversion')
        ]
        
        all_good = True
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - NOT FOUND")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error reading cars/add.html: {e}")
        return False

def test_car_price_test_file():
    """Test if car-price-test.html exists"""
    print("\n🔍 Testing car-price-test.html...")
    
    if os.path.exists('templates/car-price-test.html'):
        print("✅ Car price test page exists")
        return True
    else:
        print("❌ Car price test page missing")
        return False

def test_app_route():
    """Test if app.py has the car-price-test route"""
    print("\n🔍 Testing app.py for car-price-test route...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '/car-price-test' in content and 'car_price_test' in content:
            print("✅ Car price test route exists")
            return True
        else:
            print("❌ Car price test route missing")
            return False
            
    except Exception as e:
        print(f"❌ Error reading app.py: {e}")
        return False

def test_app_import():
    """Test if app can be imported"""
    print("\n🔍 Testing app import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app
        app = create_app()
        
        print("✅ App created successfully")
        print(f"✅ App name: {app.name}")
        
        # Test routes
        with app.test_client() as client:
            # Test car price test route
            response = client.get('/car-price-test')
            if response.status_code == 302:  # Redirect to login
                print("✅ Car price test route works (redirects to login)")
            else:
                print(f"⚠️ Car price test route status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ App import error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 اختبار حل أسعار السيارات")
    print("=" * 60)
    
    tests = [
        test_car_add_file,
        test_car_price_test_file,
        test_app_route,
        test_app_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📱 للاختبار الفعلي:")
        print("1. شغل الخادم: python quick_start.py")
        print("2. اذهب إلى: http://localhost:5000/car-price-test")
        print("3. جرب البيانات التجريبية")
        print("4. اذهب إلى: http://localhost:5000/cars/add")
        print("5. جرب إدخال سعر البيع: 75000")
        print("6. يجب أن يصبح: 75,000")
        print("\n💰 اختبار سريع:")
        print("- أدخل 50000 في حقل سعر البيع")
        print("- اضغط Tab أو انقر خارج الحقل")
        print("- يجب أن يصبح 50,000")
        return True
    else:
        print("❌ بعض الاختبارات فشلت.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للمتابعة...")
    sys.exit(0 if success else 1)
