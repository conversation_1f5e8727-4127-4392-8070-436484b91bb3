#!/usr/bin/env python3
"""
Test Price Reset Functions
"""

import os
import sys

def test_price_reset_functions():
    """Test if price reset functions are added to cars/add.html"""
    print("🔍 Testing price reset functions in cars/add.html...")
    
    try:
        with open('templates/cars/add.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for key components
        checks = [
            ('resetPrice()', 'Reset price function'),
            ('resetCostPrice()', 'Reset cost price function'),
            ('setSamplePrice()', 'Set sample price function'),
            ('setSampleCostPrice()', 'Set sample cost price function'),
            ('resetPricesOnly()', 'Reset prices only function'),
            ('resetFormCompletely()', 'Reset form completely function'),
            ('showNotification()', 'Show notification function'),
            ('onclick="resetPrice()"', 'Reset price button'),
            ('onclick="resetCostPrice()"', 'Reset cost price button'),
            ('onclick="resetPricesOnly()"', 'Reset prices only button'),
            ('onclick="resetFormCompletely()"', 'Reset form completely button'),
            ('استخدام سعر تجريبي', 'Sample price text'),
            ('إعادة تعيين الأسعار', 'Reset prices text'),
            ('fas fa-undo', 'Undo icon'),
            ('fas fa-magic', 'Magic icon'),
            ('btn-outline-warning', 'Warning button style'),
            ('confirm(', 'Confirmation dialog')
        ]
        
        all_good = True
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - NOT FOUND")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error reading cars/add.html: {e}")
        return False

def test_guide_file():
    """Test if guide file exists"""
    print("\n🔍 Testing PRICE_RESET_GUIDE.md...")
    
    if os.path.exists('PRICE_RESET_GUIDE.md'):
        print("✅ Price reset guide exists")
        
        try:
            with open('PRICE_RESET_GUIDE.md', 'r', encoding='utf-8') as f:
                content = f.read()
                
            if len(content) > 1000:
                print("✅ Guide has substantial content")
                return True
            else:
                print("⚠️ Guide content seems short")
                return False
                
        except Exception as e:
            print(f"❌ Error reading guide: {e}")
            return False
    else:
        print("❌ Price reset guide missing")
        return False

def test_app_import():
    """Test if app can be imported"""
    print("\n🔍 Testing app import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app
        app = create_app()
        
        print("✅ App created successfully")
        print(f"✅ App name: {app.name}")
        
        # Test routes
        with app.test_client() as client:
            # Test cars add route
            response = client.get('/cars/add')
            if response.status_code == 302:  # Redirect to login
                print("✅ Cars add route works (redirects to login)")
            else:
                print(f"⚠️ Cars add route status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ App import error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 اختبار وظائف إعادة تعيين الأسعار")
    print("=" * 60)
    
    tests = [
        test_price_reset_functions,
        test_guide_file,
        test_app_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📱 للاختبار الفعلي:")
        print("1. شغل الخادم: python quick_start.py")
        print("2. اذهب إلى: http://localhost:5000/cars/add")
        print("3. جرب الوظائف التالية:")
        print("   - أدخل سعر بيع واضغط زر الإعادة بجانبه")
        print("   - اضغط 'استخدام سعر تجريبي' تحت الحقل")
        print("   - اضغط 'إعادة تعيين الأسعار' (الزر الأصفر)")
        print("   - اضغط 'إعادة تعيين النموذج بالكامل' (الزر الأحمر)")
        print("\n💡 الوظائف المتاحة:")
        print("   ⟲ إعادة تعيين حقل واحد")
        print("   🎯 أسعار تجريبية (75,000 و 65,000)")
        print("   🗑️ إعادة تعيين جميع الأسعار")
        print("   🔄 إعادة تعيين النموذج بالكامل")
        print("   ✨ إشعارات تأكيد")
        return True
    else:
        print("❌ بعض الاختبارات فشلت.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للمتابعة...")
    sys.exit(0 if success else 1)
