from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Car, Sale
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime

cars_bp = Blueprint('cars', __name__)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@cars_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    brand = request.args.get('brand', '')
    
    query = Car.query
    
    if search:
        query = query.filter(
            Car.name.contains(search) |
            Car.brand.contains(search) |
            Car.model.contains(search) |
            Car.chassis_number.contains(search) |
            Car.plate_number.contains(search)
        )
    
    if status:
        query = query.filter(Car.status == status)
    
    if brand:
        query = query.filter(Car.brand == brand)
    
    cars = query.order_by(Car.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    # Get unique brands for filter
    brands = db.session.query(Car.brand).distinct().all()
    brands = [brand[0] for brand in brands if brand[0]]
    
    return render_template('cars/index.html', 
                         cars=cars, 
                         search=search, 
                         status=status, 
                         brand=brand,
                         brands=brands)

@cars_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if not current_user.has_permission('edit_cars'):
        flash('ليس لديك صلاحية لإضافة السيارات', 'error')
        return redirect(url_for('cars.index'))
    
    if request.method == 'POST':
        # Get form data
        name = request.form.get('name', '').strip()
        brand = request.form.get('brand', '').strip()
        model = request.form.get('model', '').strip()
        year = request.form.get('year', type=int)
        chassis_number = request.form.get('chassis_number', '').strip()
        plate_number = request.form.get('plate_number', '').strip()
        color = request.form.get('color', '').strip()
        engine_size = request.form.get('engine_size', '').strip()
        fuel_type = request.form.get('fuel_type', '').strip()
        transmission = request.form.get('transmission', '').strip()
        mileage = request.form.get('mileage', type=int)
        price = request.form.get('price', type=float)
        cost_price = request.form.get('cost_price', type=float)
        description = request.form.get('description', '').strip()

        # New fields
        car_type = request.form.get('car_type', '').strip()
        condition_rating = request.form.get('condition_rating', '').strip()
        condition_notes = request.form.get('condition_notes', '').strip()

        # Validation
        if not all([name, brand, model, year, chassis_number, price, car_type]):
            flash('الحقول المطلوبة: الاسم، الماركة، الموديل، السنة، رقم الشاسيه، السعر، نوع السيارة', 'error')
            return render_template('cars/add.html')
        
        if year < 1900 or year > datetime.now().year + 1:
            flash('سنة الصنع غير صحيحة', 'error')
            return render_template('cars/add.html')
        
        if price <= 0:
            flash('السعر يجب أن يكون أكبر من صفر', 'error')
            return render_template('cars/add.html')

        # Validate car type
        if car_type not in ['new', 'used']:
            flash('نوع السيارة غير صحيح', 'error')
            return render_template('cars/add.html')

        # Validate mileage for used cars
        if car_type == 'used' and (not mileage or mileage < 0):
            flash('يجب إدخال المسافة المقطوعة للسيارات المستعملة', 'error')
            return render_template('cars/add.html')

        # Set mileage to 0 for new cars
        if car_type == 'new':
            mileage = 0
            if not condition_rating:
                condition_rating = 'excellent'

        # Check if chassis number already exists
        if Car.query.filter_by(chassis_number=chassis_number).first():
            flash('رقم الشاسيه موجود بالفعل', 'error')
            return render_template('cars/add.html')
        
        # Check if plate number already exists (if provided)
        if plate_number and Car.query.filter_by(plate_number=plate_number).first():
            flash('رقم اللوحة موجود بالفعل', 'error')
            return render_template('cars/add.html')
        
        # Handle file uploads
        uploaded_files = request.files.getlist('images')
        image_paths = []
        
        for file in uploaded_files:
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # Add timestamp to avoid conflicts
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                
                upload_path = os.path.join('static/uploads/cars', filename)
                file.save(upload_path)
                image_paths.append(f'uploads/cars/{filename}')
        
        # Create new car
        car = Car(
            name=name,
            brand=brand,
            model=model,
            year=year,
            chassis_number=chassis_number,
            plate_number=plate_number or None,
            color=color,
            engine_size=engine_size,
            fuel_type=fuel_type,
            transmission=transmission,
            mileage=mileage,
            price=price,
            cost_price=cost_price,
            description=description,
            images=json.dumps(image_paths) if image_paths else None,
            car_type=car_type,
            condition_rating=condition_rating or None,
            condition_notes=condition_notes or None,
            created_by=current_user.id
        )
        
        try:
            db.session.add(car)
            db.session.commit()
            flash(f'تم إضافة السيارة {name} بنجاح', 'success')
            return redirect(url_for('cars.view', car_id=car.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة السيارة', 'error')
    
    return render_template('cars/add.html')

@cars_bp.route('/<int:car_id>')
@login_required
def view(car_id):
    car = Car.query.get_or_404(car_id)
    
    # Get car images
    images = []
    if car.images:
        try:
            images = json.loads(car.images)
        except:
            images = []
    
    # Get sales history
    sales = Sale.query.filter_by(car_id=car_id).order_by(Sale.created_at.desc()).all()
    
    return render_template('cars/view.html', car=car, images=images, sales=sales)

@cars_bp.route('/<int:car_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(car_id):
    if not current_user.has_permission('edit_cars'):
        flash('ليس لديك صلاحية لتعديل السيارات', 'error')
        return redirect(url_for('cars.view', car_id=car_id))
    
    car = Car.query.get_or_404(car_id)
    
    if request.method == 'POST':
        # Get form data
        car.brand = request.form.get('brand', '').strip()
        car.model = request.form.get('model', '').strip()
        car.year = request.form.get('year', type=int)
        car.color = request.form.get('color', '').strip()
        car.vin = request.form.get('vin', '').strip()
        car.license_plate = request.form.get('license_plate', '').strip()
        car.engine_size = request.form.get('engine_size', type=float)
        car.fuel_type = request.form.get('fuel_type', '').strip()
        car.transmission = request.form.get('transmission', '').strip()
        car.mileage = request.form.get('mileage', type=int)
        car.condition = request.form.get('condition', '').strip()
        car.quality = request.form.get('quality', '').strip()
        car.status = request.form.get('status', 'available')
        car.cost_price = request.form.get('cost_price', type=float)
        car.price = request.form.get('price', type=float)
        car.description = request.form.get('description', '').strip()
        car.notes = request.form.get('notes', '').strip()

        # Validation
        if not all([car.brand, car.model, car.year, car.condition, car.price]):
            flash('الحقول المطلوبة: الماركة، الموديل، السنة، الحالة، سعر البيع', 'error')
            return render_template('cars/edit.html', car=car)

        # Check if VIN already exists (excluding current car)
        if car.vin:
            existing_car = Car.query.filter(
                Car.vin == car.vin,
                Car.id != car_id
            ).first()
            if existing_car:
                flash('رقم الهيكل (VIN) موجود بالفعل', 'error')
                return render_template('cars/edit.html', car=car)

        # Check if license plate already exists (excluding current car)
        if car.license_plate:
            existing_car = Car.query.filter(
                Car.license_plate == car.license_plate,
                Car.id != car_id
            ).first()
            if existing_car:
                flash('رقم اللوحة موجود بالفعل', 'error')
                return render_template('cars/edit.html', car=car)
        car.updated_at = datetime.utcnow()
        
        # Handle new file uploads
        uploaded_files = request.files.getlist('photos')

        # Add new images
        for file in uploaded_files:
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename

                # Create upload directory if it doesn't exist
                upload_dir = os.path.join('static/uploads/cars')
                os.makedirs(upload_dir, exist_ok=True)

                upload_path = os.path.join(upload_dir, filename)
                file.save(upload_path)

                # Create CarPhoto record
                from models import CarPhoto
                photo = CarPhoto(
                    car_id=car.id,
                    file_path=f'uploads/cars/{filename}',
                    is_primary=False
                )
                db.session.add(photo)
        
        try:
            db.session.commit()
            flash(f'تم تحديث بيانات السيارة {car.brand} {car.model} بنجاح', 'success')
            return redirect(url_for('cars.view', car_id=car.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث البيانات', 'error')

    return render_template('cars/edit.html', car=car)

@cars_bp.route('/<int:car_id>/delete', methods=['POST'])
@login_required
def delete(car_id):
    if not current_user.has_permission('delete_all'):
        flash('ليس لديك صلاحية لحذف السيارات', 'error')
        return redirect(url_for('cars.view', car_id=car_id))
    
    car = Car.query.get_or_404(car_id)
    
    # Check if car has sales
    if car.sales:
        flash('لا يمكن حذف السيارة لأنها مرتبطة بعمليات بيع', 'error')
        return redirect(url_for('cars.view', car_id=car_id))
    
    try:
        # Delete car images
        if car.images:
            try:
                images = json.loads(car.images)
                for image_path in images:
                    full_path = os.path.join('static', image_path)
                    if os.path.exists(full_path):
                        os.remove(full_path)
            except:
                pass
        
        db.session.delete(car)
        db.session.commit()
        flash(f'تم حذف السيارة {car.name} بنجاح', 'success')
        return redirect(url_for('cars.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف السيارة', 'error')
        return redirect(url_for('cars.view', car_id=car_id))

@cars_bp.route('/photos/<int:photo_id>/delete', methods=['DELETE'])
@login_required
def delete_photo(photo_id):
    """Delete a car photo"""
    if not current_user.has_permission('edit_cars'):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية'})

    from models import CarPhoto
    photo = CarPhoto.query.get_or_404(photo_id)

    try:
        # Delete physical file
        full_path = os.path.join('static', photo.file_path)
        if os.path.exists(full_path):
            os.remove(full_path)

        # Delete from database
        db.session.delete(photo)
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف الصورة'})

@cars_bp.route('/<int:car_id>/remove_image', methods=['POST'])
@login_required
def remove_image(car_id):
    if not current_user.has_permission('edit_cars'):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية'})
    
    car = Car.query.get_or_404(car_id)
    image_path = request.json.get('image_path')
    
    if not image_path:
        return jsonify({'success': False, 'message': 'مسار الصورة مطلوب'})
    
    try:
        # Get current images
        images = []
        if car.images:
            images = json.loads(car.images)
        
        # Remove the image from list
        if image_path in images:
            images.remove(image_path)
            
            # Delete physical file
            full_path = os.path.join('static', image_path)
            if os.path.exists(full_path):
                os.remove(full_path)
            
            # Update database
            car.images = json.dumps(images) if images else None
            db.session.commit()
            
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': 'الصورة غير موجودة'})
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف الصورة'})

@cars_bp.route('/api/search')
@login_required
def api_search():
    """API endpoint for car search (for AJAX)"""
    query = request.args.get('q', '')
    status = request.args.get('status', 'available')
    
    cars = Car.query.filter(
        Car.status == status,
        Car.name.contains(query) |
        Car.brand.contains(query) |
        Car.model.contains(query)
    ).limit(10).all()
    
    results = []
    for car in cars:
        results.append({
            'id': car.id,
            'name': car.name,
            'brand': car.brand,
            'model': car.model,
            'year': car.year,
            'price': car.price,
            'display_name': f"{car.brand} {car.model} {car.year} - {car.name}"
        })
    
    return jsonify(results)
