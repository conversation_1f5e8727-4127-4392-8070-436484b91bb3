{% extends "base.html" %}

{% block title %}مبيعة #{{ sale.id }} - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">مبيعة #{{ sale.id }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('edit_sales') %}
            <button class="btn btn-warning" disabled>
                <i class="fas fa-edit me-2"></i>
                تعديل
            </button>
            {% endif %}
            
            <a href="{{ url_for('contracts.preview_contract', sale_id=sale.id) }}" class="btn btn-success">
                <i class="fas fa-file-contract me-2"></i>
                عرض العقد
            </a>
            
            <button class="btn btn-info" disabled>
                <i class="fas fa-receipt me-2"></i>
                طباعة إيصال
            </button>
        </div>
        <a href="{{ url_for('sales.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- Sale Details -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل المبيعة</h5>
                <span class="badge bg-{% if sale.status == 'active' %}success{% elif sale.status == 'completed' %}primary{% else %}danger{% endif %} fs-6">
                    {% if sale.status == 'active' %}نشط
                    {% elif sale.status == 'completed' %}مكتمل
                    {% elif sale.status == 'cancelled' %}ملغي
                    {% else %}{{ sale.status }}{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم المبيعة:</strong></td>
                                <td>#{{ sale.id }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ البيع:</strong></td>
                                <td>{{ sale.sale_date|arabic_date }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع البيع:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                        {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>سعر البيع:</strong></td>
                                <td><strong class="text-primary">{{ sale.sale_price|currency }}</strong></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if sale.sale_type == 'installment' %}
                            <tr>
                                <td><strong>الدفعة المقدمة:</strong></td>
                                <td>{{ sale.down_payment|currency if sale.down_payment else 'لا توجد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الأقساط:</strong></td>
                                <td>{{ sale.installment_count }} قسط</td>
                            </tr>
                            <tr>
                                <td><strong>قيمة القسط:</strong></td>
                                <td>{{ sale.installment_amount|currency }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ sale.created_at|arabic_date }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if sale.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>ملاحظات:</h6>
                        <div class="alert alert-light">
                            <i class="fas fa-sticky-note me-2"></i>
                            {{ sale.notes }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Customer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">بيانات العميل</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        {% if sale.customer.photo_path %}
                        <img src="{{ url_for('static', filename=sale.customer.photo_path) }}" 
                             class="img-fluid rounded-circle" style="width: 100px; height: 100px; object-fit: cover;" 
                             alt="صورة العميل">
                        {% else %}
                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" 
                             style="width: 100px; height: 100px; font-size: 2rem;">
                            {{ sale.customer.full_name[:2].upper() }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-9">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>
                                    <a href="{{ url_for('customers.view', customer_id=sale.customer.id) }}" 
                                       class="text-decoration-none">
                                        {{ sale.customer.full_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهوية:</strong></td>
                                <td><code>{{ sale.customer.national_id }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    <a href="tel:{{ sale.customer.phone }}" class="text-decoration-none">
                                        {{ sale.customer.phone }}
                                    </a>
                                </td>
                            </tr>
                            {% if sale.customer.whatsapp_number %}
                            <tr>
                                <td><strong>واتساب:</strong></td>
                                <td>
                                    <a href="https://wa.me/{{ sale.customer.whatsapp_number.replace('+', '') }}" 
                                       target="_blank" class="text-success text-decoration-none">
                                        <i class="fab fa-whatsapp me-1"></i>
                                        {{ sale.customer.whatsapp_number }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Car Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">بيانات السيارة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم السيارة:</strong></td>
                                <td>
                                    <a href="{{ url_for('cars.view', car_id=sale.car.id) }}" 
                                       class="text-decoration-none">
                                        {{ sale.car.name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الماركة والموديل:</strong></td>
                                <td>{{ sale.car.brand }} {{ sale.car.model }}</td>
                            </tr>
                            <tr>
                                <td><strong>سنة الصنع:</strong></td>
                                <td>{{ sale.car.year }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الشاسيه:</strong></td>
                                <td><code>{{ sale.car.chassis_number }}</code></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if sale.car.plate_number %}
                            <tr>
                                <td><strong>رقم اللوحة:</strong></td>
                                <td><span class="badge bg-info">{{ sale.car.plate_number }}</span></td>
                            </tr>
                            {% endif %}
                            {% if sale.car.color %}
                            <tr>
                                <td><strong>اللون:</strong></td>
                                <td>{{ sale.car.color }}</td>
                            </tr>
                            {% endif %}
                            {% if sale.car.mileage %}
                            <tr>
                                <td><strong>المسافة المقطوعة:</strong></td>
                                <td>{{ sale.car.mileage|number_format }} كم</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>سعر السيارة الأصلي:</strong></td>
                                <td>{{ sale.car.price|currency }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Payment History -->
        {% if sale.sale_type == 'installment' %}
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تاريخ المدفوعات</h5>
                {% if current_user.has_permission('payments') %}
                <a href="{{ url_for('sales.add_payment', sale_id=sale.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة دفعة
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>ملاحظات</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if sale.down_payment %}
                            <tr class="table-success">
                                <td>{{ sale.sale_date|arabic_date }}</td>
                                <td>{{ sale.down_payment|currency }}</td>
                                <td><span class="badge bg-success">دفعة مقدمة</span></td>
                                <td>-</td>
                                <td>{{ sale.created_by.full_name if sale.created_by else '-' }}</td>
                            </tr>
                            {% endif %}
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.payment_date|arabic_date }}</td>
                                <td>{{ payment.amount|currency }}</td>
                                <td>{{ payment.payment_method or 'نقدي' }}</td>
                                <td>{{ payment.notes or '-' }}</td>
                                <td>{{ payment.created_by.full_name if payment.created_by else '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد مدفوعات بعد</p>
                    {% if current_user.has_permission('payments') %}
                    <a href="{{ url_for('sales.add_payment', sale_id=sale.id) }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دفعة
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Payment Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">ملخص المدفوعات</h5>
            </div>
            <div class="card-body">
                {% set total_paid = (payments|sum(attribute='amount') or 0) + (sale.down_payment or 0) %}
                {% set remaining = sale.sale_price - total_paid %}
                
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h6>إجمالي السعر</h6>
                        <h4 class="text-primary">{{ sale.sale_price|currency }}</h4>
                    </div>
                    <div class="col-6">
                        <h6>المدفوع</h6>
                        <h5 class="text-success">{{ total_paid|currency }}</h5>
                    </div>
                    <div class="col-6">
                        <h6>المتبقي</h6>
                        <h5 class="text-{{ 'success' if remaining <= 0 else 'warning' }}">
                            {{ remaining|currency }}
                        </h5>
                    </div>
                </div>
                
                {% if remaining > 0 and sale.sale_type == 'installment' %}
                <div class="progress mt-3">
                    {% set progress = (total_paid / sale.sale_price * 100) %}
                    <div class="progress-bar" role="progressbar" style="width: {{ progress }}%">
                        {{ "%.1f"|format(progress) }}%
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if sale.customer.whatsapp_number or sale.customer.phone %}
                    <a href="{{ url_for('whatsapp.send_message') }}?customer_id={{ sale.customer.id }}" 
                       class="btn btn-success btn-sm">
                        <i class="fab fa-whatsapp me-2"></i>
                        إرسال رسالة واتساب
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-info btn-sm" disabled>
                        <i class="fas fa-download me-2"></i>
                        تحميل العقد
                    </button>
                    
                    {% if sale.sale_type == 'installment' %}
                    <a href="{{ url_for('sales.installments', sale_id=sale.id) }}" 
                       class="btn btn-warning btn-sm">
                        <i class="fas fa-calendar-alt me-2"></i>
                        جدول الأقساط
                    </a>
                    {% endif %}
                    
                    {% if sale.status == 'active' and current_user.has_permission('edit_sales') %}
                    <hr>
                    <button class="btn btn-danger btn-sm" disabled>
                        <i class="fas fa-times me-2"></i>
                        إلغاء المبيعة
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Sale Timeline -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تاريخ المبيعة</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6>إنشاء المبيعة</h6>
                            <small class="text-muted">{{ sale.created_at|arabic_date }}</small>
                        </div>
                    </div>
                    
                    {% if sale.down_payment %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6>دفع المقدمة</h6>
                            <small class="text-muted">{{ sale.sale_date|arabic_date }}</small>
                            <p class="mb-0">{{ sale.down_payment|currency }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% for payment in payments %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>دفعة</h6>
                            <small class="text-muted">{{ payment.payment_date|arabic_date }}</small>
                            <p class="mb-0">{{ payment.amount|currency }}</p>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if remaining <= 0 %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6>اكتمال الدفع</h6>
                            <small class="text-muted">مكتملة</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content p {
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add tooltips
    $('[title]').tooltip();
});
</script>
{% endblock %}
