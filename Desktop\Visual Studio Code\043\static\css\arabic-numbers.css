/**
 * تنسيقات الأرقام العربية والكتابة العشوائية
 * Arabic Numbers and Random Typing Styles
 */

/* خطوط محسنة للأرقام العربية */
.arabic-numbers {
    font-family: 'Segoe UI', '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين عرض الأرقام العربية */
.number-display,
.price-display,
.currency-display,
.arabic-number {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
    direction: rtl;
    text-align: right;
}

/* أرقام كبيرة ومميزة */
.big-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* أرقام الإحصائيات */
.stats-number {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    direction: ltr; /* للأرقام الكبيرة نستخدم اتجاه يسار لليمين */
}

/* أرقام العملة */
.currency-amount {
    font-size: 1.25rem;
    font-weight: bold;
    color: #28a745;
    direction: rtl;
}

.currency-large {
    font-size: 2rem;
    font-weight: 900;
    color: #28a745;
    text-shadow: 2px 2px 4px rgba(40, 167, 69, 0.2);
    direction: rtl;
}

/* تأثيرات الكتابة العشوائية */
.random-typing {
    position: relative;
    overflow: hidden;
}

.random-typing::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: currentColor;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* تأثير الكتابة المتحركة */
.typing-effect {
    border-right: 2px solid #333;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
    white-space: nowrap;
    overflow: hidden;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #333; }
}

/* تحريك الأرقام */
.animate-number {
    transition: all 0.3s ease;
    display: inline-block;
}

.animate-number:hover {
    transform: scale(1.05);
    color: #007bff;
}

/* أرقام متوهجة */
.glowing-number {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #007bff, 0 0 20px #007bff;
    }
    to {
        text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #007bff, 0 0 40px #007bff;
    }
}

/* أرقام نابضة */
.pulsing-number {
    animation: pulse-number 2s infinite;
}

@keyframes pulse-number {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تحسين عرض الأرقام في الجداول */
table .number-cell {
    text-align: center;
    font-weight: 600;
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    direction: ltr; /* للأرقام في الجداول */
}

/* تحسين عرض الأرقام في البطاقات */
.card .number-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
    direction: rtl;
}

/* أرقام ملونة حسب القيمة */
.number-positive {
    color: #28a745;
    font-weight: bold;
}

.number-negative {
    color: #dc3545;
    font-weight: bold;
}

.number-neutral {
    color: #6c757d;
    font-weight: normal;
}

/* تأثيرات خاصة للأرقام المهمة */
.important-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    direction: rtl;
}

/* أرقام اللوحات */
.plate-number {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    text-align: center;
    letter-spacing: 2px;
    direction: ltr; /* أرقام اللوحات تُقرأ من اليسار لليمين */
}

/* أرقام الأسعار */
.price-tag {
    background: #28a745;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.1rem;
    direction: rtl;
    display: inline-block;
}

.price-tag.large {
    font-size: 1.5rem;
    padding: 12px 20px;
}

.price-tag.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.price-tag.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* تحسين عرض الأرقام في النماذج */
.form-control.arabic-input {
    direction: rtl;
    text-align: right;
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

.form-control.number-input {
    direction: ltr;
    text-align: left;
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    font-weight: 600;
}

/* أرقام العد التنازلي */
.countdown-number {
    font-size: 2rem;
    font-weight: 900;
    color: #dc3545;
    text-align: center;
    background: rgba(220, 53, 69, 0.1);
    padding: 10px;
    border-radius: 8px;
    border: 2px solid #dc3545;
    direction: ltr;
}

/* تأثير الكتابة المتدرجة */
.gradient-typing {
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* أرقام متحركة في الخلفية */
.floating-numbers {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.floating-number {
    position: absolute;
    font-size: 2rem;
    color: rgba(102, 126, 234, 0.1);
    font-weight: bold;
    animation: float-up 10s linear infinite;
}

@keyframes float-up {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .big-number {
        font-size: 1.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .currency-large {
        font-size: 1.5rem;
    }
    
    .plate-number {
        font-size: 1.2rem;
        padding: 8px 15px;
        letter-spacing: 1px;
    }
    
    .countdown-number {
        font-size: 1.5rem;
        padding: 8px;
    }
}

/* تحسينات للطباعة */
@media print {
    .random-typing::after,
    .typing-effect {
        display: none;
    }
    
    .animate-number,
    .glowing-number,
    .pulsing-number {
        animation: none;
        transform: none;
        text-shadow: none;
    }
    
    .floating-numbers {
        display: none;
    }
}

/* فئات مساعدة */
.no-arabic-convert {
    /* هذه الفئة تمنع تحويل الأرقام إلى العربية */
}

.english-only {
    direction: ltr;
    text-align: left;
    /* هذه الفئة للنصوص التي يجب أن تبقى بالإنجليزية */
}

.rtl-number {
    direction: rtl;
    text-align: right;
}

.ltr-number {
    direction: ltr;
    text-align: left;
}

/* تأثيرات خاصة للأرقام المميزة */
.special-number {
    position: relative;
    display: inline-block;
}

.special-number::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: 8px;
    z-index: -1;
    animation: rotate-border 3s linear infinite;
}

@keyframes rotate-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين عرض الأرقام في الإشعارات */
.notification .number {
    font-weight: bold;
    color: #007bff;
    direction: rtl;
}

/* أرقام الحالة */
.status-number {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    direction: rtl;
}

.status-number.success {
    background: #d4edda;
    color: #155724;
}

.status-number.warning {
    background: #fff3cd;
    color: #856404;
}

.status-number.danger {
    background: #f8d7da;
    color: #721c24;
}

.status-number.info {
    background: #d1ecf1;
    color: #0c5460;
}
