
/* ULTIMATE LAYOUT FIX - FORCE EVERYTHING TO WORK */

/* Reset everything */
* {
    box-sizing: border-box !important;
    direction: rtl !important;
}

html {
    direction: rtl !important;
    text-align: right !important;
    font-size: 16px !important;
}

body {
    direction: rtl !important;
    text-align: right !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    min-height: 100vh !important;
    font-family: 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
    background-color: #f8f9fa !important;
    overflow-x: hidden !important;
}

/* Container fixes */
.container,
.container-fluid {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    direction: rtl !important;
}

/* Row fixes */
.row {
    display: flex !important;
    flex-wrap: wrap !important;
    width: 100% !important;
    margin: 0 !important;
    direction: rtl !important;
}

/* Column fixes */
.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-auto,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    position: relative !important;
    width: 100% !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
    direction: rtl !important;
}

/* Specific column widths */
.col-md-3, .col-lg-2 {
    flex: 0 0 250px !important;
    max-width: 250px !important;
    width: 250px !important;
    padding: 0 !important;
}

.col-md-9, .col-lg-10 {
    flex: 1 !important;
    max-width: calc(100% - 250px) !important;
    width: calc(100% - 250px) !important;
    padding: 20px !important;
}

/* Sidebar fixes */
.sidebar {
    min-height: 100vh !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
    position: relative !important;
    width: 100% !important;
    padding: 20px 0 !important;
    direction: rtl !important;
    text-align: right !important;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8) !important;
    padding: 12px 20px !important;
    margin: 2px 10px !important;
    border-radius: 8px !important;
    display: block !important;
    text-decoration: none !important;
    direction: rtl !important;
    text-align: right !important;
    transition: all 0.3s ease !important;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
    transform: translateX(-5px) !important;
}

.sidebar .nav-link i {
    width: 20px !important;
    margin-left: 10px !important;
    text-align: center !important;
}

/* Main content fixes */
.main-content {
    padding: 20px !important;
    direction: rtl !important;
    text-align: right !important;
    min-height: 100vh !important;
    background-color: #f8f9fa !important;
}

/* Navigation fixes */
.nav {
    display: flex !important;
    flex-direction: column !important;
    padding-left: 0 !important;
    margin-bottom: 0 !important;
    list-style: none !important;
}

.nav-item {
    display: list-item !important;
}

/* Utility classes */
.d-md-block {
    display: block !important;
}

.d-none {
    display: none !important;
}

.d-flex {
    display: flex !important;
}

.position-sticky {
    position: sticky !important;
    top: 0 !important;
}

.collapse {
    display: block !important;
}

/* Text alignment */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

.text-center {
    text-align: center !important;
}

.text-white {
    color: white !important;
}

/* Spacing */
.pt-3 {
    padding-top: 1rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

/* Card fixes */
.card {
    border-radius: 15px !important;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
    margin-bottom: 20px !important;
    background: white !important;
    border: none !important;
    direction: rtl !important;
    text-align: right !important;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 15px 20px !important;
    direction: rtl !important;
    text-align: right !important;
    border-bottom: none !important;
}

.card-body {
    padding: 20px !important;
    direction: rtl !important;
    text-align: right !important;
}

/* Button fixes */
.btn {
    border-radius: 8px !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    direction: rtl !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: white !important;
}

/* Form fixes */
.form-control,
.form-select {
    border-radius: 8px !important;
    border: 1px solid #ced4da !important;
    padding: 8px 12px !important;
    direction: rtl !important;
    text-align: right !important;
    width: 100% !important;
}

/* Table fixes */
.table {
    border-radius: 8px !important;
    overflow: hidden !important;
    direction: rtl !important;
    text-align: right !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
    background-color: transparent !important;
}

.table th,
.table td {
    padding: 12px !important;
    vertical-align: top !important;
    border-top: 1px solid #dee2e6 !important;
    direction: rtl !important;
    text-align: right !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .col-md-3, .col-lg-2 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        width: 100% !important;
    }
    
    .col-md-9, .col-lg-10 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        width: 100% !important;
        padding: 10px !important;
    }
    
    .sidebar {
        min-height: auto !important;
        position: relative !important;
    }
    
    .main-content {
        padding: 10px !important;
    }
}

/* Force all elements to inherit RTL */
* {
    direction: inherit !important;
}

/* Final override */
body * {
    direction: rtl !important;
}
