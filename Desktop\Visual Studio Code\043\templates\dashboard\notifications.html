{% extends "base.html" %}

{% block title %}الإشعارات - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">الإشعارات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-2"></i>
                تحديد الكل كمقروء
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="clearAllNotifications()">
                <i class="fas fa-trash me-2"></i>
                حذف الكل
            </button>
        </div>
        <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Notification Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">فلترة الإشعارات</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <select class="form-select" id="typeFilter" onchange="filterNotifications()">
                    <option value="">جميع الأنواع</option>
                    <option value="payment_due">أقساط مستحقة</option>
                    <option value="payment_overdue">أقساط متأخرة</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="new_sale">مبيعة جديدة</option>
                    <option value="system">نظام</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter" onchange="filterNotifications()">
                    <option value="">جميع الحالات</option>
                    <option value="unread">غير مقروء</option>
                    <option value="read">مقروء</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="priorityFilter" onchange="filterNotifications()">
                    <option value="">جميع الأولويات</option>
                    <option value="high">عالية</option>
                    <option value="medium">متوسطة</option>
                    <option value="low">منخفضة</option>
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" id="dateFilter" onchange="filterNotifications()" 
                       placeholder="التاريخ">
            </div>
        </div>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة الإشعارات</h5>
        <span class="badge bg-primary">{{ notifications.total if notifications else 0 }} إشعار</span>
    </div>
    <div class="card-body p-0">
        {% if notifications and notifications.items %}
        <div class="list-group list-group-flush" id="notificationsList">
            {% for notification in notifications.items %}
            <div class="list-group-item notification-item {{ 'list-group-item-light' if notification.is_read else 'list-group-item-primary' }}"
                 data-type="{{ notification.type }}" 
                 data-status="{{ 'read' if notification.is_read else 'unread' }}"
                 data-priority="{{ notification.priority or 'medium' }}"
                 data-date="{{ notification.created_at.strftime('%Y-%m-%d') }}">
                
                <div class="d-flex w-100 justify-content-between align-items-start">
                    <div class="d-flex align-items-start">
                        <!-- Notification Icon -->
                        <div class="me-3">
                            {% if notification.type == 'payment_due' %}
                                <i class="fas fa-clock text-warning fa-lg"></i>
                            {% elif notification.type == 'payment_overdue' %}
                                <i class="fas fa-exclamation-triangle text-danger fa-lg"></i>
                            {% elif notification.type == 'low_stock' %}
                                <i class="fas fa-box text-info fa-lg"></i>
                            {% elif notification.type == 'new_sale' %}
                                <i class="fas fa-handshake text-success fa-lg"></i>
                            {% elif notification.type == 'system' %}
                                <i class="fas fa-cog text-secondary fa-lg"></i>
                            {% else %}
                                <i class="fas fa-bell text-primary fa-lg"></i>
                            {% endif %}
                        </div>
                        
                        <!-- Notification Content -->
                        <div class="flex-grow-1">
                            <h6 class="mb-1 {{ 'fw-bold' if not notification.is_read }}">
                                {{ notification.title }}
                                {% if not notification.is_read %}
                                <span class="badge bg-primary ms-2">جديد</span>
                                {% endif %}
                            </h6>
                            <p class="mb-1 text-muted">{{ notification.message }}</p>
                            
                            <!-- Notification Meta -->
                            <div class="d-flex align-items-center text-muted small">
                                <span class="me-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ notification.created_at|arabic_date }}
                                </span>
                                
                                {% if notification.priority %}
                                <span class="me-3">
                                    <i class="fas fa-flag me-1"></i>
                                    <span class="badge bg-{% if notification.priority == 'high' %}danger{% elif notification.priority == 'medium' %}warning{% else %}secondary{% endif %} badge-sm">
                                        {% if notification.priority == 'high' %}عالية
                                        {% elif notification.priority == 'medium' %}متوسطة
                                        {% else %}منخفضة{% endif %}
                                    </span>
                                </span>
                                {% endif %}
                                
                                {% if notification.related_url %}
                                <span>
                                    <a href="{{ notification.related_url }}" class="text-decoration-none">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        عرض التفاصيل
                                    </a>
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notification Actions -->
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            {% if not notification.is_read %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="markAsRead({{ notification.id }})">
                                    <i class="fas fa-check me-2"></i>
                                    تحديد كمقروء
                                </a>
                            </li>
                            {% else %}
                            <li>
                                <a class="dropdown-item" href="#" onclick="markAsUnread({{ notification.id }})">
                                    <i class="fas fa-envelope me-2"></i>
                                    تحديد كغير مقروء
                                </a>
                            </li>
                            {% endif %}
                            
                            {% if notification.related_url %}
                            <li>
                                <a class="dropdown-item" href="{{ notification.related_url }}">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    عرض التفاصيل
                                </a>
                            </li>
                            {% endif %}
                            
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="#" onclick="deleteNotification({{ notification.id }})">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if notifications and notifications.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="تنقل الإشعارات">
                <ul class="pagination justify-content-center mb-0">
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadMoreNotifications()">
                            تحميل المزيد
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5>لا توجد إشعارات</h5>
            <p class="text-muted">لم يتم إنشاء أي إشعارات بعد.</p>
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Notification Statistics -->
{% if notifications %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي الإشعارات</h5>
                <h2 class="text-primary">{{ notifications.total if notifications else 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">غير مقروءة</h5>
                <h2 class="text-warning">{{ notifications.items|rejectattr('is_read')|list|length if notifications else 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">أولوية عالية</h5>
                <h2 class="text-danger">{{ notifications.items|selectattr('priority', 'equalto', 'high')|list|length if notifications else 0 }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">اليوم</h5>
                <h2 class="text-info">0</h2>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        refreshNotifications();
    }, 30000);
});

function filterNotifications() {
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    const notifications = document.querySelectorAll('.notification-item');
    
    notifications.forEach(function(notification) {
        let show = true;
        
        if (typeFilter && notification.dataset.type !== typeFilter) {
            show = false;
        }
        
        if (statusFilter && notification.dataset.status !== statusFilter) {
            show = false;
        }
        
        if (priorityFilter && notification.dataset.priority !== priorityFilter) {
            show = false;
        }
        
        if (dateFilter && notification.dataset.date !== dateFilter) {
            show = false;
        }
        
        notification.style.display = show ? 'block' : 'none';
    });
}

function markAsRead(notificationId) {
    fetch(`/dashboard/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء تحديث الإشعار', 'danger');
    });
}

function markAsUnread(notificationId) {
    fetch(`/dashboard/notifications/${notificationId}/unread`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء تحديث الإشعار', 'danger');
    });
}

function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        return;
    }
    
    fetch(`/dashboard/notifications/${notificationId}/delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء حذف الإشعار', 'danger');
    });
}

function markAllAsRead() {
    if (!confirm('هل أنت متأكد من تحديد جميع الإشعارات كمقروءة؟')) {
        return;
    }
    
    fetch('/dashboard/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء تحديث الإشعارات', 'danger');
    });
}

function clearAllNotifications() {
    if (!confirm('هل أنت متأكد من حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }
    
    fetch('/dashboard/notifications/clear-all', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء حذف الإشعارات', 'danger');
    });
}

function refreshNotifications() {
    // Silently refresh notifications in background
    fetch('/dashboard/notifications/refresh')
    .then(response => response.json())
    .then(data => {
        if (data.new_notifications > 0) {
            // Show notification badge or update counter
            updateNotificationBadge(data.total_unread);
        }
    })
    .catch(error => {
        console.error('Error refreshing notifications:', error);
    });
}

function loadMoreNotifications() {
    // Implementation for loading more notifications
    // This would typically involve AJAX pagination
    showAlert('تحميل المزيد من الإشعارات...', 'info');
}

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}

function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

// Mark notification as read when clicked
document.addEventListener('click', function(e) {
    const notificationItem = e.target.closest('.notification-item');
    if (notificationItem && !notificationItem.classList.contains('list-group-item-light')) {
        const notificationId = notificationItem.dataset.notificationId;
        if (notificationId) {
            markAsRead(notificationId);
        }
    }
});
</script>

<style>
.notification-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.list-group-item-primary {
    border-left: 4px solid #007bff;
}

.badge-sm {
    font-size: 0.7rem;
}

.dropdown-toggle::after {
    display: none;
}

@media (max-width: 768px) {
    .notification-item .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .notification-item .dropdown {
        margin-top: 10px;
        align-self: flex-end;
    }
}
</style>
{% endblock %}
