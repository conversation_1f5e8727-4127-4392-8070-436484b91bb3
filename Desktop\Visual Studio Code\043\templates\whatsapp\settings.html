{% extends "base.html" %}

{% block title %}إعدادات الواتساب - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إعدادات الواتساب</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('whatsapp.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للواتساب
        </a>
    </div>
</div>

<form method="POST">
    <div class="row">
        <!-- API Settings -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">إعد<PERSON>ات API</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                        <p class="mb-0">
                            هذه الإعدادات تتطلب خدمة واتساب API خارجية. يمكنك استخدام خدمات مثل:
                            <strong>WhatsApp Business API</strong> أو <strong>Twilio</strong> أو <strong>ChatAPI</strong>
                        </p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="api_provider" class="form-label">مزود الخدمة</label>
                                <select class="form-select" id="api_provider" name="api_provider">
                                    <option value="">اختر مزود الخدمة</option>
                                    <option value="whatsapp_business" {{ 'selected' if settings.api_provider == 'whatsapp_business' }}>
                                        WhatsApp Business API
                                    </option>
                                    <option value="twilio" {{ 'selected' if settings.api_provider == 'twilio' }}>
                                        Twilio
                                    </option>
                                    <option value="chatapi" {{ 'selected' if settings.api_provider == 'chatapi' }}>
                                        ChatAPI
                                    </option>
                                    <option value="custom" {{ 'selected' if settings.api_provider == 'custom' }}>
                                        مخصص
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="api_url" class="form-label">رابط API</label>
                                <input type="url" class="form-control" id="api_url" name="api_url" 
                                       value="{{ settings.api_url or '' }}" placeholder="https://api.example.com">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="api_token" class="form-label">رمز API</label>
                                <input type="password" class="form-control" id="api_token" name="api_token" 
                                       value="{{ settings.api_token or '' }}" placeholder="أدخل رمز API">
                                <div class="form-text">
                                    سيتم تشفير هذا الرمز وحفظه بشكل آمن
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone_number_id" class="form-label">معرف رقم الهاتف</label>
                                <input type="text" class="form-control" id="phone_number_id" name="phone_number_id" 
                                       value="{{ settings.phone_number_id or '' }}" placeholder="123456789">
                                <div class="form-text">
                                    معرف رقم الهاتف المسجل في واتساب بزنس
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Message Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">إعدادات الرسائل</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_country_code" class="form-label">رمز الدولة الافتراضي</label>
                                <select class="form-select" id="default_country_code" name="default_country_code">
                                    <option value="+966" {{ 'selected' if settings.default_country_code == '+966' }}>
                                        السعودية (+966)
                                    </option>
                                    <option value="+971" {{ 'selected' if settings.default_country_code == '+971' }}>
                                        الإمارات (+971)
                                    </option>
                                    <option value="+965" {{ 'selected' if settings.default_country_code == '+965' }}>
                                        الكويت (+965)
                                    </option>
                                    <option value="+973" {{ 'selected' if settings.default_country_code == '+973' }}>
                                        البحرين (+973)
                                    </option>
                                    <option value="+974" {{ 'selected' if settings.default_country_code == '+974' }}>
                                        قطر (+974)
                                    </option>
                                    <option value="+968" {{ 'selected' if settings.default_country_code == '+968' }}>
                                        عمان (+968)
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="message_delay" class="form-label">تأخير بين الرسائل (ثانية)</label>
                                <input type="number" class="form-control" id="message_delay" name="message_delay" 
                                       value="{{ settings.message_delay or 2 }}" min="1" max="60">
                                <div class="form-text">
                                    لتجنب الحظر من واتساب
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_daily_messages" class="form-label">الحد الأقصى للرسائل اليومية</label>
                                <input type="number" class="form-control" id="max_daily_messages" name="max_daily_messages" 
                                       value="{{ settings.max_daily_messages or 100 }}" min="1">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="retry_failed_messages" class="form-label">إعادة المحاولة للرسائل الفاشلة</label>
                                <select class="form-select" id="retry_failed_messages" name="retry_failed_messages">
                                    <option value="0" {{ 'selected' if settings.retry_failed_messages == 0 }}>
                                        لا تعيد المحاولة
                                    </option>
                                    <option value="1" {{ 'selected' if settings.retry_failed_messages == 1 }}>
                                        مرة واحدة
                                    </option>
                                    <option value="3" {{ 'selected' if settings.retry_failed_messages == 3 }}>
                                        3 مرات
                                    </option>
                                    <option value="5" {{ 'selected' if settings.retry_failed_messages == 5 }}>
                                        5 مرات
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_reminders" name="auto_reminders" 
                                       {{ 'checked' if settings.auto_reminders }}>
                                <label class="form-check-label" for="auto_reminders">
                                    تفعيل التذكيرات التلقائية للمدفوعات المتأخرة
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-12 mt-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="log_messages" name="log_messages" 
                                       {{ 'checked' if settings.log_messages }}>
                                <label class="form-check-label" for="log_messages">
                                    حفظ سجل جميع الرسائل المرسلة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notification Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إعدادات الإشعارات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reminder_days_before" class="form-label">تذكير قبل الاستحقاق (أيام)</label>
                                <input type="number" class="form-control" id="reminder_days_before" name="reminder_days_before" 
                                       value="{{ settings.reminder_days_before or 3 }}" min="1" max="30">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reminder_days_after" class="form-label">تذكير بعد التأخير (أيام)</label>
                                <input type="number" class="form-control" id="reminder_days_after" name="reminder_days_after" 
                                       value="{{ settings.reminder_days_after or 7 }}" min="1" max="30">
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="admin_notifications" class="form-label">إشعارات المدير</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="notify_failed_messages" name="notify_failed_messages" 
                                           {{ 'checked' if settings.notify_failed_messages }}>
                                    <label class="form-check-label" for="notify_failed_messages">
                                        إشعار عند فشل إرسال الرسائل
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="notify_daily_summary" name="notify_daily_summary" 
                                           {{ 'checked' if settings.notify_daily_summary }}>
                                    <label class="form-check-label" for="notify_daily_summary">
                                        ملخص يومي للرسائل المرسلة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test and Status -->
        <div class="col-lg-4">
            <!-- Connection Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">حالة الاتصال</h5>
                </div>
                <div class="card-body text-center">
                    <div id="connectionStatus">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحقق...</span>
                        </div>
                        <p class="mt-2">جاري التحقق من الاتصال...</p>
                    </div>
                </div>
            </div>
            
            <!-- Test Message -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">اختبار الإرسال</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="test_phone" class="form-label">رقم الهاتف للاختبار</label>
                        <input type="tel" class="form-control" id="test_phone" placeholder="+966501234567">
                    </div>
                    <div class="mb-3">
                        <label for="test_message" class="form-label">رسالة الاختبار</label>
                        <textarea class="form-control" id="test_message" rows="3">مرحباً، هذه رسالة اختبار من نظام إدارة المعارض.</textarea>
                    </div>
                    <button type="button" class="btn btn-success w-100" onclick="sendTestMessage()">
                        <i class="fab fa-whatsapp me-2"></i>
                        إرسال رسالة اختبار
                    </button>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">إحصائيات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success">{{ stats.sent_today or 0 }}</h4>
                            <small class="text-muted">رسائل اليوم</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger">{{ stats.failed_today or 0 }}</h4>
                            <small class="text-muted">فشلت اليوم</small>
                        </div>
                        <div class="col-6 mt-3">
                            <h4 class="text-info">{{ stats.total_sent or 0 }}</h4>
                            <small class="text-muted">إجمالي مرسلة</small>
                        </div>
                        <div class="col-6 mt-3">
                            <h4 class="text-warning">{{ stats.pending or 0 }}</h4>
                            <small class="text-muted">في الانتظار</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                        
                        <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                            <i class="fas fa-plug me-2"></i>
                            اختبار الاتصال
                        </button>
                        
                        <button type="button" class="btn btn-outline-warning" onclick="clearLogs()">
                            <i class="fas fa-trash me-2"></i>
                            مسح السجلات
                        </button>
                        
                        <hr>
                        
                        <a href="{{ url_for('whatsapp.templates') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-file-alt me-2"></i>
                            إدارة القوالب
                        </a>
                        
                        <a href="{{ url_for('whatsapp.history') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-history me-2"></i>
                            سجل الرسائل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Check connection status on page load
    checkConnectionStatus();
    
    // Auto-check connection every 5 minutes
    setInterval(checkConnectionStatus, 300000);
});

function checkConnectionStatus() {
    fetch('/whatsapp/check-connection')
        .then(response => response.json())
        .then(data => {
            updateConnectionStatus(data);
        })
        .catch(error => {
            console.error('Error checking connection:', error);
            updateConnectionStatus({status: 'error', message: 'خطأ في التحقق من الاتصال'});
        });
}

function updateConnectionStatus(data) {
    const statusDiv = $('#connectionStatus');
    
    if (data.status === 'connected') {
        statusDiv.html(`
            <div class="text-success">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h6>متصل</h6>
                <small class="text-muted">${data.message || 'الاتصال يعمل بشكل طبيعي'}</small>
            </div>
        `);
    } else if (data.status === 'disconnected') {
        statusDiv.html(`
            <div class="text-danger">
                <i class="fas fa-times-circle fa-2x mb-2"></i>
                <h6>غير متصل</h6>
                <small class="text-muted">${data.message || 'تحقق من إعدادات API'}</small>
            </div>
        `);
    } else {
        statusDiv.html(`
            <div class="text-warning">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h6>خطأ</h6>
                <small class="text-muted">${data.message || 'حدث خطأ غير متوقع'}</small>
            </div>
        `);
    }
}

function testConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    button.disabled = true;
    
    fetch('/whatsapp/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم اختبار الاتصال بنجاح', 'success');
            checkConnectionStatus();
        } else {
            showAlert(data.message || 'فشل في اختبار الاتصال', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء اختبار الاتصال', 'danger');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function sendTestMessage() {
    const phone = $('#test_phone').val();
    const message = $('#test_message').val();
    
    if (!phone || !message) {
        showAlert('يرجى إدخال رقم الهاتف ونص الرسالة', 'warning');
        return;
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    button.disabled = true;
    
    fetch('/whatsapp/send-test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            phone: phone,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إرسال رسالة الاختبار بنجاح', 'success');
        } else {
            showAlert(data.message || 'فشل في إرسال رسالة الاختبار', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء إرسال رسالة الاختبار', 'danger');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function clearLogs() {
    if (!confirm('هل أنت متأكد من مسح جميع سجلات الرسائل؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    fetch('/whatsapp/clear-logs', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم مسح السجلات بنجاح', 'success');
            location.reload();
        } else {
            showAlert('حدث خطأ أثناء مسح السجلات', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء مسح السجلات', 'danger');
    });
}

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}
</script>
{% endblock %}
