# دليل البدء السريع - نظام قوالب الواتساب

## 🚀 البدء السريع

### 1. تشغيل النظام
```bash
# ويندوز
start_server.bat

# أو مباشرة
python simple_run.py
```

### 2. الوصول للنظام
افتح المتصفح واذهب إلى: `http://localhost:5000`

### 3. تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📱 استخدام نظام الواتساب

### الوضع التجريبي (افتراضي)
النظام يعمل حالياً في **الوضع التجريبي** مما يعني:
- ✅ يمكنك إنشاء واستخدام جميع القوالب
- ✅ يمكنك اختبار جميع الوظائف
- ⚠️ لن يتم إرسال رسائل فعلية للعملاء
- 📝 ستظهر الرسائل في سجل الخادم للمراجعة

### الوصول لقوالب الواتساب
1. اذهب إلى قسم **الواتساب** من القائمة الجانبية
2. اضغط على **القوالب المحسنة**
3. ستجد 6 قوالب جاهزة للاستخدام

## 🎯 القوالب المتاحة

### 1. تذكير بالقسط - قطر
- **الاستخدام**: تذكير العملاء بالأقساط المستحقة
- **المتغيرات**: اسم العميل، رقم القسط، المبلغ، تاريخ الاستحقاق
- **مناسب لـ**: الأقساط العادية والمتأخرة

### 2. ترحيب بالعميل الجديد
- **الاستخدام**: الترحيب بالعملاء الجدد
- **المتغيرات**: اسم العميل، تفاصيل السيارة، سعر البيع
- **مناسب لـ**: العملاء الذين اشتروا سيارة حديثاً

### 3. إشعار تأخير - عاجل
- **الاستخدام**: إشعار عاجل للأقساط المتأخرة
- **المتغيرات**: أيام التأخير، المبلغ المستحق، العواقب
- **مناسب لـ**: الأقساط المتأخرة أكثر من أسبوع

### 4. عرض ترويجي
- **الاستخدام**: الحملات التسويقية والعروض
- **المتغيرات**: اسم العميل، تفاصيل العرض
- **مناسب لـ**: العروض الموسمية والحملات الخاصة

### 5. تأكيد الموعد
- **الاستخدام**: تأكيد مواعيد زيارة المعرض
- **المتغيرات**: التاريخ، الوقت، الغرض من الزيارة
- **مناسب لـ**: المواعيد المحجوزة مسبقاً

### 6. تهنئة بالشراء
- **الاستخدام**: تهنئة العملاء بالشراء الجديد
- **المتغيرات**: اسم العميل، تفاصيل السيارة، الهدايا
- **مناسب لـ**: بعد إتمام عملية الشراء مباشرة

## 🔧 كيفية الاستخدام

### إرسال رسالة من صفحة العملاء
1. اذهب إلى **العملاء** من القائمة
2. ابحث عن العميل المطلوب
3. اضغط على زر **الواتساب** (الأخضر) بجانب اسم العميل
4. اختر نوع الرسالة من القائمة المنسدلة:
   - **رسالة مخصصة**: لكتابة رسالة جديدة
   - **رسالة ترحيب**: للعملاء الجدد
   - **تذكير بالدفع**: للأقساط المستحقة
   - **متابعة**: للمواعيد والاستفسارات

### إرسال رسالة مخصصة
1. اذهب إلى **الواتساب** > **إرسال رسالة**
2. اختر العميل من القائمة المنسدلة
3. اختر قالب جاهز أو اكتب رسالة مخصصة
4. استخدم أزرار المتغيرات لإدراج البيانات التلقائية
5. اضغط **معاينة** لرؤية الرسالة النهائية
6. اضغط **إرسال** لإرسال الرسالة

### استخدام المتغيرات
المتغيرات تُستبدل تلقائياً بالبيانات الفعلية:
- `{customer_name}` → اسم العميل
- `{amount}` → مبلغ القسط
- `{due_date}` → تاريخ الاستحقاق
- `{car_details}` → تفاصيل السيارة
- `{today}` → تاريخ اليوم

## ⚙️ الإعدادات

### تفعيل الإرسال الفعلي
لتفعيل الإرسال الفعلي للرسائل:
1. اذهب إلى **الواتساب** > **إعدادات محسنة**
2. أوقف تشغيل **الوضع التجريبي**
3. أدخل إعدادات Twilio:
   - Account SID
   - Auth Token  
   - رقم الواتساب
4. احفظ الإعدادات

### الحصول على إعدادات Twilio
1. أنشئ حساب في [Twilio.com](https://www.twilio.com)
2. اذهب إلى Console Dashboard
3. انسخ Account SID و Auth Token
4. قم بإعداد WhatsApp Sandbox أو احصل على رقم معتمد

## 📊 مراقبة الرسائل

### عرض سجل الرسائل
1. اذهب إلى **الواتساب** من القائمة الرئيسية
2. ستجد قائمة بجميع الرسائل المرسلة
3. يمكنك تصفية الرسائل حسب:
   - الحالة (مرسل، فاشل، تجريبي)
   - نوع الرسالة (تذكير، ترحيب، إلخ)

### حالات الرسائل
- **مرسل**: تم إرسال الرسالة بنجاح
- **فاشل**: فشل في الإرسال
- **تجريبي**: تم في الوضع التجريبي

## 🎨 تخصيص القوالب

### إنشاء قالب جديد
1. اذهب إلى **القوالب المحسنة**
2. اضغط **إضافة قالب جديد**
3. أدخل:
   - اسم القالب
   - نوع القالب
   - محتوى الرسالة
   - وصف مختصر
4. احفظ القالب

### تعديل قالب موجود
- اضغط على أيقونة **التعديل** بجانب القالب
- قم بالتعديلات المطلوبة
- احفظ التغييرات

## 🔍 نصائح للاستخدام الأمثل

### أفضل الممارسات
1. **استخدم المتغيرات**: لجعل الرسائل شخصية أكثر
2. **اختبر أولاً**: استخدم الوضع التجريبي قبل الإرسال الفعلي
3. **راجع المعاينة**: تأكد من صحة الرسالة قبل الإرسال
4. **تابع السجلات**: راقب حالة الرسائل المرسلة

### تجنب هذه الأخطاء
- ❌ إرسال رسائل متكررة للعميل نفسه
- ❌ استخدام معلومات خاطئة في المتغيرات
- ❌ إرسال رسائل في أوقات غير مناسبة
- ❌ نسيان مراجعة المعاينة قبل الإرسال

## 📞 الدعم والمساعدة

### في حالة وجود مشاكل
1. تحقق من سجل الخادم للأخطاء
2. تأكد من صحة إعدادات Twilio
3. جرب الوضع التجريبي أولاً
4. راجع دليل المستخدم الشامل

### ملفات مفيدة
- `WHATSAPP_TEMPLATES_ENHANCED.md` - دليل شامل
- `WHATSAPP_ENHANCEMENT_SUMMARY.md` - ملخص التحسينات
- `test_whatsapp.py` - اختبار النظام

---

**🎯 النظام جاهز للاستخدام الفوري!**

**📱 ابدأ بالوضع التجريبي لتتعلم النظام، ثم فعّل الإرسال الحقيقي عند الحاجة.**
