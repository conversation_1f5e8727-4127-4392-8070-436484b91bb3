/**
 * Notifications Management System
 * Real-time notification updates and management
 */

class NotificationManager {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.notificationSound = null;
        this.lastNotificationCount = 0;
        this.isVisible = true;
        
        this.init();
    }
    
    init() {
        // Initialize notification system
        this.setupEventListeners();
        this.startPeriodicUpdates();
        this.setupVisibilityChange();
        this.loadNotificationSound();
        
        // Get initial count
        this.updateNotificationCount();
        
        console.log('🔔 Notification Manager initialized');
    }
    
    setupEventListeners() {
        // Handle notification clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.notification-bell')) {
                this.handleNotificationBellClick(e);
            }
            
            if (e.target.closest('.mark-as-read')) {
                this.markAsRead(e.target.closest('.mark-as-read').dataset.notificationId);
            }
            
            if (e.target.closest('.delete-notification')) {
                this.deleteNotification(e.target.closest('.delete-notification').dataset.notificationId);
            }
        });
        
        // Handle page visibility change
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                this.updateNotificationCount();
            }
        });
    }
    
    setupVisibilityChange() {
        // Update more frequently when page is visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.updateInterval = 60000; // 1 minute when hidden
            } else {
                this.updateInterval = 30000; // 30 seconds when visible
                this.updateNotificationCount(); // Immediate update when page becomes visible
            }
        });
    }
    
    loadNotificationSound() {
        // Load notification sound (optional)
        try {
            this.notificationSound = new Audio('/static/sounds/notification.mp3');
            this.notificationSound.volume = 0.5;
        } catch (e) {
            console.log('Notification sound not available');
        }
    }
    
    startPeriodicUpdates() {
        // Update notification count periodically
        setInterval(() => {
            if (this.isVisible) {
                this.updateNotificationCount();
            }
        }, this.updateInterval);
    }
    
    async updateNotificationCount() {
        try {
            const response = await fetch('/notifications/api/unread_count');
            const data = await response.json();
            
            if (data.count !== undefined) {
                this.updateBadge(data.count);
                
                // Play sound if new notifications
                if (data.count > this.lastNotificationCount && this.lastNotificationCount > 0) {
                    this.playNotificationSound();
                    this.showDesktopNotification('إشعار جديد', 'لديك إشعارات جديدة في النظام');
                }
                
                this.lastNotificationCount = data.count;
            }
        } catch (error) {
            console.error('Error updating notification count:', error);
        }
    }
    
    updateBadge(count) {
        const badges = document.querySelectorAll('.notification-badge');
        
        badges.forEach(badge => {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline-block';
                badge.classList.add('animate-pulse');
                
                // Remove animation after 2 seconds
                setTimeout(() => {
                    badge.classList.remove('animate-pulse');
                }, 2000);
            } else {
                badge.style.display = 'none';
            }
        });
        
        // Update page title
        this.updatePageTitle(count);
    }
    
    updatePageTitle(count) {
        const originalTitle = document.title.replace(/^\(\d+\)\s*/, '');
        
        if (count > 0) {
            document.title = `(${count}) ${originalTitle}`;
        } else {
            document.title = originalTitle;
        }
    }
    
    playNotificationSound() {
        if (this.notificationSound) {
            this.notificationSound.play().catch(e => {
                console.log('Could not play notification sound:', e);
            });
        }
    }
    
    showDesktopNotification(title, message) {
        // Request permission for desktop notifications
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification(title, {
                    body: message,
                    icon: '/static/images/logo.png',
                    tag: 'car-dealership-notification'
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification(title, {
                            body: message,
                            icon: '/static/images/logo.png',
                            tag: 'car-dealership-notification'
                        });
                    }
                });
            }
        }
    }
    
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`/notifications/mark_read/${notificationId}`);
            const data = await response.json();
            
            if (data.success) {
                // Update UI
                const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (notificationElement) {
                    notificationElement.classList.remove('unread');
                    
                    const badge = notificationElement.querySelector('.badge');
                    if (badge) {
                        badge.remove();
                    }
                }
                
                // Update count
                this.updateNotificationCount();
                
                this.showToast('تم تحديد الإشعار كمقروء', 'success');
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
            this.showToast('حدث خطأ أثناء تحديث الإشعار', 'error');
        }
    }
    
    async deleteNotification(notificationId) {
        if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
            return;
        }
        
        try {
            const response = await fetch(`/notifications/delete/${notificationId}`);
            const data = await response.json();
            
            if (data.success) {
                // Remove from UI with animation
                const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (notificationElement) {
                    notificationElement.style.transition = 'all 0.3s ease';
                    notificationElement.style.opacity = '0';
                    notificationElement.style.transform = 'translateX(100%)';
                    
                    setTimeout(() => {
                        notificationElement.remove();
                    }, 300);
                }
                
                // Update count
                this.updateNotificationCount();
                
                this.showToast('تم حذف الإشعار', 'success');
            }
        } catch (error) {
            console.error('Error deleting notification:', error);
            this.showToast('حدث خطأ أثناء حذف الإشعار', 'error');
        }
    }
    
    async getLatestNotifications() {
        try {
            const response = await fetch('/notifications/api/latest');
            const data = await response.json();
            
            return data.notifications || [];
        } catch (error) {
            console.error('Error fetching latest notifications:', error);
            return [];
        }
    }
    
    showNotificationDropdown() {
        // Show notification dropdown with latest notifications
        this.getLatestNotifications().then(notifications => {
            this.renderNotificationDropdown(notifications);
        });
    }
    
    renderNotificationDropdown(notifications) {
        const dropdown = document.getElementById('notificationDropdown');
        if (!dropdown) return;
        
        let html = '';
        
        if (notifications.length === 0) {
            html = `
                <div class="dropdown-item text-center text-muted py-3">
                    <i class="fas fa-bell-slash mb-2"></i><br>
                    لا توجد إشعارات
                </div>
            `;
        } else {
            notifications.forEach(notification => {
                const typeIcon = this.getNotificationIcon(notification.type);
                const typeClass = this.getNotificationClass(notification.type);
                
                html += `
                    <div class="dropdown-item notification-item ${notification.is_read ? '' : 'unread'}" 
                         data-notification-id="${notification.id}">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-${typeIcon} ${typeClass} me-2 mt-1"></i>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 text-truncate">${notification.title}</h6>
                                <p class="mb-1 small text-muted">${notification.message}</p>
                                <small class="text-muted">${notification.time_ago}</small>
                            </div>
                            ${!notification.is_read ? '<span class="badge bg-primary ms-2">جديد</span>' : ''}
                        </div>
                    </div>
                `;
            });
            
            html += `
                <div class="dropdown-divider"></div>
                <a class="dropdown-item text-center" href="/notifications">
                    <i class="fas fa-eye me-2"></i>
                    عرض جميع الإشعارات
                </a>
            `;
        }
        
        dropdown.innerHTML = html;
    }
    
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'bell';
    }
    
    getNotificationClass(type) {
        const classes = {
            'success': 'text-success',
            'warning': 'text-warning',
            'error': 'text-danger',
            'info': 'text-info'
        };
        return classes[type] || 'text-primary';
    }
    
    showToast(message, type = 'info') {
        // Create and show toast notification
        const toastContainer = document.getElementById('toastContainer') || this.createToastContainer();
        
        const toastId = 'toast-' + Date.now();
        const toastClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
        
        const toastHtml = `
            <div id="${toastId}" class="toast ${toastClass} text-white" role="alert">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'} me-2"></i>
                    ${message}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();
        
        // Remove toast after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
    
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }
    
    handleNotificationBellClick(e) {
        e.preventDefault();
        this.showNotificationDropdown();
    }
}

// Initialize notification manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .notification-badge {
        animation-duration: 0.5s;
        animation-fill-mode: both;
    }
    
    .animate-pulse {
        animation-name: pulse;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .notification-item.unread {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
    }
    
    .notification-item:hover {
        background-color: #f8f9fa;
    }
    
    .toast-container .toast {
        margin-bottom: 0.5rem;
    }
`;
document.head.appendChild(style);
