from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Notification, User, Sale, Installment, Customer
from datetime import datetime, date, timedelta
import json

notifications_bp = Blueprint('notifications', __name__)

class NotificationManager:
    """Notification management system"""
    
    @staticmethod
    def create_notification(user_id, title, message, notification_type='info', data=None):
        """Create a new notification"""
        try:
            notification = Notification(
                user_id=user_id,
                title=title,
                message=message,
                type=notification_type,
                is_read=False,
                created_at=datetime.utcnow()
            )
            
            db.session.add(notification)
            db.session.commit()
            
            return notification
        except Exception as e:
            db.session.rollback()
            print(f"Error creating notification: {e}")
            return None
    
    @staticmethod
    def create_system_notification(title, message, notification_type='info'):
        """Create notification for all users"""
        try:
            users = User.query.all()
            notifications = []
            
            for user in users:
                notification = Notification(
                    user_id=user.id,
                    title=title,
                    message=message,
                    type=notification_type,
                    is_read=False,
                    created_at=datetime.utcnow()
                )
                notifications.append(notification)
            
            db.session.add_all(notifications)
            db.session.commit()
            
            return len(notifications)
        except Exception as e:
            db.session.rollback()
            print(f"Error creating system notification: {e}")
            return 0
    
    @staticmethod
    def create_payment_reminder(installment):
        """Create payment reminder notification"""
        try:
            # Get users who should receive payment reminders
            managers = User.query.filter(User.role.in_(['manager', 'admin'])).all()
            
            title = f"تذكير بقسط مستحق - {installment.sale.customer.full_name}"
            message = f"""
قسط مستحق للعميل {installment.sale.customer.full_name}

تفاصيل القسط:
• رقم القسط: {installment.installment_number}
• تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
• المبلغ: {installment.amount:,.0f} ريال قطري
• السيارة: {installment.sale.car.brand} {installment.sale.car.model}

يرجى متابعة العميل لسداد القسط.
"""
            
            for manager in managers:
                NotificationManager.create_notification(
                    manager.id, title, message, 'warning'
                )
            
            return len(managers)
        except Exception as e:
            print(f"Error creating payment reminder: {e}")
            return 0
    
    @staticmethod
    def create_overdue_notification(installment):
        """Create overdue payment notification"""
        try:
            managers = User.query.filter(User.role.in_(['manager', 'admin'])).all()
            
            days_overdue = (date.today() - installment.due_date).days
            
            title = f"قسط متأخر {days_overdue} يوم - {installment.sale.customer.full_name}"
            message = f"""
⚠️ قسط متأخر يتطلب إجراء عاجل

تفاصيل القسط المتأخر:
• العميل: {installment.sale.customer.full_name}
• رقم الهاتف: {installment.sale.customer.phone}
• رقم القسط: {installment.installment_number}
• تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
• عدد أيام التأخير: {days_overdue} يوم
• المبلغ: {installment.amount:,.0f} ريال قطري
• السيارة: {installment.sale.car.brand} {installment.sale.car.model}

الإجراءات المطلوبة:
• التواصل مع العميل فوراً
• إرسال إشعار تأخير
• تحديد خطة السداد
"""
            
            for manager in managers:
                NotificationManager.create_notification(
                    manager.id, title, message, 'error'
                )
            
            return len(managers)
        except Exception as e:
            print(f"Error creating overdue notification: {e}")
            return 0
    
    @staticmethod
    def create_sale_notification(sale):
        """Create new sale notification"""
        try:
            managers = User.query.filter(User.role.in_(['manager', 'admin'])).all()
            
            title = f"عملية بيع جديدة - {sale.customer.full_name}"
            message = f"""
🎉 تم إتمام عملية بيع جديدة

تفاصيل البيع:
• العميل: {sale.customer.full_name}
• السيارة: {sale.car.brand} {sale.car.model} {sale.car.year}
• سعر البيع: {sale.sale_price:,.0f} ريال قطري
• نوع البيع: {'تقسيط' if sale.sale_type == 'installment' else 'نقدي'}
• تاريخ البيع: {sale.sale_date.strftime('%Y/%m/%d')}

{f'• الدفعة المقدمة: {sale.down_payment:,.0f} ريال' if sale.sale_type == 'installment' else ''}
{f'• عدد الأقساط: {sale.installment_count}' if sale.sale_type == 'installment' else ''}
"""
            
            for manager in managers:
                NotificationManager.create_notification(
                    manager.id, title, message, 'success'
                )
            
            return len(managers)
        except Exception as e:
            print(f"Error creating sale notification: {e}")
            return 0

@notifications_bp.route('/')
@login_required
def index():
    """Display notifications"""
    page = request.args.get('page', 1, type=int)
    filter_type = request.args.get('type', '')
    filter_read = request.args.get('read', '')
    
    query = Notification.query.filter_by(user_id=current_user.id)
    
    if filter_type:
        query = query.filter(Notification.type == filter_type)
    
    if filter_read == 'unread':
        query = query.filter(Notification.is_read == False)
    elif filter_read == 'read':
        query = query.filter(Notification.is_read == True)
    
    notifications = query.order_by(Notification.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Statistics
    total_notifications = Notification.query.filter_by(user_id=current_user.id).count()
    unread_notifications = Notification.query.filter_by(
        user_id=current_user.id, is_read=False
    ).count()
    
    # Today's notifications
    today = date.today()
    today_notifications = Notification.query.filter(
        Notification.user_id == current_user.id,
        Notification.created_at >= today
    ).count()
    
    return render_template('notifications/index.html',
                         notifications=notifications,
                         total_notifications=total_notifications,
                         unread_notifications=unread_notifications,
                         today_notifications=today_notifications,
                         filter_type=filter_type,
                         filter_read=filter_read)

@notifications_bp.route('/mark_read/<int:notification_id>')
@login_required
def mark_read(notification_id):
    """Mark notification as read"""
    notification = Notification.query.filter_by(
        id=notification_id, user_id=current_user.id
    ).first_or_404()
    
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})

@notifications_bp.route('/mark_all_read')
@login_required
def mark_all_read():
    """Mark all notifications as read"""
    Notification.query.filter_by(
        user_id=current_user.id, is_read=False
    ).update({'is_read': True})
    
    db.session.commit()
    
    flash('تم تحديد جميع الإشعارات كمقروءة', 'success')
    return redirect(url_for('notifications.index'))

@notifications_bp.route('/delete/<int:notification_id>')
@login_required
def delete(notification_id):
    """Delete notification"""
    notification = Notification.query.filter_by(
        id=notification_id, user_id=current_user.id
    ).first_or_404()
    
    db.session.delete(notification)
    db.session.commit()
    
    return jsonify({'success': True})

@notifications_bp.route('/delete_all_read')
@login_required
def delete_all_read():
    """Delete all read notifications"""
    deleted_count = Notification.query.filter_by(
        user_id=current_user.id, is_read=True
    ).delete()
    
    db.session.commit()
    
    flash(f'تم حذف {deleted_count} إشعار مقروء', 'success')
    return redirect(url_for('notifications.index'))

@notifications_bp.route('/api/unread_count')
@login_required
def unread_count():
    """Get unread notifications count via API"""
    count = Notification.query.filter_by(
        user_id=current_user.id, is_read=False
    ).count()
    
    return jsonify({'count': count})

@notifications_bp.route('/api/latest')
@login_required
def latest():
    """Get latest notifications via API"""
    notifications = Notification.query.filter_by(
        user_id=current_user.id
    ).order_by(Notification.created_at.desc()).limit(5).all()
    
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message[:100] + '...' if len(notification.message) > 100 else notification.message,
            'type': notification.type,
            'is_read': notification.is_read,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M'),
            'time_ago': get_time_ago(notification.created_at)
        })
    
    return jsonify({'notifications': notifications_data})

def get_time_ago(datetime_obj):
    """Get human readable time ago"""
    now = datetime.utcnow()
    diff = now - datetime_obj
    
    if diff.days > 0:
        return f"منذ {diff.days} يوم"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"منذ {hours} ساعة"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"منذ {minutes} دقيقة"
    else:
        return "الآن"

# Auto-generate notifications for overdue payments
def check_overdue_payments():
    """Check for overdue payments and create notifications"""
    try:
        today = date.today()
        
        # Get overdue installments
        overdue_installments = Installment.query.filter(
            Installment.due_date < today,
            Installment.status == 'pending'
        ).all()
        
        for installment in overdue_installments:
            # Check if notification already exists for this installment today
            existing_notification = Notification.query.filter(
                Notification.title.contains(installment.sale.customer.full_name),
                Notification.created_at >= today
            ).first()
            
            if not existing_notification:
                NotificationManager.create_overdue_notification(installment)
        
        # Get upcoming payments (3 days before due date)
        upcoming_date = today + timedelta(days=3)
        upcoming_installments = Installment.query.filter(
            Installment.due_date == upcoming_date,
            Installment.status == 'pending'
        ).all()
        
        for installment in upcoming_installments:
            # Check if reminder notification already exists
            existing_reminder = Notification.query.filter(
                Notification.title.contains(f"تذكير بقسط مستحق - {installment.sale.customer.full_name}"),
                Notification.created_at >= today
            ).first()
            
            if not existing_reminder:
                NotificationManager.create_payment_reminder(installment)
        
        return True
    except Exception as e:
        print(f"Error checking overdue payments: {e}")
        return False
