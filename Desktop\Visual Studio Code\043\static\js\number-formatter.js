/**
 * Number Formatter for Qatar Car Dealership System
 * Handles Arabic number formatting and currency display
 */

class NumberFormatter {
    constructor() {
        this.arabicDigits = {
            '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
            '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
        };
        
        this.englishDigits = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        };
        
        this.init();
    }
    
    init() {
        // Auto-format numbers on page load
        this.formatAllNumbers();
        
        // Setup input formatters
        this.setupInputFormatters();
        
        // Setup real-time formatting
        this.setupRealTimeFormatting();
        
        console.log('📊 Number Formatter initialized');
    }
    
    /**
     * Safely parse a number, handling precision issues
     */
    safeParseNumber(value) {
        if (value === null || value === undefined || value === '') {
            return 0;
        }
        
        // Convert to string first
        let str = String(value);
        
        // Convert Arabic digits to English
        for (let arabic in this.englishDigits) {
            str = str.replace(new RegExp(arabic, 'g'), this.englishDigits[arabic]);
        }
        
        // Remove any non-numeric characters except decimal point and minus
        str = str.replace(/[^\d.-]/g, '');
        
        // Parse as float
        let num = parseFloat(str);
        
        // Handle NaN
        if (isNaN(num)) {
            return 0;
        }
        
        // Handle precision issues
        if (Math.abs(num - Math.round(num)) < 0.0001) {
            return Math.round(num);
        }
        
        // Round to 2 decimal places
        return Math.round(num * 100) / 100;
    }
    
    /**
     * Format number with commas
     */
    formatNumber(number, options = {}) {
        const {
            useArabicDigits = false,
            decimals = 0,
            showZero = true
        } = options;
        
        const num = this.safeParseNumber(number);
        
        if (num === 0 && !showZero) {
            return '';
        }
        
        // Format with commas
        let formatted;
        if (decimals > 0 && num % 1 !== 0) {
            formatted = num.toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: decimals
            });
        } else {
            formatted = Math.floor(num).toLocaleString('en-US');
        }
        
        // Convert to Arabic digits if requested
        if (useArabicDigits) {
            for (let english in this.arabicDigits) {
                formatted = formatted.replace(new RegExp(english, 'g'), this.arabicDigits[english]);
            }
        }
        
        return formatted;
    }
    
    /**
     * Format currency for Qatar
     */
    formatQatarCurrency(amount, options = {}) {
        const {
            useArabicDigits = false,
            shortForm = false
        } = options;
        
        const num = this.safeParseNumber(amount);
        
        if (num === 0) {
            return useArabicDigits ? '٠ ر.ق' : '0 ريال قطري';
        }
        
        const formatted = this.formatNumber(num, { useArabicDigits });
        
        if (shortForm) {
            return `${formatted} ر.ق`;
        } else {
            return `${formatted} ريال قطري`;
        }
    }
    
    /**
     * Convert English digits to Arabic
     */
    toArabicDigits(text) {
        let result = String(text);
        for (let english in this.arabicDigits) {
            result = result.replace(new RegExp(english, 'g'), this.arabicDigits[english]);
        }
        return result;
    }
    
    /**
     * Convert Arabic digits to English
     */
    toEnglishDigits(text) {
        let result = String(text);
        for (let arabic in this.englishDigits) {
            result = result.replace(new RegExp(arabic, 'g'), this.englishDigits[arabic]);
        }
        return result;
    }
    
    /**
     * Format all numbers on the page
     */
    formatAllNumbers() {
        // Format currency elements
        document.querySelectorAll('.currency, [data-currency]').forEach(element => {
            const value = element.textContent || element.dataset.currency;
            const useArabic = element.classList.contains('arabic-digits');
            const shortForm = element.classList.contains('short-form');
            
            element.textContent = this.formatQatarCurrency(value, {
                useArabicDigits: useArabic,
                shortForm: shortForm
            });
        });
        
        // Format number elements
        document.querySelectorAll('.number, [data-number]').forEach(element => {
            const value = element.textContent || element.dataset.number;
            const useArabic = element.classList.contains('arabic-digits');
            const decimals = parseInt(element.dataset.decimals) || 0;
            
            element.textContent = this.formatNumber(value, {
                useArabicDigits: useArabic,
                decimals: decimals
            });
        });
        
        // Format percentage elements
        document.querySelectorAll('.percentage, [data-percentage]').forEach(element => {
            const value = element.textContent || element.dataset.percentage;
            const useArabic = element.classList.contains('arabic-digits');
            
            const formatted = this.formatNumber(value, {
                useArabicDigits: useArabic,
                decimals: 1
            });
            
            element.textContent = `${formatted}%`;
        });
    }
    
    /**
     * Setup input formatters
     */
    setupInputFormatters() {
        // Currency inputs
        document.querySelectorAll('input[data-format="currency"]').forEach(input => {
            this.setupCurrencyInput(input);
        });
        
        // Number inputs
        document.querySelectorAll('input[data-format="number"]').forEach(input => {
            this.setupNumberInput(input);
        });
        
        // Phone inputs (Qatar format)
        document.querySelectorAll('input[data-format="phone"]').forEach(input => {
            this.setupPhoneInput(input);
        });
        
        // ID inputs (Qatar format)
        document.querySelectorAll('input[data-format="qatar-id"]').forEach(input => {
            this.setupQatarIdInput(input);
        });
    }
    
    /**
     * Setup currency input formatting
     */
    setupCurrencyInput(input) {
        input.addEventListener('input', (e) => {
            let value = e.target.value;
            
            // Remove all non-numeric characters
            value = value.replace(/[^\d]/g, '');
            
            if (value) {
                // Format as currency
                const num = parseInt(value);
                e.target.value = this.formatNumber(num);
            }
        });
        
        input.addEventListener('blur', (e) => {
            let value = e.target.value;
            if (value) {
                const num = this.safeParseNumber(value);
                e.target.value = this.formatNumber(num);
            }
        });
    }
    
    /**
     * Setup number input formatting
     */
    setupNumberInput(input) {
        input.addEventListener('input', (e) => {
            let value = e.target.value;
            
            // Allow only numbers and decimal point
            value = value.replace(/[^\d.]/g, '');
            
            // Ensure only one decimal point
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }
            
            e.target.value = value;
        });
        
        input.addEventListener('blur', (e) => {
            let value = e.target.value;
            if (value) {
                const num = this.safeParseNumber(value);
                const decimals = parseInt(input.dataset.decimals) || 0;
                e.target.value = this.formatNumber(num, { decimals });
            }
        });
    }
    
    /**
     * Setup Qatar phone input formatting
     */
    setupPhoneInput(input) {
        input.addEventListener('input', (e) => {
            let value = e.target.value;
            
            // Remove all non-numeric characters
            value = value.replace(/\D/g, '');
            
            // Limit to 8 digits for Qatar
            if (value.length > 8) {
                value = value.substring(0, 8);
            }
            
            e.target.value = value;
        });
    }
    
    /**
     * Setup Qatar ID input formatting
     */
    setupQatarIdInput(input) {
        input.addEventListener('input', (e) => {
            let value = e.target.value;
            
            // Remove all non-numeric characters
            value = value.replace(/\D/g, '');
            
            // Limit to 11 digits for Qatar ID
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            e.target.value = value;
        });
    }
    
    /**
     * Setup real-time formatting for dynamic content
     */
    setupRealTimeFormatting() {
        // Watch for DOM changes and format new numbers
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.formatElementNumbers(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * Format numbers in a specific element
     */
    formatElementNumbers(element) {
        // Format currency in the element
        element.querySelectorAll('.currency, [data-currency]').forEach(el => {
            const value = el.textContent || el.dataset.currency;
            const useArabic = el.classList.contains('arabic-digits');
            const shortForm = el.classList.contains('short-form');
            
            el.textContent = this.formatQatarCurrency(value, {
                useArabicDigits: useArabic,
                shortForm: shortForm
            });
        });
        
        // Format numbers in the element
        element.querySelectorAll('.number, [data-number]').forEach(el => {
            const value = el.textContent || el.dataset.number;
            const useArabic = el.classList.contains('arabic-digits');
            const decimals = parseInt(el.dataset.decimals) || 0;
            
            el.textContent = this.formatNumber(value, {
                useArabicDigits: useArabic,
                decimals: decimals
            });
        });
    }
    
    /**
     * Validate Qatar phone number
     */
    validateQatarPhone(phone) {
        const cleaned = this.toEnglishDigits(phone).replace(/\D/g, '');
        return cleaned.length === 8 && /^[3-7]/.test(cleaned);
    }
    
    /**
     * Validate Qatar ID number
     */
    validateQatarId(id) {
        const cleaned = this.toEnglishDigits(id).replace(/\D/g, '');
        return cleaned.length === 11;
    }
    
    /**
     * Get raw number value from formatted text
     */
    getRawValue(formattedText) {
        return this.safeParseNumber(formattedText);
    }
}

// Initialize number formatter when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.numberFormatter = new NumberFormatter();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NumberFormatter;
}
