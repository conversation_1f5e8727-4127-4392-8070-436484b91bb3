{% extends "base.html" %}

{% block title %}تغيير كلمة المرور - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تغيير كلمة المرور</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للملف الشخصي
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تغيير كلمة المرور</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                        
                        <!-- Password Strength Indicator -->
                        <div class="mt-2">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="passwordStrengthText" class="text-muted"></small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="passwordMatch" class="form-text"></div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key me-2"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">نصائح الأمان</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-shield-alt me-2"></i>كلمة مرور قوية يجب أن تحتوي على:</h6>
                    <ul class="mb-0">
                        <li>6 أحرف على الأقل</li>
                        <li>أحرف كبيرة وصغيرة</li>
                        <li>أرقام</li>
                        <li>رموز خاصة (!@#$%^&*)</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تجنب:</h6>
                    <ul class="mb-0">
                        <li>استخدام معلومات شخصية</li>
                        <li>كلمات المرور الشائعة</li>
                        <li>مشاركة كلمة المرور مع الآخرين</li>
                        <li>استخدام نفس كلمة المرور في مواقع أخرى</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 6) strength += 1;
    if (password.length >= 8) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');
    
    switch (strength) {
        case 0:
        case 1:
            strengthBar.style.width = '20%';
            strengthBar.className = 'progress-bar bg-danger';
            feedback = 'ضعيفة جداً';
            break;
        case 2:
            strengthBar.style.width = '40%';
            strengthBar.className = 'progress-bar bg-danger';
            feedback = 'ضعيفة';
            break;
        case 3:
            strengthBar.style.width = '60%';
            strengthBar.className = 'progress-bar bg-warning';
            feedback = 'متوسطة';
            break;
        case 4:
            strengthBar.style.width = '80%';
            strengthBar.className = 'progress-bar bg-info';
            feedback = 'جيدة';
            break;
        case 5:
        case 6:
            strengthBar.style.width = '100%';
            strengthBar.className = 'progress-bar bg-success';
            feedback = 'قوية';
            break;
    }
    
    strengthText.textContent = feedback;
    return strength;
}

$(document).ready(function() {
    // Check password strength
    $('#new_password').on('input', function() {
        const password = $(this).val();
        checkPasswordStrength(password);
        checkPasswordMatch();
    });
    
    // Check password match
    $('#confirm_password').on('input', function() {
        checkPasswordMatch();
    });
    
    function checkPasswordMatch() {
        const newPassword = $('#new_password').val();
        const confirmPassword = $('#confirm_password').val();
        const matchDiv = $('#passwordMatch');
        
        if (confirmPassword === '') {
            matchDiv.text('');
            return;
        }
        
        if (newPassword === confirmPassword) {
            matchDiv.html('<i class="fas fa-check text-success me-1"></i>كلمات المرور متطابقة');
        } else {
            matchDiv.html('<i class="fas fa-times text-danger me-1"></i>كلمات المرور غير متطابقة');
        }
    }
    
    // Form validation
    $('#changePasswordForm').on('submit', function(e) {
        const newPassword = $('#new_password').val();
        const confirmPassword = $('#confirm_password').val();
        
        if (newPassword.length < 6) {
            alert('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
            e.preventDefault();
            return;
        }
        
        if (newPassword !== confirmPassword) {
            alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
            e.preventDefault();
            return;
        }
        
        const strength = checkPasswordStrength(newPassword);
        if (strength < 3) {
            if (!confirm('كلمة المرور ضعيفة. هل تريد المتابعة؟')) {
                e.preventDefault();
                return;
            }
        }
    });
});
</script>
{% endblock %}
