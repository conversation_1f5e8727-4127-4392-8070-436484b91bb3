#!/usr/bin/env python3
"""
Test script for WhatsApp templates functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test basic imports
        from flask import Flask
        print("✓ Flask imported successfully")
        
        from models import db, Customer, WhatsAppMessage
        print("✓ Models imported successfully")
        
        from whatsapp import whatsapp_bp, get_available_templates, process_message_variables
        print("✓ WhatsApp module imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_templates():
    """Test template functionality"""
    try:
        print("\nTesting templates...")
        
        from whatsapp import get_available_templates
        templates = get_available_templates()
        
        print(f"✓ Found {len(templates)} templates")
        
        for template in templates:
            print(f"  - {template.name} ({template.type})")
        
        return True
    except Exception as e:
        print(f"✗ Template error: {e}")
        return False

def test_message_processing():
    """Test message variable processing"""
    try:
        print("\nTesting message processing...")
        
        from whatsapp import process_message_variables
        
        # Test message with variables
        test_message = "مرحبا {customer_name}، تاريخ اليوم هو {today}"
        processed = process_message_variables(test_message)
        
        print(f"✓ Original: {test_message}")
        print(f"✓ Processed: {processed}")
        
        return True
    except Exception as e:
        print(f"✗ Message processing error: {e}")
        return False

def test_app_creation():
    """Test Flask app creation"""
    try:
        print("\nTesting app creation...")
        
        from app import create_app
        app = create_app()
        
        print("✓ Flask app created successfully")
        print(f"✓ App name: {app.name}")
        
        # Test blueprints
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        print(f"✓ Registered blueprints: {', '.join(blueprint_names)}")
        
        return True
    except Exception as e:
        print(f"✗ App creation error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== WhatsApp Templates Test Suite ===\n")
    
    tests = [
        test_imports,
        test_templates,
        test_message_processing,
        test_app_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! WhatsApp templates system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
