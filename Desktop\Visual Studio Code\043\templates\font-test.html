{% extends "base.html" %}

{% block title %}اختبار الخطوط العربية - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="mb-0">اختبار الخطوط العربية</h3>
                </div>
                <div class="card-body">
                    
                    <!-- Font Loading Status -->
                    <div class="alert alert-info" id="fontStatus">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري تحميل الخطوط...
                    </div>
                    
                    <!-- Primary Font Test -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>خط Cairo (الأساسي)</h5>
                                </div>
                                <div class="card-body" style="font-family: 'Cairo', sans-serif;">
                                    <h1>مرحباً بكم في معرض السيارات</h1>
                                    <h2>نظام إدارة شامل ومتطور</h2>
                                    <h3>خدمات متميزة للعملاء</h3>
                                    <p>هذا نص تجريبي لاختبار خط Cairo الذي يعتبر الخط الأساسي في النظام. يتميز هذا الخط بوضوحه وجماليته في عرض النصوص العربية.</p>
                                    <p><strong>نص عريض:</strong> معرض السيارات الحديث</p>
                                    <p><em>نص مائل:</em> أفضل الخدمات والأسعار</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>خط Noto Sans Arabic</h5>
                                </div>
                                <div class="card-body" style="font-family: 'Noto Sans Arabic', sans-serif;">
                                    <h1>مرحباً بكم في معرض السيارات</h1>
                                    <h2>نظام إدارة شامل ومتطور</h2>
                                    <h3>خدمات متميزة للعملاء</h3>
                                    <p>هذا نص تجريبي لاختبار خط Noto Sans Arabic الذي يعتبر خط احتياطي في النظام. يتميز بدعمه الممتاز للغة العربية.</p>
                                    <p><strong>نص عريض:</strong> معرض السيارات الحديث</p>
                                    <p><em>نص مائل:</em> أفضل الخدمات والأسعار</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Traditional Arabic Fonts -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>خط Noto Naskh Arabic (تقليدي)</h5>
                                </div>
                                <div class="card-body text-arabic">
                                    <h2>بسم الله الرحمن الرحيم</h2>
                                    <p>هذا نص تجريبي بالخط التقليدي العربي. يستخدم هذا الخط للنصوص الطويلة والمحتوى التقليدي. يتميز بجماليته وسهولة قراءته في النصوص الطويلة.</p>
                                    <p>يمكن استخدام هذا الخط في العقود والوثائق الرسمية والمراسلات المهمة التي تتطلب طابعاً رسمياً وتقليدياً.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>خط Amiri (كلاسيكي)</h5>
                                </div>
                                <div class="card-body" style="font-family: 'Amiri', serif;">
                                    <h2>الخط العربي الأصيل</h2>
                                    <p>خط أميري هو خط عربي كلاسيكي يتميز بجماليته الفائقة وأناقته في عرض النصوص العربية التراثية والكلاسيكية.</p>
                                    <p>يستخدم هذا الخط في المناسبات الخاصة والوثائق المهمة التي تتطلب لمسة من الأناقة والتراث العربي الأصيل.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Numbers and Currency Test -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>اختبار الأرقام</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>أرقام عادية:</strong></p>
                                    <p class="number">1234567890</p>
                                    <p class="number">٠١٢٣٤٥٦٧٨٩</p>
                                    
                                    <p><strong>أرقام منسقة:</strong></p>
                                    <p class="number">1,234,567</p>
                                    <p class="arabic-numbers">١٬٢٣٤٬٥٦٧</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>اختبار العملة</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>ريال قطري:</strong></p>
                                    <p class="currency">50000</p>
                                    <p class="currency">125000</p>
                                    <p class="currency">750000</p>
                                    
                                    <p><strong>مبالغ منسقة:</strong></p>
                                    <p>٥٠٬٠٠٠ ر.ق</p>
                                    <p>١٢٥٬٠٠٠ ر.ق</p>
                                    <p>٧٥٠٬٠٠٠ ر.ق</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>معلومات قطر</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>رقم الهوية:</strong></p>
                                    <p class="number">12345678901</p>
                                    
                                    <p><strong>رقم الهاتف:</strong></p>
                                    <p class="number">55123456</p>
                                    <p class="number">77654321</p>
                                    
                                    <p><strong>التاريخ:</strong></p>
                                    <p>٢٠٢٤/١٢/٢٥</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Elements Test -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>اختبار عناصر النموذج</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="testName" class="form-label">الاسم الكامل</label>
                                                    <input type="text" class="form-control" id="testName" placeholder="أدخل الاسم الكامل">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="testPhone" class="form-label">رقم الهاتف</label>
                                                    <input type="tel" class="form-control" id="testPhone" placeholder="55123456">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="testSelect" class="form-label">اختر نوع السيارة</label>
                                                    <select class="form-select" id="testSelect">
                                                        <option>اختر نوع السيارة</option>
                                                        <option>تويوتا</option>
                                                        <option>نيسان</option>
                                                        <option>هوندا</option>
                                                        <option>مرسيدس</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="testPrice" class="form-label">السعر</label>
                                                    <input type="number" class="form-control currency-input" id="testPrice" placeholder="50000">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="testTextarea" class="form-label">ملاحظات</label>
                                            <textarea class="form-control" id="testTextarea" rows="3" placeholder="أدخل ملاحظاتك هنا..."></textarea>
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-primary">حفظ البيانات</button>
                                            <button type="button" class="btn btn-secondary">إلغاء</button>
                                            <button type="button" class="btn btn-success">تأكيد</button>
                                            <button type="button" class="btn btn-warning">تحذير</button>
                                            <button type="button" class="btn btn-danger">حذف</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Font Information -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>معلومات الخطوط</h5>
                                </div>
                                <div class="card-body">
                                    <div id="fontInfo">
                                        <p><strong>حالة تحميل الخطوط:</strong> <span id="loadingStatus">جاري التحقق...</span></p>
                                        <p><strong>الخط المستخدم حالياً:</strong> <span id="currentFont">غير محدد</span></p>
                                        <p><strong>الخطوط المتاحة:</strong></p>
                                        <ul id="availableFonts">
                                            <li>جاري التحقق من الخطوط المتاحة...</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Check font loading status
    function updateFontStatus() {
        const statusElement = $('#fontStatus');
        const loadingStatusElement = $('#loadingStatus');
        const currentFontElement = $('#currentFont');
        const availableFontsElement = $('#availableFonts');
        
        if (window.fontManager && window.fontManager.isLoaded()) {
            statusElement.removeClass('alert-info').addClass('alert-success');
            statusElement.html('<i class="fas fa-check me-2"></i>تم تحميل الخطوط بنجاح!');
            loadingStatusElement.text('مكتمل');
        } else {
            loadingStatusElement.text('جاري التحميل...');
        }
        
        // Get current font
        const computedStyle = window.getComputedStyle(document.body);
        currentFontElement.text(computedStyle.fontFamily);
        
        // Check available fonts
        const fonts = ['Cairo', 'Noto Sans Arabic', 'Noto Naskh Arabic', 'Amiri'];
        const availableList = fonts.map(font => {
            const isAvailable = window.fontManager ? window.fontManager.isFontAvailable(font) : false;
            const status = isAvailable ? '✅' : '❌';
            return `<li>${status} ${font}</li>`;
        }).join('');
        
        availableFontsElement.html(availableList);
    }
    
    // Update status immediately
    updateFontStatus();
    
    // Listen for fonts loaded event
    document.addEventListener('fontsLoaded', function() {
        updateFontStatus();
    });
    
    // Update status every 2 seconds until fonts are loaded
    const statusInterval = setInterval(function() {
        updateFontStatus();
        if (window.fontManager && window.fontManager.isLoaded()) {
            clearInterval(statusInterval);
        }
    }, 2000);
    
    // Test currency formatting
    $('.currency').each(function() {
        const amount = parseFloat($(this).text());
        if (!isNaN(amount) && window.fontManager) {
            $(this).text(window.fontManager.formatQatarCurrency(amount));
        }
    });
    
    // Test number formatting
    $('.number').each(function() {
        const number = parseFloat($(this).text().replace(/[^\d]/g, ''));
        if (!isNaN(number) && window.fontManager) {
            $(this).text(window.fontManager.formatArabicNumber(number));
        }
    });
});
</script>
{% endblock %}
