# دليل استخدام الخطوط في العقود

## الخطوط المتاحة

### 1. Cairo (الخط الأساسي)
- **الاستخدام:** العناوين والنصوص الحديثة
- **المميزات:** واضح، عصري، متعدد الأوزان
- **الحجم المقترح:** 16-24px للعناوين، 12-16px للنصوص

### 2. Noto Sans Arabic
- **الاستخدام:** النصوص العامة والمحتوى الطويل
- **المميزات:** قراءة ممتازة، دعم شامل للعربية
- **الحجم المقترح:** 14-18px للنصوص العادية

### 3. Noto Naskh Arabic
- **الاستخدام:** النصوص التقليدية والرسمية
- **المميزات:** طابع تقليدي، مناسب للوثائق الرسمية
- **الحجم المقترح:** 14-16px للنصوص التقليدية

### 4. Amiri
- **الاستخدام:** النصوص الكلاسيكية والعناوين المميزة
- **المميزات:** جمالية عالية، طابع تراثي
- **الحجم المقترح:** 16-20px للعناوين الخاصة

## إرشادات الاستخدام

### في PDF:
```python
# تسجيل الخط
pdfmetrics.registerFont(TTFont('Cairo', 'static/fonts/Cairo[slnt,wght].ttf'))

# استخدام الخط
c.setFont('Cairo', 16)
c.drawRightString(x, y, text)
```

### في Word:
```python
# تطبيق الخط
run.font.name = 'Cairo'
run.font.size = Inches(0.2)
```

## أفضل الممارسات

1. **استخدم Cairo للعناوين الرئيسية**
2. **استخدم Noto Sans Arabic للنصوص العامة**
3. **استخدم Noto Naskh للشروط والأحكام**
4. **استخدم Amiri للتوقيعات والعناوين المميزة**

## حل المشاكل الشائعة

### مشكلة: الخط لا يظهر في PDF
- تأكد من تسجيل الخط قبل الاستخدام
- تحقق من مسار ملف الخط
- استخدم prepare_arabic_text() للنصوص العربية

### مشكلة: الخط لا يظهر في Word
- تأكد من تثبيت الخط في النظام
- استخدم الاسم الصحيح للخط
- تحقق من ترميز النص (UTF-8)

## الدعم الفني

للمساعدة في مشاكل الخطوط:
1. راجع هذا الدليل
2. استخدم enhance_contracts_fonts.py للاختبار
3. تحقق من سجلات الأخطاء
