{% extends "base.html" %}

{% block title %}اختبار أسعار السيارات - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-car me-2"></i>
                        اختبار أسعار السيارات
                    </h3>
                </div>
                <div class="card-body">
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>اختبار مخصص لحقول الأسعار في إضافة السيارة</h6>
                        <p class="mb-0">
                            هذه الصفحة تحاكي نفس حقول الأسعار الموجودة في صفحة إضافة السيارة.
                            جرب إدخال أرقام مختلفة لاختبار عمل النظام.
                        </p>
                    </div>

                    <form id="carPriceTestForm">
                        <div class="row">
                            <!-- Sale Price -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control currency-input" id="price"
                                               name="price" data-format="currency" required
                                               placeholder="أدخل السعر" autocomplete="off">
                                        <span class="input-group-text">ريال قطري</span>
                                    </div>
                                    <div class="form-text">هذا هو نفس حقل سعر البيع في إضافة السيارة</div>
                                </div>
                            </div>
                            
                            <!-- Cost Price -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="cost_price" class="form-label">سعر التكلفة</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control currency-input" id="cost_price"
                                               name="cost_price" data-format="currency"
                                               placeholder="أدخل سعر التكلفة" autocomplete="off">
                                        <span class="input-group-text">ريال قطري</span>
                                    </div>
                                    <div class="form-text">اختياري - للتقارير المالية</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <button type="button" class="btn btn-primary" onclick="testCarPrices()">
                                        <i class="fas fa-check me-2"></i>
                                        اختبار الأسعار
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-2" onclick="clearCarForm()">
                                        <i class="fas fa-eraser me-2"></i>
                                        مسح الحقول
                                    </button>
                                    <button type="button" class="btn btn-info ms-2" onclick="fillCarSampleData()">
                                        <i class="fas fa-magic me-2"></i>
                                        بيانات تجريبية
                                    </button>
                                    <a href="{{ url_for('cars.add') }}" class="btn btn-success ms-2">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة سيارة حقيقية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Test Results -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">نتائج اختبار أسعار السيارات</h6>
                                </div>
                                <div class="card-body">
                                    <div id="carTestResults">
                                        <p class="text-muted">اضغط "اختبار الأسعار" لرؤية النتائج</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Info -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">معلومات التشخيص</h6>
                                </div>
                                <div class="card-body">
                                    <div id="carDebugInfo">
                                        <p><strong>Price Input Handler:</strong> <span id="priceHandlerStatus">جاري التحقق...</span></p>
                                        <p><strong>Cost Price Handler:</strong> <span id="costPriceHandlerStatus">جاري التحقق...</span></p>
                                        <p><strong>Simple Price Handler:</strong> <span id="simplePriceHandlerStatus">جاري التحقق...</span></p>
                                        <p><strong>jQuery:</strong> <span id="jqueryStatus">جاري التحقق...</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">تعليمات الاختبار</h6>
                                </div>
                                <div class="card-body">
                                    <h6>أمثلة للاختبار:</h6>
                                    <ul>
                                        <li><strong>أدخل:</strong> 50000 → <strong>النتيجة المتوقعة:</strong> 50,000</li>
                                        <li><strong>أدخل:</strong> ٧٥٥٠٠ → <strong>النتيجة المتوقعة:</strong> 75,500</li>
                                        <li><strong>أدخل:</strong> 100000.50 → <strong>النتيجة المتوقعة:</strong> 100,000.5</li>
                                        <li><strong>أدخل:</strong> abc123def → <strong>النتيجة المتوقعة:</strong> 123</li>
                                    </ul>
                                    
                                    <h6 class="mt-3">خطوات الاختبار:</h6>
                                    <ol>
                                        <li>أدخل رقم في حقل سعر البيع</li>
                                        <li>اضغط خارج الحقل (أو Tab)</li>
                                        <li>شاهد التنسيق التلقائي</li>
                                        <li>كرر مع حقل سعر التكلفة</li>
                                        <li>اضغط "اختبار الأسعار" للتحقق</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update debug info
    updateCarDebugInfo();
    
    // DIRECT PRICE INPUT FIX - Same as in cars/add.html
    function setupPriceInput() {
        const priceInput = $('#price');
        
        if (priceInput.length) {
            console.log('🔧 Setting up price input directly...');
            
            // Remove any existing handlers
            priceInput.off('input focus blur');
            
            // Input event - clean as user types
            priceInput.on('input', function() {
                let value = $(this).val();
                
                // Convert Arabic digits to English
                value = value.replace(/[٠-٩]/g, function(d) {
                    return '٠١٢٣٤٥٦٧٨٩'.indexOf(d);
                });
                
                // Remove non-numeric characters except decimal point
                value = value.replace(/[^\d.]/g, '');
                
                // Handle multiple decimal points
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                
                $(this).val(value);
                $(this).removeClass('is-invalid');
            });
            
            // Focus event - remove formatting
            priceInput.on('focus', function() {
                let value = $(this).val().replace(/[^\d.]/g, '');
                $(this).val(value);
            });
            
            // Blur event - format and validate
            priceInput.on('blur', function() {
                let value = parseFloat($(this).val());
                if (!isNaN(value) && value > 0) {
                    // Format with commas
                    $(this).val(value.toLocaleString('en-US'));
                }
            });
            
            console.log('✅ Price input setup complete');
        }
    }
    
    // Setup cost price input too
    function setupCostPriceInput() {
        const costPriceInput = $('#cost_price');
        
        if (costPriceInput.length) {
            console.log('🔧 Setting up cost price input directly...');
            
            // Remove any existing handlers
            costPriceInput.off('input focus blur');
            
            // Input event - clean as user types
            costPriceInput.on('input', function() {
                let value = $(this).val();
                
                // Convert Arabic digits to English
                value = value.replace(/[٠-٩]/g, function(d) {
                    return '٠١٢٣٤٥٦٧٨٩'.indexOf(d);
                });
                
                // Remove non-numeric characters except decimal point
                value = value.replace(/[^\d.]/g, '');
                
                // Handle multiple decimal points
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                
                $(this).val(value);
                $(this).removeClass('is-invalid');
            });
            
            // Focus event - remove formatting
            costPriceInput.on('focus', function() {
                let value = $(this).val().replace(/[^\d.]/g, '');
                $(this).val(value);
            });
            
            // Blur event - format
            costPriceInput.on('blur', function() {
                let value = parseFloat($(this).val());
                if (!isNaN(value) && value > 0) {
                    // Format with commas
                    $(this).val(value.toLocaleString('en-US'));
                }
            });
            
            console.log('✅ Cost price input setup complete');
        }
    }
    
    // Setup both price inputs immediately
    setupPriceInput();
    setupCostPriceInput();
});

function updateCarDebugInfo() {
    // Check if price input handler is working
    const priceInput = $('#price');
    const hasPriceHandler = priceInput.length > 0;
    $('#priceHandlerStatus').text(hasPriceHandler ? '✅ متاح' : '❌ غير متاح');
    $('#priceHandlerStatus').removeClass('text-success text-danger')
                            .addClass(hasPriceHandler ? 'text-success' : 'text-danger');
    
    // Check if cost price input handler is working
    const costPriceInput = $('#cost_price');
    const hasCostPriceHandler = costPriceInput.length > 0;
    $('#costPriceHandlerStatus').text(hasCostPriceHandler ? '✅ متاح' : '❌ غير متاح');
    $('#costPriceHandlerStatus').removeClass('text-success text-danger')
                                .addClass(hasCostPriceHandler ? 'text-success' : 'text-danger');
    
    // Check Simple Price Handler
    $('#simplePriceHandlerStatus').text(window.simplePriceHandler ? '✅ متاح' : '❌ غير متاح');
    $('#simplePriceHandlerStatus').removeClass('text-success text-danger')
                                  .addClass(window.simplePriceHandler ? 'text-success' : 'text-danger');
    
    // Check jQuery
    $('#jqueryStatus').text(window.jQuery ? '✅ متاح' : '❌ غير متاح');
    $('#jqueryStatus').removeClass('text-success text-danger')
                      .addClass(window.jQuery ? 'text-success' : 'text-danger');
}

function testCarPrices() {
    const results = [];
    
    // Test price input
    const priceInput = $('#price');
    const priceValue = priceInput.val();
    const priceClean = priceValue.replace(/[^\d.]/g, '');
    const priceNumeric = parseFloat(priceClean);
    
    results.push({
        field: 'سعر البيع',
        original: priceValue,
        clean: priceClean,
        numeric: isNaN(priceNumeric) ? 'غير صحيح' : priceNumeric,
        valid: !isNaN(priceNumeric) && priceNumeric > 0
    });
    
    // Test cost price input
    const costPriceInput = $('#cost_price');
    const costPriceValue = costPriceInput.val();
    const costPriceClean = costPriceValue.replace(/[^\d.]/g, '');
    const costPriceNumeric = parseFloat(costPriceClean);
    
    results.push({
        field: 'سعر التكلفة',
        original: costPriceValue,
        clean: costPriceClean,
        numeric: isNaN(costPriceNumeric) ? 'غير صحيح' : costPriceNumeric,
        valid: costPriceValue === '' || (!isNaN(costPriceNumeric) && costPriceNumeric > 0)
    });
    
    // Display results
    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>الحقل</th><th>القيمة المدخلة</th><th>القيمة المنظفة</th><th>القيمة الرقمية</th><th>صحيح؟</th></tr></thead><tbody>';
    
    results.forEach(result => {
        const statusClass = result.valid ? 'text-success' : 'text-danger';
        const statusIcon = result.valid ? '✅' : '❌';
        
        html += `<tr>
            <td>${result.field}</td>
            <td>${result.original || '-'}</td>
            <td>${result.clean || '-'}</td>
            <td>${result.numeric}</td>
            <td class="${statusClass}">${statusIcon}</td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    
    $('#carTestResults').html(html);
}

function clearCarForm() {
    $('#carPriceTestForm')[0].reset();
    $('#carTestResults').html('<p class="text-muted">اضغط "اختبار الأسعار" لرؤية النتائج</p>');
}

function fillCarSampleData() {
    $('#price').val('75000').trigger('blur');
    $('#cost_price').val('65000').trigger('blur');
    
    setTimeout(() => {
        testCarPrices();
    }, 500);
}
</script>
{% endblock %}
