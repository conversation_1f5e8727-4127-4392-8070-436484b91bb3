{% extends "base.html" %}

{% block title %}لوحة التحكم - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">لوحة التحكم</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i>
                {{ current_date|arabic_date }}
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-number">{{ stats.cars.total }}</div>
            <div class="stats-label">إجمالي السيارات</div>
            <div class="mt-2">
                <small>متاحة: {{ stats.cars.available }} | مباعة: {{ stats.cars.sold }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="stats-number">{{ stats.sales.this_month }}</div>
            <div class="stats-label">مبيعات هذا الشهر</div>
            <div class="mt-2">
                <small>الإجمالي: {{ stats.sales.total }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="stats-number">{{ stats.revenue.monthly|currency }}</div>
            <div class="stats-label">إيرادات الشهر</div>
            <div class="mt-2">
                <small>السنوية: {{ stats.revenue.yearly|currency }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
            <div class="stats-number">{{ stats.installments.overdue }}</div>
            <div class="stats-label">أقساط متأخرة</div>
            <div class="mt-2">
                <small>المعلقة: {{ stats.installments.pending }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if current_user.has_permission('edit_cars') %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('cars.add') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus-circle mb-2"></i><br>
                            إضافة سيارة جديدة
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if current_user.has_permission('edit_customers') %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('customers.add') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-plus mb-2"></i><br>
                            إضافة عميل جديد
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if current_user.has_permission('create_sales') %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('sales.add') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-handshake mb-2"></i><br>
                            عملية بيع جديدة
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if current_user.has_permission('reports') %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-bar mb-2"></i><br>
                            عرض التقارير
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Sales -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">آخر المبيعات</h5>
                <a href="{{ url_for('sales.index') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>السيارة</th>
                                <th>النوع</th>
                                <th>السعر</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('customers.view', customer_id=sale.customer.id) }}" class="text-decoration-none">
                                        {{ sale.customer.full_name }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{{ url_for('cars.view', car_id=sale.car.id) }}" class="text-decoration-none">
                                        {{ sale.car.brand }} {{ sale.car.model }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                        {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                                    </span>
                                </td>
                                <td>{{ sale.sale_price|currency }}</td>
                                <td>{{ sale.sale_date|arabic_date }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مبيعات حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Overdue Installments -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أقساط متأخرة</h5>
                <a href="{{ url_for('sales.overdue_installments') }}" class="btn btn-sm btn-outline-danger">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if overdue_installments %}
                <div class="list-group list-group-flush">
                    {% for installment in overdue_installments %}
                    <div class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">{{ installment.sale.customer.full_name }}</div>
                            <small class="text-muted">
                                قسط رقم {{ installment.installment_number }} - 
                                {{ installment.amount|currency }}
                            </small>
                        </div>
                        <span class="badge bg-danger rounded-pill">
                            {{ (current_date - installment.due_date).days }} يوم
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted">لا توجد أقساط متأخرة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Monthly Installments -->
{% if monthly_installments %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">أقساط هذا الشهر</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>السيارة</th>
                                <th>رقم القسط</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for installment in monthly_installments %}
                            <tr>
                                <td>{{ installment.sale.customer.full_name }}</td>
                                <td>{{ installment.sale.car.brand }} {{ installment.sale.car.model }}</td>
                                <td>{{ installment.installment_number }}</td>
                                <td>{{ installment.due_date|arabic_date }}</td>
                                <td>{{ installment.amount|currency }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if installment.status == 'paid' else 'warning' if installment.status == 'partial' else 'secondary' }}">
                                        {% if installment.status == 'paid' %}مدفوع
                                        {% elif installment.status == 'partial' %}مدفوع جزئياً
                                        {% else %}معلق{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if installment.status == 'pending' %}
                                    <a href="{{ url_for('whatsapp.send_installment_reminder', installment_id=installment.id) }}" 
                                       class="btn btn-sm btn-outline-success" title="إرسال تذكير">
                                        <i class="fab fa-whatsapp"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add some animations
    $(document).ready(function() {
        $('.stats-card').each(function(index) {
            $(this).delay(index * 100).fadeIn(500);
        });
    });
</script>
{% endblock %}
