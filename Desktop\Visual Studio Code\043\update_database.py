#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لإضافة جداول أرقام السيارات
Database update script to add plate numbers tables
"""

import os
import sys
from datetime import datetime, date

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, PlateNumber, PlateNumberSale, PlateNumberBid, PlateNumberPayment, User
import json

def update_database():
    """Update database with new plate numbers tables"""
    app = create_app()
    
    with app.app_context():
        print("🔄 تحديث قاعدة البيانات...")
        
        try:
            # Create all tables
            db.create_all()
            print("✅ تم إنشاء الجداول بنجاح")
            
            # Add sample plate numbers
            add_sample_data()
            
            print("🎉 تم تحديث قاعدة البيانات بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
            return False
    
    return True

def add_sample_data():
    """Add sample plate numbers data"""
    print("📝 إضافة بيانات تجريبية...")
    
    # Get admin user
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        print("⚠️ لم يتم العثور على المستخدم الإداري")
        return
    
    # Sample plate numbers
    sample_plates = [
        {
            'number': '1',
            'category': 'vip',
            'type': 'private',
            'price': 500000,
            'cost_price': 400000,
            'description': 'رقم واحد - الأغلى والأندر في قطر',
            'code_letter': 'A',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': False,
            'is_mirror': False,
            'is_lucky': True,
            'digits_count': 1
        },
        {
            'number': '7777',
            'category': 'vip',
            'type': 'private',
            'price': 200000,
            'cost_price': 150000,
            'description': 'رقم سبعة مكرر - رقم محظوظ ومميز',
            'code_letter': 'A',
            'series': '2024',
            'is_repeated': True,
            'is_sequential': False,
            'is_mirror': False,
            'is_lucky': True,
            'digits_count': 4
        },
        {
            'number': '12321',
            'category': 'vip',
            'type': 'private',
            'price': 150000,
            'cost_price': 120000,
            'description': 'رقم مرآة مميز - يقرأ نفس الرقم من الجهتين',
            'code_letter': 'A',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': False,
            'is_mirror': True,
            'is_lucky': False,
            'digits_count': 5
        },
        {
            'number': '1234',
            'category': 'special',
            'type': 'private',
            'price': 75000,
            'cost_price': 60000,
            'description': 'أرقام متسلسلة من 1 إلى 4',
            'code_letter': 'B',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': True,
            'is_mirror': False,
            'is_lucky': False,
            'digits_count': 4
        },
        {
            'number': '5678',
            'category': 'special',
            'type': 'private',
            'price': 65000,
            'cost_price': 50000,
            'description': 'أرقام متسلسلة من 5 إلى 8',
            'code_letter': 'B',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': True,
            'is_mirror': False,
            'is_lucky': False,
            'digits_count': 4
        },
        {
            'number': '888',
            'category': 'vip',
            'type': 'private',
            'price': 180000,
            'cost_price': 140000,
            'description': 'رقم ثمانية مكرر - رقم محظوظ جداً',
            'code_letter': 'A',
            'series': '2024',
            'is_repeated': True,
            'is_sequential': False,
            'is_mirror': False,
            'is_lucky': True,
            'digits_count': 3,
            'is_auction': True,
            'starting_bid': 150000,
            'current_bid': 150000,
            'reserve_price': 170000,
            'auction_start_date': datetime.now(),
            'auction_end_date': datetime(2024, 12, 31, 23, 59, 59)
        },
        {
            'number': '2000',
            'category': 'special',
            'type': 'private',
            'price': 45000,
            'cost_price': 35000,
            'description': 'رقم ألفين - رقم مميز وسهل التذكر',
            'code_letter': 'C',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': False,
            'is_mirror': False,
            'is_lucky': False,
            'digits_count': 4
        },
        {
            'number': '123456',
            'category': 'regular',
            'type': 'private',
            'price': 15000,
            'cost_price': 10000,
            'description': 'رقم عادي - أرقام متسلسلة طويلة',
            'code_letter': 'D',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': True,
            'is_mirror': False,
            'is_lucky': False,
            'digits_count': 6
        },
        {
            'number': '9999',
            'category': 'vip',
            'type': 'private',
            'price': 220000,
            'cost_price': 180000,
            'description': 'رقم تسعة مكرر - رقم محظوظ ومميز جداً',
            'code_letter': 'A',
            'series': '2024',
            'is_repeated': True,
            'is_sequential': False,
            'is_mirror': False,
            'is_lucky': True,
            'digits_count': 4,
            'status': 'sold'
        },
        {
            'number': '1001',
            'category': 'special',
            'type': 'private',
            'price': 35000,
            'cost_price': 25000,
            'description': 'رقم مرآة بسيط - سهل التذكر',
            'code_letter': 'B',
            'series': '2024',
            'is_repeated': False,
            'is_sequential': False,
            'is_mirror': True,
            'is_lucky': False,
            'digits_count': 4
        }
    ]
    
    added_count = 0
    for plate_data in sample_plates:
        # Check if plate number already exists
        existing = PlateNumber.query.filter_by(number=plate_data['number']).first()
        if existing:
            print(f"⚠️ رقم اللوحة {plate_data['number']} موجود مسبقاً")
            continue
        
        # Create special features JSON
        special_features = {
            'is_sequential': plate_data.get('is_sequential', False),
            'is_repeated': plate_data.get('is_repeated', False),
            'is_mirror': plate_data.get('is_mirror', False),
            'is_birthday': plate_data.get('is_birthday', False),
            'is_lucky': plate_data.get('is_lucky', False)
        }
        
        # Create plate number
        plate = PlateNumber(
            number=plate_data['number'],
            category=plate_data['category'],
            type=plate_data['type'],
            digits_count=plate_data['digits_count'],
            price=plate_data['price'],
            cost_price=plate_data.get('cost_price'),
            status=plate_data.get('status', 'available'),
            description=plate_data.get('description', ''),
            code_letter=plate_data.get('code_letter'),
            series=plate_data.get('series'),
            is_sequential=plate_data.get('is_sequential', False),
            is_repeated=plate_data.get('is_repeated', False),
            is_mirror=plate_data.get('is_mirror', False),
            is_birthday=plate_data.get('is_birthday', False),
            is_lucky=plate_data.get('is_lucky', False),
            is_auction=plate_data.get('is_auction', False),
            starting_bid=plate_data.get('starting_bid'),
            current_bid=plate_data.get('current_bid'),
            reserve_price=plate_data.get('reserve_price'),
            auction_start_date=plate_data.get('auction_start_date'),
            auction_end_date=plate_data.get('auction_end_date'),
            special_features=json.dumps(special_features),
            images=json.dumps([]),
            created_by=admin.id
        )
        
        db.session.add(plate)
        added_count += 1
        print(f"✅ تم إضافة رقم اللوحة: {plate_data['number']}")
    
    try:
        db.session.commit()
        print(f"🎉 تم إضافة {added_count} رقم لوحة بنجاح!")
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ البيانات: {str(e)}")

def show_statistics():
    """Show database statistics"""
    app = create_app()
    
    with app.app_context():
        print("\n📊 إحصائيات قاعدة البيانات:")
        print("=" * 40)
        
        # Plate numbers statistics
        total_plates = PlateNumber.query.count()
        available_plates = PlateNumber.query.filter_by(status='available').count()
        sold_plates = PlateNumber.query.filter_by(status='sold').count()
        auction_plates = PlateNumber.query.filter_by(is_auction=True, status='available').count()
        
        print(f"📋 أرقام اللوحات:")
        print(f"   • إجمالي: {total_plates}")
        print(f"   • متاحة: {available_plates}")
        print(f"   • مباعة: {sold_plates}")
        print(f"   • في المزايدة: {auction_plates}")
        
        # Categories statistics
        vip_count = PlateNumber.query.filter_by(category='vip').count()
        special_count = PlateNumber.query.filter_by(category='special').count()
        regular_count = PlateNumber.query.filter_by(category='regular').count()
        
        print(f"\n🏷️ الفئات:")
        print(f"   • VIP: {vip_count}")
        print(f"   • مميزة: {special_count}")
        print(f"   • عادية: {regular_count}")
        
        # Price statistics
        if total_plates > 0:
            avg_price = db.session.query(db.func.avg(PlateNumber.price)).scalar()
            max_price = db.session.query(db.func.max(PlateNumber.price)).scalar()
            min_price = db.session.query(db.func.min(PlateNumber.price)).scalar()
            
            print(f"\n💰 الأسعار:")
            print(f"   • متوسط السعر: {avg_price:,.0f} ريال")
            print(f"   • أعلى سعر: {max_price:,.0f} ريال")
            print(f"   • أقل سعر: {min_price:,.0f} ريال")
        
        # Sales statistics
        total_sales = PlateNumberSale.query.count()
        total_bids = PlateNumberBid.query.count()
        
        print(f"\n📈 المبيعات والمزايدات:")
        print(f"   • إجمالي المبيعات: {total_sales}")
        print(f"   • إجمالي المزايدات: {total_bids}")
        
        print("=" * 40)

if __name__ == '__main__':
    print("🚀 بدء تحديث قاعدة البيانات لأرقام السيارات...")
    print("=" * 50)
    
    if update_database():
        show_statistics()
        print("\n✅ تم التحديث بنجاح! يمكنك الآن استخدام قسم أرقام السيارات.")
        print("🌐 للوصول للقسم: http://localhost:5000/plate-numbers")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات.")
        sys.exit(1)
