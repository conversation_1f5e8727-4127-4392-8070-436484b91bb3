/* EMERGENCY ICON FIX - FORCE ICONS TO SHOW */

/* Import Font Awesome from CDN */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Force all icons to display properly */
i,
.fa,
.fas,
.far,
.fab,
.fal,
.fad,
.fat {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    font-weight: 900 !important;
    display: inline-block !important;
    font-size: 16px !important;
    color: inherit !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: auto !important;
    height: auto !important;
    text-indent: 0 !important;
    letter-spacing: normal !important;
    word-spacing: normal !important;
    white-space: nowrap !important;
    text-transform: none !important;
    text-shadow: none !important;
    outline: none !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* Specific icon fixes */
.fa-home::before { content: "\f015" !important; }
.fa-user::before { content: "\f007" !important; }
.fa-car::before { content: "\f1b9" !important; }
.fa-users::before { content: "\f0c0" !important; }
.fa-file-contract::before { content: "\f56c" !important; }
.fa-chart-bar::before { content: "\f080" !important; }
.fa-whatsapp::before { content: "\f232" !important; }
.fa-bell::before { content: "\f0f3" !important; }
.fa-cog::before { content: "\f013" !important; }
.fa-settings::before { content: "\f013" !important; }
.fa-plus::before { content: "\f067" !important; }
.fa-edit::before { content: "\f044" !important; }
.fa-trash::before { content: "\f1f8" !important; }
.fa-save::before { content: "\f0c7" !important; }
.fa-search::before { content: "\f002" !important; }
.fa-filter::before { content: "\f0b0" !important; }
.fa-download::before { content: "\f019" !important; }
.fa-upload::before { content: "\f093" !important; }
.fa-print::before { content: "\f02f" !important; }
.fa-eye::before { content: "\f06e" !important; }
.fa-check::before { content: "\f00c" !important; }
.fa-times::before { content: "\f00d" !important; }
.fa-exclamation::before { content: "\f12a" !important; }
.fa-info::before { content: "\f129" !important; }
.fa-star::before { content: "\f005" !important; }
.fa-heart::before { content: "\f004" !important; }
.fa-thumbs-up::before { content: "\f164" !important; }
.fa-phone::before { content: "\f095" !important; }
.fa-envelope::before { content: "\f0e0" !important; }
.fa-calendar::before { content: "\f073" !important; }
.fa-clock::before { content: "\f017" !important; }
.fa-money-bill::before { content: "\f0d6" !important; }
.fa-dollar-sign::before { content: "\f155" !important; }
.fa-credit-card::before { content: "\f09d" !important; }
.fa-building::before { content: "\f1ad" !important; }
.fa-map-marker::before { content: "\f041" !important; }
.fa-location-dot::before { content: "\f3c5" !important; }
.fa-globe::before { content: "\f0ac" !important; }
.fa-language::before { content: "\f1ab" !important; }
.fa-flag::before { content: "\f024" !important; }
.fa-id-card::before { content: "\f2c2" !important; }
.fa-address-card::before { content: "\f2bb" !important; }
.fa-mobile::before { content: "\f3ce" !important; }
.fa-mobile-alt::before { content: "\f3cd" !important; }
.fa-tablet::before { content: "\f3fb" !important; }
.fa-laptop::before { content: "\f109" !important; }
.fa-desktop::before { content: "\f108" !important; }
.fa-wifi::before { content: "\f1eb" !important; }
.fa-signal::before { content: "\f012" !important; }
.fa-battery-full::before { content: "\f240" !important; }
.fa-power-off::before { content: "\f011" !important; }
.fa-refresh::before { content: "\f021" !important; }
.fa-sync::before { content: "\f021" !important; }
.fa-undo::before { content: "\f0e2" !important; }
.fa-redo::before { content: "\f01e" !important; }
.fa-copy::before { content: "\f0c5" !important; }
.fa-paste::before { content: "\f0ea" !important; }
.fa-cut::before { content: "\f0c4" !important; }
.fa-bold::before { content: "\f032" !important; }
.fa-italic::before { content: "\f033" !important; }
.fa-underline::before { content: "\f0cd" !important; }
.fa-align-left::before { content: "\f036" !important; }
.fa-align-center::before { content: "\f037" !important; }
.fa-align-right::before { content: "\f038" !important; }
.fa-list::before { content: "\f03a" !important; }
.fa-list-ul::before { content: "\f0ca" !important; }
.fa-list-ol::before { content: "\f0cb" !important; }
.fa-table::before { content: "\f0ce" !important; }
.fa-th::before { content: "\f00a" !important; }
.fa-th-list::before { content: "\f00b" !important; }
.fa-th-large::before { content: "\f009" !important; }
.fa-grid::before { content: "\f00a" !important; }
.fa-columns::before { content: "\f0db" !important; }
.fa-bars::before { content: "\f0c9" !important; }
.fa-menu::before { content: "\f0c9" !important; }
.fa-hamburger::before { content: "\f0c9" !important; }
.fa-ellipsis-v::before { content: "\f142" !important; }
.fa-ellipsis-h::before { content: "\f141" !important; }
.fa-more::before { content: "\f141" !important; }
.fa-arrow-up::before { content: "\f062" !important; }
.fa-arrow-down::before { content: "\f063" !important; }
.fa-arrow-left::before { content: "\f060" !important; }
.fa-arrow-right::before { content: "\f061" !important; }
.fa-chevron-up::before { content: "\f077" !important; }
.fa-chevron-down::before { content: "\f078" !important; }
.fa-chevron-left::before { content: "\f053" !important; }
.fa-chevron-right::before { content: "\f054" !important; }
.fa-angle-up::before { content: "\f106" !important; }
.fa-angle-down::before { content: "\f107" !important; }
.fa-angle-left::before { content: "\f104" !important; }
.fa-angle-right::before { content: "\f105" !important; }
.fa-caret-up::before { content: "\f0d8" !important; }
.fa-caret-down::before { content: "\f0d7" !important; }
.fa-caret-left::before { content: "\f0d9" !important; }
.fa-caret-right::before { content: "\f0da" !important; }
.fa-sort::before { content: "\f0dc" !important; }
.fa-sort-up::before { content: "\f0de" !important; }
.fa-sort-down::before { content: "\f0dd" !important; }
.fa-expand::before { content: "\f065" !important; }
.fa-compress::before { content: "\f066" !important; }
.fa-maximize::before { content: "\f31e" !important; }
.fa-minimize::before { content: "\f78c" !important; }
.fa-window-maximize::before { content: "\f2d0" !important; }
.fa-window-minimize::before { content: "\f2d1" !important; }
.fa-window-restore::before { content: "\f2d2" !important; }
.fa-window-close::before { content: "\f2d3" !important; }

/* Sidebar specific icon fixes */
.sidebar .nav-link i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 16px !important;
    width: 20px !important;
    text-align: center !important;
    margin-left: 10px !important;
    color: rgba(255,255,255,0.9) !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

.sidebar .nav-link:hover i {
    color: white !important;
}

/* Button icon fixes */
.btn i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    margin-left: 8px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Card header icon fixes */
.card-header i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 18px !important;
    margin-left: 10px !important;
    color: white !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Table icon fixes */
.table i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Form icon fixes */
.form-control i,
.input-group i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 16px !important;
    color: #6c757d !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Alert icon fixes */
.alert i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 16px !important;
    margin-left: 8px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Badge icon fixes */
.badge i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 12px !important;
    margin-left: 4px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Dropdown icon fixes */
.dropdown i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    margin-left: 6px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Pagination icon fixes */
.pagination i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Breadcrumb icon fixes */
.breadcrumb i {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    margin-left: 6px !important;
    color: inherit !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

/* Force visibility for all icons */
[class*="fa-"]::before {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fallback for missing icons */
.fa::before {
    content: "\f111" !important; /* circle */
}

/* Emergency fallback */
i:empty::before {
    content: "•" !important;
    font-family: Arial !important;
    font-weight: bold !important;
    color: inherit !important;
}

/* Force icon display */
.fa, .fas, .far, .fab {
    -webkit-font-feature-settings: normal !important;
    font-feature-settings: normal !important;
    -webkit-font-variant: normal !important;
    font-variant: normal !important;
}

/* Ensure icons are not hidden */
.fa[style*="display: none"],
.fas[style*="display: none"],
.far[style*="display: none"],
.fab[style*="display: none"] {
    display: inline-block !important;
}

/* Override any hiding styles */
.fa[hidden],
.fas[hidden],
.far[hidden],
.fab[hidden] {
    display: inline-block !important;
    visibility: visible !important;
}
