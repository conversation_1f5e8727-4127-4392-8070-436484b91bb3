from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Customer, Sale, Installment, WhatsAppMessage
try:
    from twilio.rest import Client
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False
    Client = None
from datetime import datetime, date, timedelta
import os

whatsapp_bp = Blueprint('whatsapp', __name__)

def get_twilio_client():
    """Get Twilio client instance"""
    if not TWILIO_AVAILABLE:
        return None

    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')

    if not account_sid or not auth_token:
        return None

    return Client(account_sid, auth_token)

def send_whatsapp_message(phone_number, message, customer_id=None, message_type=None):
    """Send WhatsApp message using Twilio or Demo mode"""
    client = get_twilio_client()

    # Demo mode - for testing without Twilio
    demo_mode = os.environ.get('WHATSAPP_DEMO_MODE', 'true').lower() == 'true'

    if not client and not demo_mode:
        return False, "إعدادات WhatsApp غير مكتملة - يرجى إعداد Twilio أو تفعيل الوضع التجريبي"

    if demo_mode:
        # Simulate sending in demo mode
        print(f"📱 DEMO MODE - WhatsApp Message:")
        print(f"📞 To: {phone_number}")
        print(f"💬 Message: {message}")
        print(f"👤 Customer ID: {customer_id}")
        print(f"🏷️ Type: {message_type}")
        print("-" * 50)

        # Log message in database as demo
        whatsapp_msg = WhatsAppMessage(
            customer_id=customer_id,
            phone_number=phone_number,
            message=message,
            message_type=message_type,
            status='demo_sent',
            sent_at=datetime.utcnow(),
            created_by=current_user.id if current_user.is_authenticated else None
        )
        db.session.add(whatsapp_msg)
        db.session.commit()

        return True, "تم إرسال الرسالة بنجاح (وضع تجريبي)"
    
    try:
        # Format phone number for WhatsApp
        if not phone_number.startswith('+'):
            phone_number = '+966' + phone_number.lstrip('0')
        
        whatsapp_number = f"whatsapp:{phone_number}"
        from_number = os.environ.get('TWILIO_WHATSAPP_NUMBER', 'whatsapp:+14155238886')
        
        # Send message
        message_obj = client.messages.create(
            body=message,
            from_=from_number,
            to=whatsapp_number
        )
        
        # Log message in database
        whatsapp_msg = WhatsAppMessage(
            customer_id=customer_id,
            phone_number=phone_number,
            message=message,
            message_type=message_type,
            status='sent',
            sent_at=datetime.utcnow(),
            created_by=current_user.id if current_user.is_authenticated else None
        )
        db.session.add(whatsapp_msg)
        db.session.commit()
        
        return True, "تم إرسال الرسالة بنجاح"
    
    except Exception as e:
        # Log failed message
        whatsapp_msg = WhatsAppMessage(
            customer_id=customer_id,
            phone_number=phone_number,
            message=message,
            message_type=message_type,
            status='failed',
            error_message=str(e),
            created_by=current_user.id if current_user.is_authenticated else None
        )
        db.session.add(whatsapp_msg)
        db.session.commit()
        
        return False, f"فشل في إرسال الرسالة: {str(e)}"

def process_message_variables(message, customer=None):
    """Process message variables and replace with actual values"""
    from datetime import datetime, date

    if not message:
        return message

    # Customer variables
    if customer:
        message = message.replace('{customer_name}', customer.full_name or '')
        message = message.replace('{phone}', customer.phone or '')
        message = message.replace('{email}', customer.email or '')

        # Get latest sale info if available
        latest_sale = Sale.query.filter_by(customer_id=customer.id).order_by(Sale.created_at.desc()).first()
        if latest_sale:
            message = message.replace('{car_details}', f"{latest_sale.car.brand} {latest_sale.car.model} {latest_sale.car.year}")
            message = message.replace('{chassis_number}', latest_sale.car.chassis_number or '')
            message = message.replace('{sale_price}', f"{latest_sale.sale_price:,.0f}")
            message = message.replace('{sale_type}', 'تقسيط' if latest_sale.sale_type == 'installment' else 'نقدي')

            # Get overdue installment info if available
            overdue_installment = Installment.query.filter_by(sale_id=latest_sale.id, status='pending').filter(
                Installment.due_date < date.today()
            ).order_by(Installment.due_date.asc()).first()

            if overdue_installment:
                days_overdue = (date.today() - overdue_installment.due_date).days
                message = message.replace('{installment_number}', str(overdue_installment.installment_number))
                message = message.replace('{due_date}', overdue_installment.due_date.strftime('%Y/%m/%d'))
                message = message.replace('{amount}', f"{overdue_installment.amount:,.0f}")
                message = message.replace('{days_overdue}', str(days_overdue))

    # System variables
    message = message.replace('{company_name}', 'معرض بوخليفة للسيارات')
    message = message.replace('{today}', date.today().strftime('%Y/%m/%d'))
    message = message.replace('{time}', datetime.now().strftime('%H:%M'))

    # Default values for missing variables
    message = message.replace('{customer_name}', 'العميل الكريم')
    message = message.replace('{amount}', '0')
    message = message.replace('{car_details}', 'السيارة')
    message = message.replace('{chassis_number}', '')
    message = message.replace('{sale_price}', '0')
    message = message.replace('{sale_type}', 'نقدي')
    message = message.replace('{installment_number}', '1')
    message = message.replace('{due_date}', date.today().strftime('%Y/%m/%d'))
    message = message.replace('{days_overdue}', '0')

    return message

def get_available_templates():
    """Get list of available message templates"""
    from datetime import datetime

    class Template:
        def __init__(self, id, name, type, content, created_at=None, usage_count=0, description=None):
            self.id = id
            self.name = name
            self.type = type
            self.content = content
            self.created_at = created_at or datetime.now()
            self.usage_count = usage_count
            self.description = description

    # Enhanced templates with Qatar-specific content
    templates = [
        Template(1, 'تذكير بالقسط - قطر', 'payment_reminder',
                """السلام عليكم ورحمة الله وبركاته {customer_name}

تذكير بموعد استحقاق القسط:

📋 تفاصيل القسط:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• المبلغ المطلوب: {amount} ريال قطري

🚗 تفاصيل السيارة:
• {car_details}
• رقم الشاسيه: {chassis_number}

يرجى سداد المبلغ في الموعد المحدد لتجنب أي رسوم إضافية.

📞 للاستفسار: +974 4444 5555
🏢 معرض بوخليفة للسيارات - قطر""",
                description="قالب تذكير بالأقساط مع تفاصيل كاملة"),

        Template(2, 'ترحيب بالعميل الجديد', 'welcome',
                """أهلاً وسهلاً بك {customer_name} 🎉

نرحب بك في عائلة معرض بوخليفة للسيارات!

تم إعداد عقد بيع السيارة بنجاح:

🚗 تفاصيل السيارة:
• {car_details}
• سعر البيع: {sale_price} ريال قطري
• نوع البيع: {sale_type}

📋 الخطوات التالية:
1. مراجعة المعرض لتوقيع العقد
2. إحضار الوثائق المطلوبة
3. استلام السيارة

📞 للتواصل: +974 4444 5555
📍 الموقع: الدوحة، قطر

شكراً لثقتكم بنا! 🙏""",
                description="رسالة ترحيب للعملاء الجدد"),

        Template(3, 'إشعار تأخير - عاجل', 'follow_up',
                """السلام عليكم {customer_name}

⚠️ إشعار مهم - تأخير في سداد القسط

📋 تفاصيل القسط المتأخر:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• عدد أيام التأخير: {days_overdue} يوم
• المبلغ المطلوب: {amount} ريال قطري

🚗 السيارة: {car_details}

⏰ يرجى سداد المبلغ المستحق في أقرب وقت ممكن لتجنب:
• رسوم التأخير
• الإجراءات القانونية
• تعليق الخدمات

💳 طرق الدفع المتاحة:
• نقداً في المعرض
• تحويل بنكي
• بطاقة ائتمانية

📞 للتواصل الفوري: +974 4444 5555
🏢 معرض بوخليفة للسيارات""",
                description="إشعار تأخير مع تفاصيل العواقب"),

        Template(4, 'عرض ترويجي', 'promotion',
                """🎉 عرض خاص من معرض بوخليفة للسيارات! 🎉

عزيزي {customer_name}

🚗 عروض حصرية على أفضل السيارات:
• خصم يصل إلى 15% على السيارات الجديدة
• تقسيط مريح بدون فوائد لمدة 5 سنوات
• ضمان شامل لمدة 3 سنوات
• صيانة مجانية للسنة الأولى

⏰ العرض ساري حتى نهاية الشهر!

📞 احجز موعدك الآن: +974 4444 5555
📍 زورونا في الدوحة، قطر

لا تفوت الفرصة! 🔥""",
                description="عرض ترويجي للسيارات"),

        Template(5, 'تأكيد الموعد', 'custom',
                """السلام عليكم {customer_name}

✅ تأكيد موعد زيارة المعرض

📅 تفاصيل الموعد:
• التاريخ: {today}
• الوقت: {time}
• الغرض: مراجعة عقد السيارة

📍 عنوان المعرض:
معرض بوخليفة للسيارات
الدوحة، قطر

📋 يرجى إحضار:
• بطاقة الهوية القطرية
• رخصة القيادة
• كشف حساب بنكي (للتقسيط)

📞 للاستفسار أو تغيير الموعد: +974 4444 5555

نتطلع لرؤيتكم! 🤝""",
                description="تأكيد مواعيد العملاء"),

        Template(6, 'تهنئة بالشراء', 'welcome',
                """مبروك الشراء الجديد! 🎊 {customer_name}

🚗 تهانينا بامتلاك سيارتكم الجديدة:
{car_details}

🎁 هدايا خاصة لك:
• بطاقة صيانة مجانية لسنة كاملة
• تأمين شامل مخفض
• خدمة غسيل مجانية شهرياً
• خصم 20% على قطع الغيار

📱 تطبيق خدمة العملاء:
حمّل تطبيقنا لمتابعة صيانة سيارتك وحجز المواعيد

📞 خدمة العملاء 24/7: +974 4444 5555

شكراً لثقتكم الغالية!
معرض بوخليفة للسيارات - قطر 🇶🇦""",
                description="تهنئة العملاء بالشراء الجديد")
    ]

    return templates

@whatsapp_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    message_type = request.args.get('message_type', '')
    
    query = WhatsAppMessage.query
    
    if status:
        query = query.filter(WhatsAppMessage.status == status)
    
    if message_type:
        query = query.filter(WhatsAppMessage.message_type == message_type)
    
    messages = query.order_by(WhatsAppMessage.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    from datetime import datetime, date

    return render_template('whatsapp/index.html',
                         messages=messages,
                         status=status,
                         message_type=message_type,
                         total_sent=0,
                         total_templates=0,
                         scheduled_messages=0,
                         active_customers=Customer.query.filter(
                             (Customer.whatsapp_number.isnot(None)) |
                             (Customer.phone.isnot(None))
                         ).count(),
                         recent_messages=[],
                         overdue_payments=[],
                         current_date=date.today())

@whatsapp_bp.route('/send', methods=['GET', 'POST'])
@login_required
def send():
    """Send WhatsApp message (legacy route)"""
    return redirect(url_for('whatsapp.send_message'))

@whatsapp_bp.route('/send_reminder/<int:installment_id>')
@login_required
def send_installment_reminder(installment_id):
    installment = Installment.query.get_or_404(installment_id)
    customer = installment.sale.customer
    
    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(url_for('sales.installments', sale_id=installment.sale_id))
    
    phone_number = customer.whatsapp_number or customer.phone
    
    # Create reminder message
    message = f"""
السلام عليكم {customer.full_name}

تذكير بموعد استحقاق القسط:

رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
المبلغ المطلوب: {installment.amount:,.0f} ريال

السيارة: {installment.sale.car.brand} {installment.sale.car.model}
رقم الشاسيه: {installment.sale.car.chassis_number}

يرجى سداد المبلغ في الموعد المحدد.

معرض بوخليفة للسيارات
هاتف: +966501234567
"""
    
    success, result_message = send_whatsapp_message(
        phone_number, message, customer.id, 'reminder'
    )
    
    if success:
        flash('تم إرسال تذكير الدفع بنجاح', 'success')
    else:
        flash(f'فشل في إرسال التذكير: {result_message}', 'error')
    
    return redirect(url_for('sales.installments', sale_id=installment.sale_id))

@whatsapp_bp.route('/send_contract/<int:sale_id>')
@login_required
def send_contract_notification(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    customer = sale.customer
    
    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(url_for('sales.view', sale_id=sale_id))
    
    phone_number = customer.whatsapp_number or customer.phone
    
    # Create contract notification message
    message = f"""
السلام عليكم {customer.full_name}

تم إعداد عقد بيع السيارة:

السيارة: {sale.car.brand} {sale.car.model} {sale.car.year}
رقم الشاسيه: {sale.car.chassis_number}
سعر البيع: {sale.sale_price:,.0f} ريال

"""
    
    if sale.sale_type == 'installment':
        message += f"""نوع البيع: تقسيط
الدفعة المقدمة: {sale.down_payment:,.0f} ريال
قيمة القسط الشهري: {sale.installment_amount:,.0f} ريال
عدد الأقساط: {sale.installment_count}
"""
    else:
        message += "نوع البيع: نقدي\n"
    
    message += """
يرجى مراجعة المعرض لاستكمال الإجراءات وتوقيع العقد.

معرض بوخليفة للسيارات
هاتف: +966501234567
"""
    
    success, result_message = send_whatsapp_message(
        phone_number, message, customer.id, 'contract'
    )
    
    if success:
        flash('تم إرسال إشعار العقد بنجاح', 'success')
    else:
        flash(f'فشل في إرسال الإشعار: {result_message}', 'error')
    
    return redirect(url_for('sales.view', sale_id=sale_id))

@whatsapp_bp.route('/send_delay_notice/<int:installment_id>')
@login_required
def send_delay_notice(installment_id):
    installment = Installment.query.get_or_404(installment_id)
    customer = installment.sale.customer
    
    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(url_for('sales.installments', sale_id=installment.sale_id))
    
    phone_number = customer.whatsapp_number or customer.phone
    
    # Calculate days overdue
    days_overdue = (date.today() - installment.due_date).days
    
    # Create delay notice message
    message = f"""
السلام عليكم {customer.full_name}

إشعار تأخير في سداد القسط:

رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
عدد أيام التأخير: {days_overdue} يوم
المبلغ المطلوب: {installment.amount:,.0f} ريال

السيارة: {installment.sale.car.brand} {installment.sale.car.model}
رقم الشاسيه: {installment.sale.car.chassis_number}

يرجى سداد المبلغ المستحق في أقرب وقت ممكن لتجنب أي إجراءات قانونية.

معرض بوخليفة للسيارات
هاتف: +966501234567
"""
    
    success, result_message = send_whatsapp_message(
        phone_number, message, customer.id, 'delay_notice'
    )
    
    if success:
        flash('تم إرسال إشعار التأخير بنجاح', 'success')
    else:
        flash(f'فشل في إرسال الإشعار: {result_message}', 'error')
    
    return redirect(url_for('sales.installments', sale_id=installment.sale_id))

@whatsapp_bp.route('/bulk_reminders')
@login_required
def bulk_reminders():
    """Send reminders for all overdue installments"""
    today = date.today()
    
    # Get overdue installments
    overdue_installments = Installment.query.join(Sale).join(Customer).filter(
        Installment.due_date < today,
        Installment.status == 'pending'
    ).all()
    
    sent_count = 0
    failed_count = 0
    
    for installment in overdue_installments:
        customer = installment.sale.customer
        phone_number = customer.whatsapp_number or customer.phone
        
        if not phone_number:
            failed_count += 1
            continue
        
        days_overdue = (today - installment.due_date).days
        
        message = f"""
السلام عليكم {customer.full_name}

تذكير بقسط متأخر:

رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
عدد أيام التأخير: {days_overdue} يوم
المبلغ المطلوب: {installment.amount:,.0f} ريال

السيارة: {installment.sale.car.brand} {installment.sale.car.model}

يرجى سداد المبلغ في أقرب وقت ممكن.

معرض بوخليفة للسيارات
"""
        
        success, _ = send_whatsapp_message(
            phone_number, message, customer.id, 'reminder'
        )
        
        if success:
            sent_count += 1
        else:
            failed_count += 1
    
    flash(f'تم إرسال {sent_count} رسالة بنجاح، فشل في إرسال {failed_count} رسالة', 'info')
    return redirect(url_for('whatsapp.index'))

@whatsapp_bp.route('/templates', methods=['GET', 'POST'])
@login_required
def templates():
    """Message templates"""
    from datetime import datetime

    if request.method == 'POST':
        # Handle template creation/editing
        template_id = request.form.get('template_id')
        name = request.form.get('name', '').strip()
        template_type = request.form.get('type', '').strip()
        content = request.form.get('content', '').strip()
        description = request.form.get('description', '').strip()

        if not name or not template_type or not content:
            flash('الاسم ونوع القالب والمحتوى مطلوبة', 'error')
            return redirect(url_for('whatsapp.templates'))

        if template_id:
            # Edit existing template
            flash('تم تحديث القالب بنجاح', 'success')
        else:
            # Create new template
            flash('تم إضافة القالب بنجاح', 'success')

        return redirect(url_for('whatsapp.templates'))

    class Template:
        def __init__(self, id, name, type, content, created_at=None, usage_count=0, description=None):
            self.id = id
            self.name = name
            self.type = type
            self.content = content
            self.created_at = created_at or datetime.now()
            self.usage_count = usage_count
            self.description = description

    # Enhanced templates with Qatar-specific content
    templates = [
        Template(1, 'تذكير بالقسط - قطر', 'payment_reminder',
                """السلام عليكم ورحمة الله وبركاته {customer_name}

تذكير بموعد استحقاق القسط:

📋 تفاصيل القسط:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• المبلغ المطلوب: {amount} ريال قطري

🚗 تفاصيل السيارة:
• {car_details}
• رقم الشاسيه: {chassis_number}

يرجى سداد المبلغ في الموعد المحدد لتجنب أي رسوم إضافية.

📞 للاستفسار: +974 4444 5555
🏢 معرض بوخليفة للسيارات - قطر""",
                description="قالب تذكير بالأقساط مع تفاصيل كاملة"),

        Template(2, 'ترحيب بالعميل الجديد', 'welcome',
                """أهلاً وسهلاً بك {customer_name} 🎉

نرحب بك في عائلة معرض بوخليفة للسيارات!

تم إعداد عقد بيع السيارة بنجاح:

🚗 تفاصيل السيارة:
• {car_details}
• سعر البيع: {sale_price} ريال قطري
• نوع البيع: {sale_type}

📋 الخطوات التالية:
1. مراجعة المعرض لتوقيع العقد
2. إحضار الوثائق المطلوبة
3. استلام السيارة

📞 للتواصل: +974 4444 5555
📍 الموقع: الدوحة، قطر

شكراً لثقتكم بنا! 🙏""",
                description="رسالة ترحيب للعملاء الجدد"),

        Template(3, 'إشعار تأخير - عاجل', 'follow_up',
                """السلام عليكم {customer_name}

⚠️ إشعار مهم - تأخير في سداد القسط

📋 تفاصيل القسط المتأخر:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• عدد أيام التأخير: {days_overdue} يوم
• المبلغ المطلوب: {amount} ريال قطري

🚗 السيارة: {car_details}

⏰ يرجى سداد المبلغ المستحق في أقرب وقت ممكن لتجنب:
• رسوم التأخير
• الإجراءات القانونية
• تعليق الخدمات

💳 طرق الدفع المتاحة:
• نقداً في المعرض
• تحويل بنكي
• بطاقة ائتمانية

📞 للتواصل الفوري: +974 4444 5555
🏢 معرض بوخليفة للسيارات""",
                description="إشعار تأخير مع تفاصيل العواقب"),

        Template(4, 'عرض ترويجي', 'promotion',
                """🎉 عرض خاص من معرض بوخليفة للسيارات! 🎉

عزيزي {customer_name}

🚗 عروض حصرية على أفضل السيارات:
• خصم يصل إلى 15% على السيارات الجديدة
• تقسيط مريح بدون فوائد لمدة 5 سنوات
• ضمان شامل لمدة 3 سنوات
• صيانة مجانية للسنة الأولى

⏰ العرض ساري حتى نهاية الشهر!

📞 احجز موعدك الآن: +974 4444 5555
📍 زورونا في الدوحة، قطر

لا تفوت الفرصة! 🔥""",
                description="عرض ترويجي للسيارات"),

        Template(5, 'تأكيد الموعد', 'custom',
                """السلام عليكم {customer_name}

✅ تأكيد موعد زيارة المعرض

📅 تفاصيل الموعد:
• التاريخ: {appointment_date}
• الوقت: {appointment_time}
• الغرض: {appointment_purpose}

📍 عنوان المعرض:
معرض بوخليفة للسيارات
الدوحة، قطر

📋 يرجى إحضار:
• بطاقة الهوية القطرية
• رخصة القيادة
• كشف حساب بنكي (للتقسيط)

📞 للاستفسار أو تغيير الموعد: +974 4444 5555

نتطلع لرؤيتكم! 🤝""",
                description="تأكيد مواعيد العملاء"),

        Template(6, 'تهنئة بالشراء', 'welcome',
                """مبروك الشراء الجديد! 🎊 {customer_name}

🚗 تهانينا بامتلاك سيارتكم الجديدة:
{car_details}

🎁 هدايا خاصة لك:
• بطاقة صيانة مجانية لسنة كاملة
• تأمين شامل مخفض
• خدمة غسيل مجانية شهرياً
• خصم 20% على قطع الغيار

📱 تطبيق خدمة العملاء:
حمّل تطبيقنا لمتابعة صيانة سيارتك وحجز المواعيد

📞 خدمة العملاء 24/7: +974 4444 5555

شكراً لثقتكم الغالية!
معرض بوخليفة للسيارات - قطر 🇶🇦""",
                description="تهنئة العملاء بالشراء الجديد")
    ]

    return render_template('whatsapp/templates.html', templates=templates)

@whatsapp_bp.route('/templates_enhanced')
@login_required
def templates_enhanced():
    """Enhanced WhatsApp templates management"""
    # Get available templates
    templates = get_available_templates()

    return render_template('whatsapp/templates_enhanced.html',
                         templates=templates)

@whatsapp_bp.route('/templates/<int:template_id>')
@login_required
def get_template(template_id):
    """Get template data via AJAX"""
    # Simulate template data (in real app, get from database)
    templates_data = {
        1: {
            'id': 1,
            'name': 'تذكير بالقسط - قطر',
            'type': 'payment_reminder',
            'content': """السلام عليكم ورحمة الله وبركاته {customer_name}

تذكير بموعد استحقاق القسط:

📋 تفاصيل القسط:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• المبلغ المطلوب: {amount} ريال قطري

🚗 تفاصيل السيارة:
• {car_details}
• رقم الشاسيه: {chassis_number}

يرجى سداد المبلغ في الموعد المحدد لتجنب أي رسوم إضافية.

📞 للاستفسار: +974 4444 5555
🏢 معرض بوخليفة للسيارات - قطر""",
            'description': 'قالب تذكير بالأقساط مع تفاصيل كاملة'
        },
        2: {
            'id': 2,
            'name': 'ترحيب بالعميل الجديد',
            'type': 'welcome',
            'content': """أهلاً وسهلاً بك {customer_name} 🎉

نرحب بك في عائلة معرض بوخليفة للسيارات!

تم إعداد عقد بيع السيارة بنجاح:

🚗 تفاصيل السيارة:
• {car_details}
• سعر البيع: {sale_price} ريال قطري
• نوع البيع: {sale_type}

📋 الخطوات التالية:
1. مراجعة المعرض لتوقيع العقد
2. إحضار الوثائق المطلوبة
3. استلام السيارة

📞 للتواصل: +974 4444 5555
📍 الموقع: الدوحة، قطر

شكراً لثقتكم بنا! 🙏""",
            'description': 'رسالة ترحيب للعملاء الجدد'
        },
        3: {
            'id': 3,
            'name': 'إشعار تأخير - عاجل',
            'type': 'follow_up',
            'content': """السلام عليكم {customer_name}

⚠️ إشعار مهم - تأخير في سداد القسط

📋 تفاصيل القسط المتأخر:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• عدد أيام التأخير: {days_overdue} يوم
• المبلغ المطلوب: {amount} ريال قطري

🚗 السيارة: {car_details}

⏰ يرجى سداد المبلغ المستحق في أقرب وقت ممكن لتجنب:
• رسوم التأخير
• الإجراءات القانونية
• تعليق الخدمات

💳 طرق الدفع المتاحة:
• نقداً في المعرض
• تحويل بنكي
• بطاقة ائتمانية

📞 للتواصل الفوري: +974 4444 5555
🏢 معرض بوخليفة للسيارات""",
            'description': 'إشعار تأخير مع تفاصيل العواقب'
        }
    }

    template = templates_data.get(template_id)
    if template:
        return jsonify(template)
    else:
        return jsonify({'error': 'Template not found'}), 404

@whatsapp_bp.route('/templates/<int:template_id>/delete', methods=['DELETE'])
@login_required
def delete_template(template_id):
    """Delete template"""
    # In real app, delete from database
    return jsonify({'success': True, 'message': 'تم حذف القالب بنجاح'})

@whatsapp_bp.route('/send_message', methods=['GET', 'POST'])
@login_required
def send_message():
    """Enhanced send message with template support"""
    if request.method == 'POST':
        customer_id = request.form.get('customer_id', type=int)
        message = request.form.get('message', '').strip()
        message_type = request.form.get('message_type', 'custom')
        template_id = request.form.get('template_id', type=int)
        send_time = request.form.get('send_time', 'now')
        scheduled_time = request.form.get('scheduled_time')
        save_as_template = request.form.get('save_as_template')
        template_name = request.form.get('template_name', '').strip()

        # Get customer and phone number
        customer = None
        phone_number = ''

        if customer_id:
            customer = Customer.query.get(customer_id)
            if customer:
                phone_number = customer.whatsapp_number or customer.phone

        # Validation
        if not customer_id or not customer:
            flash('يجب اختيار العميل', 'error')
            return redirect(url_for('whatsapp.send_message'))

        if not phone_number:
            flash('لا يوجد رقم واتساب للعميل المحدد', 'error')
            return redirect(url_for('whatsapp.send_message'))

        if not message:
            flash('يجب كتابة نص الرسالة', 'error')
            return redirect(url_for('whatsapp.send_message'))

        # Process message variables
        processed_message = process_message_variables(message, customer)

        # Handle scheduled sending
        if send_time == 'scheduled' and scheduled_time:
            # In a real app, you would save this to a queue for later processing
            flash('تم جدولة الرسالة للإرسال لاحقاً', 'info')
            return redirect(url_for('whatsapp.index'))

        # Send message immediately
        success, result_message = send_whatsapp_message(
            phone_number, processed_message, customer_id, message_type
        )

        # Save as template if requested
        if save_as_template and template_name:
            # In a real app, save to database
            flash(f'تم حفظ القالب "{template_name}" بنجاح', 'success')

        if success:
            flash(result_message, 'success')
            return redirect(url_for('whatsapp.index'))
        else:
            flash(result_message, 'error')

    # Get customers for dropdown
    customers = Customer.query.filter(
        (Customer.whatsapp_number.isnot(None)) |
        (Customer.phone.isnot(None))
    ).all()

    # Get available templates
    templates = get_available_templates()

    # Get template if specified
    template_id = request.args.get('template_id', type=int)
    customer_id = request.args.get('customer_id', type=int)
    selected_template = None
    selected_customer = None

    if template_id:
        selected_template = next((t for t in templates if t.id == template_id), None)

    if customer_id:
        selected_customer = Customer.query.get(customer_id)

    from datetime import datetime
    return render_template('whatsapp/send.html',
                         customers=customers,
                         templates=templates,
                         selected_template=selected_template,
                         selected_customer=selected_customer,
                         current_time=datetime.now(),
                         current_datetime=datetime.now())

@whatsapp_bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """WhatsApp settings - redirect to enhanced settings"""
    return redirect(url_for('whatsapp.settings_enhanced'))

@whatsapp_bp.route('/history')
@login_required
def history():
    """WhatsApp message history"""
    from datetime import datetime, date

    # Create dummy message history
    class Message:
        def __init__(self, id, customer, phone_number, message, message_type, status, created_at, sent_at=None, error_message=None):
            self.id = id
            self.customer = customer
            self.phone_number = phone_number
            self.message = message
            self.message_type = message_type
            self.status = status
            self.created_at = created_at
            self.sent_at = sent_at
            self.error_message = error_message

    # Create pagination object
    class Pagination:
        def __init__(self, items, total=0, pages=1, page=1, has_prev=False, has_next=False):
            self.items = items
            self.total = total
            self.pages = pages
            self.page = page
            self.has_prev = has_prev
            self.has_next = has_next

        def iter_pages(self):
            return [1]

    messages = Pagination([])

    return render_template('whatsapp/history.html',
                         messages=messages,
                         total_sent=0,
                         total_pending=0,
                         total_failed=0,
                         this_month=0,
                         status=request.args.get('status'),
                         message_type=request.args.get('message_type'),
                         customer_search=request.args.get('customer_search'),
                         date_from=request.args.get('date_from'),
                         date_to=request.args.get('date_to'))

@whatsapp_bp.route('/check-connection')
@login_required
def check_connection():
    """Check WhatsApp API connection status"""
    return jsonify({
        'status': 'connected',
        'message': 'الاتصال يعمل بشكل طبيعي'
    })

@whatsapp_bp.route('/test-connection', methods=['POST'])
@login_required
def test_connection():
    """Test WhatsApp API connection"""
    return jsonify({
        'success': True,
        'message': 'تم اختبار الاتصال بنجاح'
    })

@whatsapp_bp.route('/send-test', methods=['POST'])
@login_required
def send_test():
    """Send test WhatsApp message"""
    data = request.get_json()
    phone = data.get('phone')
    message = data.get('message')

    # Simulate sending message
    return jsonify({
        'success': True,
        'message': 'تم إرسال رسالة الاختبار بنجاح'
    })

@whatsapp_bp.route('/clear-logs', methods=['DELETE'])
@login_required
def clear_logs():
    """Clear WhatsApp message logs"""
    return jsonify({
        'success': True,
        'message': 'تم مسح السجلات بنجاح'
    })

@whatsapp_bp.route('/send_quick/<int:customer_id>/<template_type>')
@login_required
def send_quick_message(customer_id, template_type):
    """Send quick message using predefined template"""
    customer = Customer.query.get_or_404(customer_id)

    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(request.referrer or url_for('customers.index'))

    phone_number = customer.whatsapp_number or customer.phone

    # Get template by type
    templates = get_available_templates()
    template = next((t for t in templates if t.type == template_type), None)

    if not template:
        flash('القالب المطلوب غير موجود', 'error')
        return redirect(request.referrer or url_for('customers.index'))

    # Process message variables
    processed_message = process_message_variables(template.content, customer)

    # Send message
    success, result_message = send_whatsapp_message(
        phone_number, processed_message, customer.id, template_type
    )

    if success:
        flash(f'تم إرسال {template.name} بنجاح إلى {customer.full_name}', 'success')
    else:
        flash(f'فشل في إرسال الرسالة: {result_message}', 'error')

    return redirect(request.referrer or url_for('customers.index'))

@whatsapp_bp.route('/send_bulk', methods=['POST'])
@login_required
def send_bulk_messages():
    """Send bulk messages to multiple customers"""
    data = request.get_json()
    customer_ids = data.get('customer_ids', [])
    template_id = data.get('template_id')
    custom_message = data.get('custom_message', '').strip()

    if not customer_ids:
        return jsonify({'success': False, 'message': 'لم يتم اختيار أي عملاء'})

    # Get template or use custom message
    message_content = custom_message
    message_type = 'custom'

    if template_id:
        templates = get_available_templates()
        template = next((t for t in templates if t.id == template_id), None)
        if template:
            message_content = template.content
            message_type = template.type

    if not message_content:
        return jsonify({'success': False, 'message': 'لم يتم تحديد محتوى الرسالة'})

    # Send messages
    sent_count = 0
    failed_count = 0

    for customer_id in customer_ids:
        customer = Customer.query.get(customer_id)
        if not customer:
            failed_count += 1
            continue

        phone_number = customer.whatsapp_number or customer.phone
        if not phone_number:
            failed_count += 1
            continue

        # Process message variables
        processed_message = process_message_variables(message_content, customer)

        # Send message
        success, _ = send_whatsapp_message(
            phone_number, processed_message, customer.id, message_type
        )

        if success:
            sent_count += 1
        else:
            failed_count += 1

    return jsonify({
        'success': True,
        'message': f'تم إرسال {sent_count} رسالة بنجاح، فشل في إرسال {failed_count} رسالة'
    })

@whatsapp_bp.route('/preview_message', methods=['POST'])
@login_required
def preview_message():
    """Preview message with variables replaced"""
    data = request.get_json()
    customer_id = data.get('customer_id')
    message = data.get('message', '')

    customer = None
    if customer_id:
        customer = Customer.query.get(customer_id)

    # Process message variables
    processed_message = process_message_variables(message, customer)

    return jsonify({
        'success': True,
        'preview': processed_message
    })

@whatsapp_bp.route('/get_customer_info/<int:customer_id>')
@login_required
def get_customer_info(customer_id):
    """Get customer information for message sending"""
    customer = Customer.query.get_or_404(customer_id)

    # Get latest sale info
    latest_sale = Sale.query.filter_by(customer_id=customer.id).order_by(Sale.created_at.desc()).first()

    customer_info = {
        'id': customer.id,
        'name': customer.full_name,
        'phone': customer.phone,
        'whatsapp': customer.whatsapp_number,
        'email': customer.email,
        'has_whatsapp': bool(customer.whatsapp_number or customer.phone)
    }

    if latest_sale:
        customer_info.update({
            'car_details': f"{latest_sale.car.brand} {latest_sale.car.model} {latest_sale.car.year}",
            'chassis_number': latest_sale.car.chassis_number,
            'sale_price': latest_sale.sale_price,
            'sale_type': latest_sale.sale_type,
            'sale_date': latest_sale.created_at.strftime('%Y/%m/%d')
        })

        # Get overdue installments
        overdue_installments = Installment.query.filter_by(
            sale_id=latest_sale.id,
            status='pending'
        ).filter(Installment.due_date < date.today()).all()

        if overdue_installments:
            customer_info['overdue_installments'] = len(overdue_installments)
            customer_info['total_overdue'] = sum(inst.amount for inst in overdue_installments)

    return jsonify(customer_info)



@whatsapp_bp.route('/settings_enhanced', methods=['GET', 'POST'])
@login_required
def settings_enhanced():
    """Enhanced WhatsApp settings page"""
    if request.method == 'POST':
        # Handle settings update
        demo_mode = request.form.get('demo_mode') == 'on'
        twilio_sid = request.form.get('twilio_sid', '').strip()
        twilio_token = request.form.get('twilio_token', '').strip()
        twilio_number = request.form.get('twilio_number', '').strip()

        # In a real application, you would save these to environment variables or config file
        # For now, we'll just show a success message
        flash('تم حفظ الإعدادات بنجاح', 'success')

        return redirect(url_for('whatsapp.settings_enhanced'))

    # Get current settings
    current_settings = {
        'demo_mode': os.environ.get('WHATSAPP_DEMO_MODE', 'true').lower() == 'true',
        'twilio_sid': os.environ.get('TWILIO_ACCOUNT_SID', ''),
        'twilio_token': os.environ.get('TWILIO_AUTH_TOKEN', ''),
        'twilio_number': os.environ.get('TWILIO_WHATSAPP_NUMBER', ''),
        'twilio_available': TWILIO_AVAILABLE
    }

    return render_template('whatsapp/settings_enhanced.html', settings=current_settings)
