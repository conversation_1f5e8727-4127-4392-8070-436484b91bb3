from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Customer, Sale, Installment, WhatsAppMessage
try:
    from twilio.rest import Client
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False
    Client = None
from datetime import datetime, date, timedelta
import os

whatsapp_bp = Blueprint('whatsapp', __name__)

def get_twilio_client():
    """Get Twilio client instance"""
    if not TWILIO_AVAILABLE:
        return None

    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')

    if not account_sid or not auth_token:
        return None

    return Client(account_sid, auth_token)

def send_whatsapp_message(phone_number, message, customer_id=None, message_type=None):
    """Send WhatsApp message using Twilio"""
    client = get_twilio_client()
    
    if not client:
        return False, "إعدادات WhatsApp غير مكتملة"
    
    try:
        # Format phone number for WhatsApp
        if not phone_number.startswith('+'):
            phone_number = '+966' + phone_number.lstrip('0')
        
        whatsapp_number = f"whatsapp:{phone_number}"
        from_number = os.environ.get('TWILIO_WHATSAPP_NUMBER', 'whatsapp:+***********')
        
        # Send message
        message_obj = client.messages.create(
            body=message,
            from_=from_number,
            to=whatsapp_number
        )
        
        # Log message in database
        whatsapp_msg = WhatsAppMessage(
            customer_id=customer_id,
            phone_number=phone_number,
            message=message,
            message_type=message_type,
            status='sent',
            sent_at=datetime.utcnow(),
            created_by=current_user.id if current_user.is_authenticated else None
        )
        db.session.add(whatsapp_msg)
        db.session.commit()
        
        return True, "تم إرسال الرسالة بنجاح"
    
    except Exception as e:
        # Log failed message
        whatsapp_msg = WhatsAppMessage(
            customer_id=customer_id,
            phone_number=phone_number,
            message=message,
            message_type=message_type,
            status='failed',
            error_message=str(e),
            created_by=current_user.id if current_user.is_authenticated else None
        )
        db.session.add(whatsapp_msg)
        db.session.commit()
        
        return False, f"فشل في إرسال الرسالة: {str(e)}"

@whatsapp_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    message_type = request.args.get('message_type', '')
    
    query = WhatsAppMessage.query
    
    if status:
        query = query.filter(WhatsAppMessage.status == status)
    
    if message_type:
        query = query.filter(WhatsAppMessage.message_type == message_type)
    
    messages = query.order_by(WhatsAppMessage.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('whatsapp/index.html', 
                         messages=messages, 
                         status=status,
                         message_type=message_type)

@whatsapp_bp.route('/send', methods=['GET', 'POST'])
@login_required
def send_message():
    if request.method == 'POST':
        customer_id = request.form.get('customer_id', type=int)
        phone_number = request.form.get('phone_number', '').strip()
        message = request.form.get('message', '').strip()
        message_type = request.form.get('message_type', 'custom')
        
        # Validation
        if not phone_number or not message:
            flash('رقم الهاتف والرسالة مطلوبان', 'error')
            return render_template('whatsapp/send.html')
        
        # Send message
        success, result_message = send_whatsapp_message(
            phone_number, message, customer_id, message_type
        )
        
        if success:
            flash(result_message, 'success')
            return redirect(url_for('whatsapp.index'))
        else:
            flash(result_message, 'error')
    
    # Get customers for dropdown
    customers = Customer.query.all()
    
    return render_template('whatsapp/send.html', customers=customers)

@whatsapp_bp.route('/send_reminder/<int:installment_id>')
@login_required
def send_installment_reminder(installment_id):
    installment = Installment.query.get_or_404(installment_id)
    customer = installment.sale.customer
    
    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(url_for('sales.installments', sale_id=installment.sale_id))
    
    phone_number = customer.whatsapp_number or customer.phone
    
    # Create reminder message
    message = f"""
السلام عليكم {customer.full_name}

تذكير بموعد استحقاق القسط:

رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
المبلغ المطلوب: {installment.amount:,.0f} ريال

السيارة: {installment.sale.car.brand} {installment.sale.car.model}
رقم الشاسيه: {installment.sale.car.chassis_number}

يرجى سداد المبلغ في الموعد المحدد.

معرض بوخليفة للسيارات
هاتف: +966501234567
"""
    
    success, result_message = send_whatsapp_message(
        phone_number, message, customer.id, 'reminder'
    )
    
    if success:
        flash('تم إرسال تذكير الدفع بنجاح', 'success')
    else:
        flash(f'فشل في إرسال التذكير: {result_message}', 'error')
    
    return redirect(url_for('sales.installments', sale_id=installment.sale_id))

@whatsapp_bp.route('/send_contract/<int:sale_id>')
@login_required
def send_contract_notification(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    customer = sale.customer
    
    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(url_for('sales.view', sale_id=sale_id))
    
    phone_number = customer.whatsapp_number or customer.phone
    
    # Create contract notification message
    message = f"""
السلام عليكم {customer.full_name}

تم إعداد عقد بيع السيارة:

السيارة: {sale.car.brand} {sale.car.model} {sale.car.year}
رقم الشاسيه: {sale.car.chassis_number}
سعر البيع: {sale.sale_price:,.0f} ريال

"""
    
    if sale.sale_type == 'installment':
        message += f"""نوع البيع: تقسيط
الدفعة المقدمة: {sale.down_payment:,.0f} ريال
قيمة القسط الشهري: {sale.installment_amount:,.0f} ريال
عدد الأقساط: {sale.installment_count}
"""
    else:
        message += "نوع البيع: نقدي\n"
    
    message += """
يرجى مراجعة المعرض لاستكمال الإجراءات وتوقيع العقد.

معرض بوخليفة للسيارات
هاتف: +966501234567
"""
    
    success, result_message = send_whatsapp_message(
        phone_number, message, customer.id, 'contract'
    )
    
    if success:
        flash('تم إرسال إشعار العقد بنجاح', 'success')
    else:
        flash(f'فشل في إرسال الإشعار: {result_message}', 'error')
    
    return redirect(url_for('sales.view', sale_id=sale_id))

@whatsapp_bp.route('/send_delay_notice/<int:installment_id>')
@login_required
def send_delay_notice(installment_id):
    installment = Installment.query.get_or_404(installment_id)
    customer = installment.sale.customer
    
    if not customer.whatsapp_number and not customer.phone:
        flash('لا يوجد رقم واتساب للعميل', 'error')
        return redirect(url_for('sales.installments', sale_id=installment.sale_id))
    
    phone_number = customer.whatsapp_number or customer.phone
    
    # Calculate days overdue
    days_overdue = (date.today() - installment.due_date).days
    
    # Create delay notice message
    message = f"""
السلام عليكم {customer.full_name}

إشعار تأخير في سداد القسط:

رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
عدد أيام التأخير: {days_overdue} يوم
المبلغ المطلوب: {installment.amount:,.0f} ريال

السيارة: {installment.sale.car.brand} {installment.sale.car.model}
رقم الشاسيه: {installment.sale.car.chassis_number}

يرجى سداد المبلغ المستحق في أقرب وقت ممكن لتجنب أي إجراءات قانونية.

معرض بوخليفة للسيارات
هاتف: +966501234567
"""
    
    success, result_message = send_whatsapp_message(
        phone_number, message, customer.id, 'delay_notice'
    )
    
    if success:
        flash('تم إرسال إشعار التأخير بنجاح', 'success')
    else:
        flash(f'فشل في إرسال الإشعار: {result_message}', 'error')
    
    return redirect(url_for('sales.installments', sale_id=installment.sale_id))

@whatsapp_bp.route('/bulk_reminders')
@login_required
def bulk_reminders():
    """Send reminders for all overdue installments"""
    today = date.today()
    
    # Get overdue installments
    overdue_installments = Installment.query.join(Sale).join(Customer).filter(
        Installment.due_date < today,
        Installment.status == 'pending'
    ).all()
    
    sent_count = 0
    failed_count = 0
    
    for installment in overdue_installments:
        customer = installment.sale.customer
        phone_number = customer.whatsapp_number or customer.phone
        
        if not phone_number:
            failed_count += 1
            continue
        
        days_overdue = (today - installment.due_date).days
        
        message = f"""
السلام عليكم {customer.full_name}

تذكير بقسط متأخر:

رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
عدد أيام التأخير: {days_overdue} يوم
المبلغ المطلوب: {installment.amount:,.0f} ريال

السيارة: {installment.sale.car.brand} {installment.sale.car.model}

يرجى سداد المبلغ في أقرب وقت ممكن.

معرض بوخليفة للسيارات
"""
        
        success, _ = send_whatsapp_message(
            phone_number, message, customer.id, 'reminder'
        )
        
        if success:
            sent_count += 1
        else:
            failed_count += 1
    
    flash(f'تم إرسال {sent_count} رسالة بنجاح، فشل في إرسال {failed_count} رسالة', 'info')
    return redirect(url_for('whatsapp.index'))

@whatsapp_bp.route('/templates')
@login_required
def templates():
    """Message templates"""
    templates = [
        {
            'name': 'تذكير بالقسط',
            'type': 'reminder',
            'content': """السلام عليكم {customer_name}

تذكير بموعد استحقاق القسط:
رقم القسط: {installment_number}
تاريخ الاستحقاق: {due_date}
المبلغ المطلوب: {amount} ريال

معرض بوخليفة للسيارات"""
        },
        {
            'name': 'إشعار العقد',
            'type': 'contract',
            'content': """السلام عليكم {customer_name}

تم إعداد عقد بيع السيارة:
السيارة: {car_details}
سعر البيع: {sale_price} ريال

يرجى مراجعة المعرض لاستكمال الإجراءات.

معرض بوخليفة للسيارات"""
        },
        {
            'name': 'إشعار تأخير',
            'type': 'delay_notice',
            'content': """السلام عليكم {customer_name}

إشعار تأخير في سداد القسط:
رقم القسط: {installment_number}
عدد أيام التأخير: {days_overdue} يوم
المبلغ المطلوب: {amount} ريال

يرجى سداد المبلغ في أقرب وقت ممكن.

معرض بوخليفة للسيارات"""
        }
    ]
    
    return render_template('whatsapp/templates.html', templates=templates)

@whatsapp_bp.route('/settings')
@login_required
def settings():
    """WhatsApp settings"""
    if not current_user.has_permission('settings'):
        flash('ليس لديك صلاحية للوصول إلى الإعدادات', 'error')
        return redirect(url_for('whatsapp.index'))
    
    # Check if Twilio credentials are configured
    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
    whatsapp_number = os.environ.get('TWILIO_WHATSAPP_NUMBER')
    
    is_configured = bool(account_sid and auth_token and whatsapp_number)
    
    return render_template('whatsapp/settings.html', 
                         is_configured=is_configured,
                         account_sid=account_sid[:10] + '...' if account_sid else None,
                         whatsapp_number=whatsapp_number)
