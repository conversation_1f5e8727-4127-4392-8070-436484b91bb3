{% extends "base.html" %}

{% block title %}معاينة العقد - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">معاينة العقد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('contracts.generate_pdf', sale_id=sale.id) }}" class="btn btn-primary">
                <i class="fas fa-file-pdf me-2"></i>
                تحميل PDF
            </a>
            <a href="{{ url_for('contracts.generate_word', sale_id=sale.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-file-word me-2"></i>
                تحميل Word
            </a>
        </div>
        <button onclick="window.print()" class="btn btn-outline-secondary">
            <i class="fas fa-print me-2"></i>
            طباعة
        </button>
    </div>
</div>

<!-- Contract Preview -->
<div class="card" id="contract-content">
    <div class="card-body p-5">
        <!-- Header -->
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">عقد بيع سيارة</h1>
            <h3 class="text-primary">{{ company_name }}</h3>
            <p class="text-muted">{{ config.COMPANY_ADDRESS }}</p>
            <p class="text-muted">هاتف: {{ config.COMPANY_PHONE }}</p>
        </div>

        <hr class="my-4">

        <!-- Contract Details -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h5>الطرف الأول (البائع):</h5>
                <p class="mb-1"><strong>{{ company_name_en }}</strong></p>
                <p class="mb-1">{{ config.COMPANY_ADDRESS }}</p>
                <p class="mb-1">هاتف: {{ config.COMPANY_PHONE }}</p>
                <p class="mb-1">البريد الإلكتروني: {{ config.COMPANY_EMAIL }}</p>
            </div>
            <div class="col-md-6">
                <h5>الطرف الثاني (المشتري):</h5>
                <p class="mb-1"><strong>{{ sale.customer.full_name }}</strong></p>
                <p class="mb-1">رقم الهوية: {{ sale.customer.national_id }}</p>
                <p class="mb-1">رقم الهاتف: {{ sale.customer.phone }}</p>
                {% if sale.customer.email %}
                <p class="mb-1">البريد الإلكتروني: {{ sale.customer.email }}</p>
                {% endif %}
                {% if sale.customer.address %}
                <p class="mb-1">العنوان: {{ sale.customer.address }}</p>
                {% endif %}
            </div>
        </div>

        <hr class="my-4">

        <!-- Vehicle Details -->
        <div class="mb-4">
            <h5>تفاصيل السيارة:</h5>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الماركة:</strong></td>
                            <td>{{ sale.car.brand }}</td>
                        </tr>
                        <tr>
                            <td><strong>الموديل:</strong></td>
                            <td>{{ sale.car.model }}</td>
                        </tr>
                        <tr>
                            <td><strong>سنة الصنع:</strong></td>
                            <td>{{ sale.car.year }}</td>
                        </tr>
                        <tr>
                            <td><strong>رقم الشاسيه:</strong></td>
                            <td>{{ sale.car.chassis_number }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        {% if sale.car.plate_number %}
                        <tr>
                            <td><strong>رقم اللوحة:</strong></td>
                            <td>{{ sale.car.plate_number }}</td>
                        </tr>
                        {% endif %}
                        {% if sale.car.color %}
                        <tr>
                            <td><strong>اللون:</strong></td>
                            <td>{{ sale.car.color }}</td>
                        </tr>
                        {% endif %}
                        {% if sale.car.engine_size %}
                        <tr>
                            <td><strong>حجم المحرك:</strong></td>
                            <td>{{ sale.car.engine_size }}</td>
                        </tr>
                        {% endif %}
                        {% if sale.car.mileage %}
                        <tr>
                            <td><strong>المسافة المقطوعة:</strong></td>
                            <td>{{ sale.car.mileage|number_format }} كم</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <!-- Sale Details -->
        <div class="mb-4">
            <h5>تفاصيل البيع:</h5>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>سعر البيع:</strong></td>
                            <td class="text-success"><strong>{{ sale.sale_price|currency }}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>نوع البيع:</strong></td>
                            <td>
                                <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                    {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ البيع:</strong></td>
                            <td>{{ sale.sale_date|arabic_date }}</td>
                        </tr>
                    </table>
                </div>
                {% if sale.sale_type == 'installment' %}
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الدفعة المقدمة:</strong></td>
                            <td>{{ sale.down_payment|currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>قيمة القسط الشهري:</strong></td>
                            <td>{{ sale.installment_amount|currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>عدد الأقساط:</strong></td>
                            <td>{{ sale.installment_count }} قسط</td>
                        </tr>
                        {% if sale.first_installment_date %}
                        <tr>
                            <td><strong>تاريخ أول قسط:</strong></td>
                            <td>{{ sale.first_installment_date|arabic_date }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
                {% endif %}
            </div>
        </div>

        {% if sale.customer.guarantor_name %}
        <hr class="my-4">

        <!-- Guarantor Details -->
        <div class="mb-4">
            <h5>بيانات الضامن:</h5>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td>{{ sale.customer.guarantor_name }}</td>
                        </tr>
                        {% if sale.customer.guarantor_id %}
                        <tr>
                            <td><strong>رقم الهوية:</strong></td>
                            <td>{{ sale.customer.guarantor_id }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        {% if sale.customer.guarantor_phone %}
                        <tr>
                            <td><strong>رقم الهاتف:</strong></td>
                            <td>{{ sale.customer.guarantor_phone }}</td>
                        </tr>
                        {% endif %}
                        {% if sale.customer.guarantor_address %}
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>{{ sale.customer.guarantor_address }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <hr class="my-4">

        <!-- Terms and Conditions -->
        <div class="mb-4">
            <h5>الشروط والأحكام:</h5>
            <ol class="list-group list-group-numbered">
                <li class="list-group-item border-0">يتعهد الطرف الثاني بدفع المبلغ المتفق عليه في المواعيد المحددة.</li>
                <li class="list-group-item border-0">في حالة التأخير عن السداد، يحق للطرف الأول اتخاذ الإجراءات القانونية اللازمة.</li>
                <li class="list-group-item border-0">يتم تسليم السيارة بعد استكمال جميع الإجراءات القانونية.</li>
                <li class="list-group-item border-0">هذا العقد ملزم للطرفين ولا يمكن تعديله إلا بموافقة الطرفين.</li>
                <li class="list-group-item border-0">يخضع هذا العقد لأحكام القانون السعودي.</li>
            </ol>
        </div>

        <hr class="my-4">

        <!-- Signatures -->
        <div class="row mt-5">
            <div class="col-md-6 text-center">
                <h6>توقيع الطرف الأول</h6>
                <div class="border-bottom border-2 border-dark mx-5 mb-2" style="height: 60px;"></div>
                <p class="mb-0"><strong>{{ company_name }}</strong></p>
                <p class="text-muted">التاريخ: _______________</p>
            </div>
            <div class="col-md-6 text-center">
                <h6>توقيع الطرف الثاني</h6>
                <div class="border-bottom border-2 border-dark mx-5 mb-2" style="height: 60px;"></div>
                <p class="mb-0"><strong>{{ sale.customer.full_name }}</strong></p>
                <p class="text-muted">التاريخ: _______________</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-5 pt-4 border-top">
            <small class="text-muted">
                تم إنشاء هذا العقد بواسطة نظام إدارة معرض السيارات - {{ current_datetime.strftime('%Y/%m/%d %H:%M') }}
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn-toolbar, .border-bottom:first-child {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    body {
        font-size: 12pt;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    .page-break {
        page-break-before: always;
    }
}
</style>
{% endblock %}
