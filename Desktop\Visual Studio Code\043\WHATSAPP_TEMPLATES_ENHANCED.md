# نظام قوالب الواتساب المحسن

## 🎉 التحسينات الجديدة

تم تطوير نظام قوالب الواتساب بميزات متقدمة لتحسين التواصل مع العملاء في معرض بوخليفة للسيارات.

## ✨ الميزات الجديدة

### 1. قوالب محسنة ومتنوعة
- **تذكيرات الدفع**: قوالب مخصصة للتذكير بالأقساط المستحقة
- **رسائل الترحيب**: قوالب للترحيب بالعملاء الجدد
- **العروض الترويجية**: قوالب للحملات التسويقية
- **متابعة العملاء**: قوالب لتأكيد المواعيد والمتابعة

### 2. نظام المتغيرات الذكي
يدعم النظام المتغيرات التالية:

#### معلومات العميل:
- `{customer_name}` - اسم العميل
- `{phone}` - رقم الهاتف
- `{email}` - البريد الإلكتروني

#### معلومات السيارة:
- `{car_details}` - تفاصيل السيارة (الماركة والموديل والسنة)
- `{chassis_number}` - رقم الشاسيه
- `{sale_price}` - سعر البيع
- `{sale_type}` - نوع البيع (نقدي/تقسيط)

#### معلومات الأقساط:
- `{installment_number}` - رقم القسط
- `{due_date}` - تاريخ الاستحقاق
- `{amount}` - مبلغ القسط
- `{days_overdue}` - عدد أيام التأخير

#### معلومات النظام:
- `{company_name}` - اسم الشركة
- `{today}` - تاريخ اليوم
- `{time}` - الوقت الحالي

### 3. واجهة مستخدم محسنة

#### صفحة القوالب المحسنة (`/whatsapp/templates_enhanced`)
- تصميم بطاقات تفاعلية لكل قالب
- معاينة فورية للقوالب
- تصنيف القوالب حسب النوع
- إحصائيات الاستخدام لكل قالب

#### صفحة إرسال الرسائل المحسنة
- معاينة مباشرة للرسالة مع المتغيرات المستبدلة
- أزرار سريعة لإدراج المتغيرات
- خيارات جدولة الإرسال
- حفظ الرسائل كقوالب جديدة

### 4. إرسال سريع من صفحة العملاء
تم إضافة قائمة منسدلة في صفحة العملاء تحتوي على:
- رسالة مخصصة
- رسالة ترحيب سريعة
- تذكير بالدفع
- رسالة متابعة
- فتح واتساب مباشرة

### 5. Routes جديدة

#### `/whatsapp/send_quick/<customer_id>/<template_type>`
إرسال سريع باستخدام قالب محدد

#### `/whatsapp/send_bulk` (POST)
إرسال رسائل جماعية لعدة عملاء

#### `/whatsapp/preview_message` (POST)
معاينة الرسالة مع المتغيرات المستبدلة

#### `/whatsapp/get_customer_info/<customer_id>`
الحصول على معلومات العميل لاستخدامها في القوالب

## 🚀 كيفية الاستخدام

### 1. الوصول للقوالب المحسنة
```
http://localhost:5000/whatsapp/templates_enhanced
```

### 2. إرسال رسالة مخصصة
```
http://localhost:5000/whatsapp/send_message
```

### 3. إرسال سريع من صفحة العملاء
- اذهب إلى صفحة العملاء
- اضغط على زر الواتساب بجانب اسم العميل
- اختر نوع الرسالة المطلوبة

## 📱 القوالب المتاحة

### 1. تذكير بالقسط - قطر
قالب شامل للتذكير بالأقساط المستحقة مع تفاصيل كاملة

### 2. ترحيب بالعميل الجديد
رسالة ترحيب للعملاء الجدد مع تفاصيل السيارة والخطوات التالية

### 3. إشعار تأخير - عاجل
إشعار للأقساط المتأخرة مع تفاصيل العواقب وطرق الدفع

### 4. عرض ترويجي
قالب للعروض والحملات التسويقية

### 5. تأكيد الموعد
تأكيد مواعيد زيارة المعرض

### 6. تهنئة بالشراء
تهنئة العملاء بالشراء الجديد مع هدايا خاصة

## 🎨 التصميم والواجهة

### ألوان القوالب:
- **تذكيرات الدفع**: أزرق (Primary)
- **رسائل الترحيب**: أخضر (Success)
- **إشعارات التأخير**: أحمر (Danger)
- **العروض الترويجية**: أزرق فاتح (Info)
- **المتابعة**: رمادي (Secondary)

### تأثيرات تفاعلية:
- تأثير hover على البطاقات
- أيقونات واتساب متحركة
- معاينة فورية للرسائل
- عدادات الأحرف المباشرة

## 🔧 الملفات المضافة/المحدثة

### ملفات جديدة:
- `templates/whatsapp/templates_enhanced.html` - صفحة القوالب المحسنة
- `test_whatsapp.py` - اختبار وظائف الواتساب
- `run_server.py` - تشغيل الخادم المبسط

### ملفات محدثة:
- `whatsapp.py` - إضافة وظائف جديدة
- `templates/whatsapp/send.html` - تحسين صفحة الإرسال
- `templates/customers/index.html` - إضافة أزرار الواتساب السريع

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملف `test_whatsapp.py` لاختبار النظام
- راجع سجلات الخادم للأخطاء
- تأكد من وجود جميع المتطلبات في `requirements.txt`

## 🎯 الخطوات التالية

1. **اختبار النظام**: تشغيل `test_whatsapp.py`
2. **تخصيص القوالب**: إضافة قوالب جديدة حسب الحاجة
3. **ربط واتساب**: إعداد Twilio للإرسال الفعلي
4. **تدريب المستخدمين**: شرح الميزات الجديدة للفريق

---

**تم تطوير هذا النظام خصيصاً لمعرض بوخليفة للسيارات - قطر** 🇶🇦
