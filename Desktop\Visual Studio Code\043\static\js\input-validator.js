/**
 * Input Validator for Qatar Car Dealership System
 * Handles Arabic/English number input and validation
 */

class InputValidator {
    constructor() {
        this.arabicDigits = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        };
        
        this.englishDigits = {
            '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
            '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
        };
        
        this.init();
    }
    
    init() {
        this.setupNumberInputs();
        this.setupCurrencyInputs();
        this.setupPhoneInputs();
        this.setupIdInputs();
        this.setupFormValidation();
        
        console.log('✅ Input Validator initialized');
    }
    
    /**
     * Convert Arabic digits to English
     */
    arabicToEnglish(text) {
        if (!text) return '';
        
        let result = String(text);
        for (let arabic in this.arabicDigits) {
            result = result.replace(new RegExp(arabic, 'g'), this.arabicDigits[arabic]);
        }
        return result;
    }
    
    /**
     * Convert English digits to Arabic
     */
    englishToArabic(text) {
        if (!text) return '';
        
        let result = String(text);
        for (let english in this.englishDigits) {
            result = result.replace(new RegExp(english, 'g'), this.englishDigits[english]);
        }
        return result;
    }
    
    /**
     * Clean and validate number input
     */
    cleanNumber(value, options = {}) {
        const {
            allowDecimals = true,
            allowNegative = false,
            maxDecimals = 2
        } = options;
        
        if (!value) return '';
        
        // Convert Arabic digits to English
        let cleaned = this.arabicToEnglish(String(value));
        
        // Remove all non-numeric characters except decimal point and minus
        if (allowDecimals && allowNegative) {
            cleaned = cleaned.replace(/[^\d.-]/g, '');
        } else if (allowDecimals) {
            cleaned = cleaned.replace(/[^\d.]/g, '');
        } else if (allowNegative) {
            cleaned = cleaned.replace(/[^\d-]/g, '');
        } else {
            cleaned = cleaned.replace(/[^\d]/g, '');
        }
        
        // Handle decimal places
        if (allowDecimals && cleaned.includes('.')) {
            const parts = cleaned.split('.');
            if (parts.length > 2) {
                // Keep only first decimal point
                cleaned = parts[0] + '.' + parts.slice(1).join('');
            }
            
            // Limit decimal places
            if (parts[1] && parts[1].length > maxDecimals) {
                cleaned = parts[0] + '.' + parts[1].substring(0, maxDecimals);
            }
        }
        
        // Handle negative sign
        if (allowNegative && cleaned.includes('-')) {
            // Keep only first minus sign at the beginning
            const minusCount = (cleaned.match(/-/g) || []).length;
            if (minusCount > 1 || (cleaned.indexOf('-') > 0)) {
                cleaned = cleaned.replace(/-/g, '');
                if (value.startsWith('-')) {
                    cleaned = '-' + cleaned;
                }
            }
        }
        
        return cleaned;
    }
    
    /**
     * Setup number inputs
     */
    setupNumberInputs() {
        // Handle all number inputs
        document.querySelectorAll('input[type="number"]').forEach(input => {
            this.setupNumberInput(input);
        });
        
        // Handle inputs with number class
        document.querySelectorAll('input.number-input').forEach(input => {
            this.setupNumberInput(input);
        });
    }
    
    /**
     * Setup individual number input
     */
    setupNumberInput(input) {
        const allowDecimals = input.step && input.step !== '1';
        const allowNegative = !input.hasAttribute('min') || parseFloat(input.min) < 0;
        
        // Input event - clean as user types
        input.addEventListener('input', (e) => {
            const cursorPos = e.target.selectionStart;
            const originalValue = e.target.value;
            
            const cleaned = this.cleanNumber(originalValue, {
                allowDecimals,
                allowNegative
            });
            
            if (cleaned !== originalValue) {
                e.target.value = cleaned;
                
                // Restore cursor position
                const newCursorPos = Math.min(cursorPos, cleaned.length);
                e.target.setSelectionRange(newCursorPos, newCursorPos);
            }
            
            // Remove any validation errors
            this.clearFieldError(input);
        });
        
        // Paste event - handle pasted content
        input.addEventListener('paste', (e) => {
            e.preventDefault();
            
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');
            const cleaned = this.cleanNumber(pastedText, {
                allowDecimals,
                allowNegative
            });
            
            input.value = cleaned;
            input.dispatchEvent(new Event('input'));
        });
        
        // Blur event - final validation
        input.addEventListener('blur', (e) => {
            this.validateNumberInput(input);
        });
        
        // Focus event - clear errors
        input.addEventListener('focus', (e) => {
            this.clearFieldError(input);
        });
    }
    
    /**
     * Setup currency inputs
     */
    setupCurrencyInputs() {
        document.querySelectorAll('.currency-input, input[data-format="currency"]').forEach(input => {
            this.setupCurrencyInput(input);
        });
    }
    
    /**
     * Setup individual currency input
     */
    setupCurrencyInput(input) {
        // Set input type to text to handle formatting
        input.type = 'text';
        
        input.addEventListener('input', (e) => {
            const cleaned = this.cleanNumber(e.target.value, {
                allowDecimals: true,
                allowNegative: false
            });
            
            e.target.value = cleaned;
            this.clearFieldError(input);
        });
        
        input.addEventListener('blur', (e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value) && value > 0) {
                // Format with commas for display
                e.target.value = this.formatCurrency(value);
            }
            
            this.validateCurrencyInput(input);
        });
        
        input.addEventListener('focus', (e) => {
            // Remove formatting for editing
            const value = e.target.value.replace(/[^\d.]/g, '');
            e.target.value = value;
            this.clearFieldError(input);
        });
    }
    
    /**
     * Setup phone inputs
     */
    setupPhoneInputs() {
        document.querySelectorAll('input[data-format="phone"], .phone-input').forEach(input => {
            this.setupPhoneInput(input);
        });
    }
    
    /**
     * Setup individual phone input
     */
    setupPhoneInput(input) {
        input.addEventListener('input', (e) => {
            let cleaned = this.arabicToEnglish(e.target.value);
            cleaned = cleaned.replace(/\D/g, ''); // Only digits
            
            // Limit to 8 digits for Qatar
            if (cleaned.length > 8) {
                cleaned = cleaned.substring(0, 8);
            }
            
            e.target.value = cleaned;
            this.clearFieldError(input);
        });
        
        input.addEventListener('blur', (e) => {
            this.validatePhoneInput(input);
        });
    }
    
    /**
     * Setup ID inputs
     */
    setupIdInputs() {
        document.querySelectorAll('input[data-format="qatar-id"], .id-input').forEach(input => {
            this.setupIdInput(input);
        });
    }
    
    /**
     * Setup individual ID input
     */
    setupIdInput(input) {
        input.addEventListener('input', (e) => {
            let cleaned = this.arabicToEnglish(e.target.value);
            cleaned = cleaned.replace(/\D/g, ''); // Only digits
            
            // Limit to 11 digits for Qatar ID
            if (cleaned.length > 11) {
                cleaned = cleaned.substring(0, 11);
            }
            
            e.target.value = cleaned;
            this.clearFieldError(input);
        });
        
        input.addEventListener('blur', (e) => {
            this.validateIdInput(input);
        });
    }
    
    /**
     * Setup form validation
     */
    setupFormValidation() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        });
    }
    
    /**
     * Validate number input
     */
    validateNumberInput(input) {
        const value = parseFloat(input.value);
        const min = input.min ? parseFloat(input.min) : null;
        const max = input.max ? parseFloat(input.max) : null;
        
        if (input.required && (!input.value || isNaN(value))) {
            this.showFieldError(input, 'هذا الحقل مطلوب');
            return false;
        }
        
        if (!isNaN(value)) {
            if (min !== null && value < min) {
                this.showFieldError(input, `القيمة يجب أن تكون ${min} أو أكثر`);
                return false;
            }
            
            if (max !== null && value > max) {
                this.showFieldError(input, `القيمة يجب أن تكون ${max} أو أقل`);
                return false;
            }
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    /**
     * Validate currency input
     */
    validateCurrencyInput(input) {
        const value = parseFloat(input.value.replace(/[^\d.]/g, ''));
        
        if (input.required && (!input.value || isNaN(value) || value <= 0)) {
            this.showFieldError(input, 'يجب إدخال مبلغ صحيح أكبر من صفر');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    /**
     * Validate phone input
     */
    validatePhoneInput(input) {
        const value = input.value;
        
        if (input.required && !value) {
            this.showFieldError(input, 'رقم الهاتف مطلوب');
            return false;
        }
        
        if (value && (value.length !== 8 || !/^[3-7]/.test(value))) {
            this.showFieldError(input, 'رقم الهاتف يجب أن يكون 8 أرقام ويبدأ بـ 3-7');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    /**
     * Validate ID input
     */
    validateIdInput(input) {
        const value = input.value;
        
        if (input.required && !value) {
            this.showFieldError(input, 'رقم الهوية مطلوب');
            return false;
        }
        
        if (value && value.length !== 11) {
            this.showFieldError(input, 'رقم الهوية يجب أن يكون 11 رقم بالضبط');
            return false;
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    /**
     * Validate entire form
     */
    validateForm(form) {
        let isValid = true;
        
        // Validate all number inputs
        form.querySelectorAll('input[type="number"], .number-input').forEach(input => {
            if (!this.validateNumberInput(input)) {
                isValid = false;
            }
        });
        
        // Validate currency inputs
        form.querySelectorAll('.currency-input, input[data-format="currency"]').forEach(input => {
            if (!this.validateCurrencyInput(input)) {
                isValid = false;
            }
        });
        
        // Validate phone inputs
        form.querySelectorAll('.phone-input, input[data-format="phone"]').forEach(input => {
            if (!this.validatePhoneInput(input)) {
                isValid = false;
            }
        });
        
        // Validate ID inputs
        form.querySelectorAll('.id-input, input[data-format="qatar-id"]').forEach(input => {
            if (!this.validateIdInput(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    /**
     * Format currency for display
     */
    formatCurrency(amount) {
        if (!amount || isNaN(amount)) return '';
        
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    /**
     * Show field error
     */
    showFieldError(input, message) {
        // Remove existing error
        this.clearFieldError(input);
        
        // Add error class
        input.classList.add('is-invalid');
        
        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        errorDiv.setAttribute('data-error-for', input.id || input.name);
        
        // Insert after input or input group
        const inputGroup = input.closest('.input-group');
        const target = inputGroup || input;
        target.parentNode.insertBefore(errorDiv, target.nextSibling);
        
        // Focus on first error
        if (!document.querySelector('.is-invalid:focus')) {
            input.focus();
        }
    }
    
    /**
     * Clear field error
     */
    clearFieldError(input) {
        input.classList.remove('is-invalid');
        
        // Remove error message
        const errorId = input.id || input.name;
        const errorDiv = document.querySelector(`[data-error-for="${errorId}"]`);
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    /**
     * Get clean numeric value from input
     */
    getNumericValue(input) {
        const value = input.value.replace(/[^\d.-]/g, '');
        return parseFloat(value) || 0;
    }
    
    /**
     * Set numeric value to input
     */
    setNumericValue(input, value) {
        if (input.classList.contains('currency-input') || input.dataset.format === 'currency') {
            input.value = this.formatCurrency(value);
        } else {
            input.value = value;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (!window.inputValidator) {
        window.inputValidator = new InputValidator();
        console.log('✅ InputValidator initialized from input-validator.js');
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InputValidator;
}
