#!/usr/bin/env python3
"""
Font Quality Checker for Qatar Car Dealership System
Checks font files and provides recommendations
"""

import os
import hashlib
from pathlib import Path

class FontChecker:
    def __init__(self, fonts_dir="static/fonts"):
        self.fonts_dir = Path(fonts_dir)
        self.required_fonts = [
            "Cairo-Regular.ttf",
            "NotoNaskhArabic-Regular.ttf"
        ]
        self.recommended_fonts = [
            "Cairo-Bold.ttf",
            "Cairo-Light.ttf",
            "NotoSansArabic-Regular.ttf",
            "Amiri-Regular.ttf"
        ]
    
    def check_font_exists(self, font_name):
        """Check if font file exists"""
        font_path = self.fonts_dir / font_name
        return font_path.exists()
    
    def get_font_size(self, font_name):
        """Get font file size"""
        font_path = self.fonts_dir / font_name
        if font_path.exists():
            return os.path.getsize(font_path)
        return 0
    
    def get_font_hash(self, font_name):
        """Get font file hash for integrity check"""
        font_path = self.fonts_dir / font_name
        if font_path.exists():
            with open(font_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        return None
    
    def check_font_quality(self, font_name):
        """Check font quality and provide recommendations"""
        font_path = self.fonts_dir / font_name
        
        if not font_path.exists():
            return {
                'status': 'missing',
                'message': f'❌ Font {font_name} is missing',
                'recommendations': [
                    'Download the font from Google Fonts or official source',
                    'Place it in the static/fonts directory',
                    'Verify the file name matches exactly'
                ]
            }
        
        file_size = self.get_font_size(font_name)
        
        # Check file size (reasonable ranges for Arabic fonts)
        if file_size < 50000:  # Less than 50KB
            status = 'warning'
            message = f'⚠️  Font {font_name} seems too small ({file_size:,} bytes)'
            recommendations = [
                'Verify the font file is complete',
                'Check if it supports Arabic characters',
                'Consider downloading from official source'
            ]
        elif file_size > 5000000:  # More than 5MB
            status = 'warning'
            message = f'⚠️  Font {font_name} is very large ({file_size:,} bytes)'
            recommendations = [
                'Consider subsetting the font',
                'Remove unused glyphs',
                'Use WOFF2 format for web'
            ]
        else:
            status = 'good'
            message = f'✅ Font {font_name} looks good ({file_size:,} bytes)'
            recommendations = []
        
        return {
            'status': status,
            'message': message,
            'size': file_size,
            'hash': self.get_font_hash(font_name),
            'recommendations': recommendations
        }
    
    def check_arabic_support(self, font_name):
        """Check if font supports Arabic characters (basic check)"""
        try:
            from fontTools.ttLib import TTFont
            
            font_path = self.fonts_dir / font_name
            if not font_path.exists():
                return False
            
            font = TTFont(font_path)
            
            # Check for Arabic Unicode ranges
            arabic_ranges = [
                (0x0600, 0x06FF),  # Arabic
                (0x0750, 0x077F),  # Arabic Supplement
                (0xFB50, 0xFDFF),  # Arabic Presentation Forms-A
                (0xFE70, 0xFEFF),  # Arabic Presentation Forms-B
            ]
            
            cmap = font.getBestCmap()
            arabic_support = False
            
            for start, end in arabic_ranges:
                for code_point in range(start, min(start + 10, end + 1)):
                    if code_point in cmap:
                        arabic_support = True
                        break
                if arabic_support:
                    break
            
            font.close()
            return arabic_support
            
        except ImportError:
            print("⚠️  fonttools not available. Install with: pip install fonttools")
            return None
        except Exception as e:
            print(f"❌ Error checking Arabic support for {font_name}: {e}")
            return None
    
    def generate_font_report(self):
        """Generate comprehensive font report"""
        print("🔍 Font Quality Report")
        print("=" * 50)
        
        # Check required fonts
        print("\n📋 Required Fonts:")
        print("-" * 20)
        
        required_status = []
        for font in self.required_fonts:
            result = self.check_font_quality(font)
            print(f"{result['message']}")
            
            if result['recommendations']:
                for rec in result['recommendations']:
                    print(f"   💡 {rec}")
            
            # Check Arabic support
            arabic_support = self.check_arabic_support(font)
            if arabic_support is True:
                print(f"   ✅ Arabic support: Yes")
            elif arabic_support is False:
                print(f"   ❌ Arabic support: No")
            elif arabic_support is None:
                print(f"   ❓ Arabic support: Cannot verify")
            
            required_status.append(result['status'])
            print()
        
        # Check recommended fonts
        print("\n📋 Recommended Fonts:")
        print("-" * 25)
        
        recommended_status = []
        for font in self.recommended_fonts:
            result = self.check_font_quality(font)
            print(f"{result['message']}")
            
            if result['recommendations']:
                for rec in result['recommendations']:
                    print(f"   💡 {rec}")
            
            recommended_status.append(result['status'])
            print()
        
        # Check for extra fonts
        print("\n📋 Additional Fonts Found:")
        print("-" * 30)
        
        all_fonts = list(self.fonts_dir.glob("*.ttf")) + list(self.fonts_dir.glob("*.otf"))
        extra_fonts = []
        
        for font_file in all_fonts:
            if font_file.name not in self.required_fonts + self.recommended_fonts:
                extra_fonts.append(font_file.name)
                result = self.check_font_quality(font_file.name)
                print(f"{result['message']}")
        
        if not extra_fonts:
            print("No additional fonts found.")
        
        # Summary
        print("\n📊 Summary:")
        print("-" * 15)
        
        required_good = required_status.count('good')
        required_total = len(self.required_fonts)
        
        print(f"Required fonts: {required_good}/{required_total} OK")
        
        if required_good == required_total:
            print("✅ All required fonts are available and look good!")
        else:
            print("❌ Some required fonts need attention.")
        
        recommended_good = recommended_status.count('good')
        recommended_total = len(self.recommended_fonts)
        
        print(f"Recommended fonts: {recommended_good}/{recommended_total} available")
        print(f"Additional fonts: {len(extra_fonts)} found")
        
        # Calculate total size
        total_size = 0
        for font_file in all_fonts:
            total_size += os.path.getsize(font_file)
        
        print(f"Total font size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        
        return {
            'required_status': required_status,
            'recommended_status': recommended_status,
            'extra_fonts': extra_fonts,
            'total_size': total_size
        }
    
    def check_css_references(self):
        """Check if CSS files reference the fonts correctly"""
        print("\n🎨 CSS Font References:")
        print("-" * 25)
        
        css_files = [
            "static/css/style.css",
            "static/css/arabic-fonts.css",
            "static/css/font-enhancements.css"
        ]
        
        for css_file in css_files:
            css_path = Path(css_file)
            if css_path.exists():
                print(f"✅ Found: {css_file}")
                
                with open(css_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for font references
                font_references = []
                for font in self.required_fonts + self.recommended_fonts:
                    font_name = font.replace('.ttf', '').replace('-Regular', '')
                    if font_name.lower() in content.lower():
                        font_references.append(font_name)
                
                if font_references:
                    print(f"   📝 References: {', '.join(font_references)}")
                else:
                    print(f"   ⚠️  No font references found")
            else:
                print(f"❌ Missing: {css_file}")
        
        # Check base.html
        base_html = Path("templates/base.html")
        if base_html.exists():
            print(f"✅ Found: templates/base.html")
            
            with open(base_html, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'font' in content.lower():
                print(f"   📝 Contains font references")
            else:
                print(f"   ⚠️  No font references found")
        else:
            print(f"❌ Missing: templates/base.html")
    
    def provide_recommendations(self):
        """Provide optimization recommendations"""
        print("\n💡 Optimization Recommendations:")
        print("-" * 35)
        
        recommendations = [
            "1. Use font-display: swap for better loading performance",
            "2. Preload critical fonts in HTML head",
            "3. Subset fonts to include only needed characters",
            "4. Convert to WOFF2 format for better compression",
            "5. Use CSS font-feature-settings for Arabic text",
            "6. Implement font loading strategies with JavaScript",
            "7. Monitor font loading performance with Web Vitals",
            "8. Consider using system fonts as fallbacks",
            "9. Test fonts across different browsers and devices",
            "10. Keep font files under 100KB when possible"
        ]
        
        for rec in recommendations:
            print(f"   {rec}")
        
        print("\n🔗 Useful Resources:")
        print("-" * 20)
        
        resources = [
            "Google Fonts: https://fonts.google.com/",
            "Font optimization guide: https://web.dev/font-best-practices/",
            "Arabic typography: https://arabictypography.com/",
            "Font loading strategies: https://web.dev/optimize-webfont-loading/"
        ]
        
        for resource in resources:
            print(f"   📖 {resource}")

def main():
    """Main function"""
    print("🎨 Font Quality Checker for Qatar Car Dealership")
    print("=" * 50)
    
    checker = FontChecker()
    
    # Generate comprehensive report
    checker.generate_font_report()
    checker.check_css_references()
    checker.provide_recommendations()
    
    print("\n🎉 Font check completed!")

if __name__ == "__main__":
    main()
