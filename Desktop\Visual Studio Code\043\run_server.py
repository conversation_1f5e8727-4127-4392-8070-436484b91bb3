#!/usr/bin/env python3
"""
Simple server runner for the car dealership system
"""

try:
    from app import create_app
    
    print("Creating Flask application...")
    app = create_app()
    
    print("Starting server on http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000,
        use_reloader=False  # Disable reloader to avoid issues
    )
    
except Exception as e:
    print(f"Error starting server: {e}")
    import traceback
    traceback.print_exc()
