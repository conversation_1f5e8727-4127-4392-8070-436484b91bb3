{% extends "base.html" %}

{% block title %}اختبار الأرقام العربية والكتابة العشوائية{% endblock %}

{% block extra_css %}
<style>
.test-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.test-title {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.demo-box {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    text-align: center;
}

.demo-box.active {
    border-color: #007bff;
    background: #e7f3ff;
}

.number-display {
    font-size: 2rem;
    font-weight: bold;
    margin: 10px 0;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.typing-demo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-panel {
    background: #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}

.feature-demo {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.feature-demo.active {
    border-color: #28a745;
    background: #d4edda;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.floating-numbers-demo {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 15px;
    overflow: hidden;
    margin: 20px 0;
}

.test-input {
    font-size: 1.2rem;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
    margin: 10px 0;
    width: 100%;
}

.test-input:focus {
    border-color: #007bff;
    outline: none;
}

.result-display {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="text-center mb-4">
        <h1 class="random-typing">🔢 اختبار الأرقام العربية والكتابة العشوائية</h1>
        <p class="text-muted">صفحة اختبار شاملة لجميع ميزات الأرقام العربية والتأثيرات البصرية</p>
    </div>

    <!-- Statistics Demo -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-chart-bar me-2"></i>إحصائيات بالأرقام العربية</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number animate-number">1234567</span>
                <span class="stat-label">إجمالي المبيعات</span>
            </div>
            <div class="stat-card">
                <span class="stat-number animate-number">98765</span>
                <span class="stat-label">عدد العملاء</span>
            </div>
            <div class="stat-card">
                <span class="stat-number animate-number">456789</span>
                <span class="stat-label">الأرباح الشهرية</span>
            </div>
            <div class="stat-card">
                <span class="stat-number animate-number">12345</span>
                <span class="stat-label">السيارات المباعة</span>
            </div>
        </div>
    </div>

    <!-- Number Conversion Demo -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-exchange-alt me-2"></i>تحويل الأرقام</h3>
        
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">أدخل أرقام إنجليزية:</label>
                <input type="text" class="test-input" id="englishInput" placeholder="مثال: 123456.78">
                <div class="result-display" id="arabicResult">النتيجة ستظهر هنا...</div>
            </div>
            <div class="col-md-6">
                <label class="form-label">أدخل أرقام عربية:</label>
                <input type="text" class="test-input" id="arabicInput" placeholder="مثال: ١٢٣٤٥٦٫٧٨">
                <div class="result-display" id="englishResult">النتيجة ستظهر هنا...</div>
            </div>
        </div>
    </div>

    <!-- Typing Effects Demo -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-keyboard me-2"></i>تأثيرات الكتابة العشوائية</h3>
        
        <div class="row">
            <div class="col-md-4">
                <div class="demo-box">
                    <h5>كتابة بطيئة</h5>
                    <div class="typing-demo" id="slowTyping">مرحباً بكم في نظام معرض السيارات</div>
                    <button class="btn btn-primary btn-sm" onclick="startSlowTyping()">ابدأ</button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="demo-box">
                    <h5>كتابة سريعة</h5>
                    <div class="typing-demo" id="fastTyping">أرقام السيارات القطرية المميزة</div>
                    <button class="btn btn-success btn-sm" onclick="startFastTyping()">ابدأ</button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="demo-box">
                    <h5>كتابة متدرجة</h5>
                    <div class="typing-demo gradient-typing" id="gradientTyping">نظام شامل ومتطور</div>
                    <button class="btn btn-warning btn-sm" onclick="startGradientTyping()">ابدأ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Number Animation Demo -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-magic me-2"></i>تحريك الأرقام</h3>
        
        <div class="row">
            <div class="col-md-3">
                <div class="demo-box">
                    <h6>أرقام نابضة</h6>
                    <div class="number-display pulsing-number" id="pulsingNumber">١٢٣٤٥٦</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="demo-box">
                    <h6>أرقام متوهجة</h6>
                    <div class="number-display glowing-number" id="glowingNumber">٧٨٩٠١٢</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="demo-box">
                    <h6>أرقام خاصة</h6>
                    <div class="number-display special-number" id="specialNumber">٩٩٩٩</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="demo-box">
                    <h6>عد تنازلي</h6>
                    <div class="countdown-number" id="countdownNumber">٦٠</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Currency Formatting Demo -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-money-bill-wave me-2"></i>تنسيق العملة</h3>
        
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">أدخل مبلغ:</label>
                <input type="number" class="test-input" id="currencyInput" placeholder="مثال: 123456.78">
            </div>
            <div class="col-md-6">
                <label class="form-label">التنسيقات المختلفة:</label>
                <div class="result-display">
                    <div><strong>عادي:</strong> <span id="normalCurrency">٠ ريال</span></div>
                    <div><strong>قطري:</strong> <span id="qatarCurrency">٠ ر.ق</span></div>
                    <div><strong>مع فواصل:</strong> <span id="formattedCurrency">٠</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plate Numbers Demo -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-id-card me-2"></i>أرقام اللوحات</h3>
        
        <div class="row">
            <div class="col-md-3">
                <div class="plate-number">A-١</div>
                <small class="text-muted d-block text-center">VIP</small>
            </div>
            <div class="col-md-3">
                <div class="plate-number">B-٧٧٧٧</div>
                <small class="text-muted d-block text-center">مكرر</small>
            </div>
            <div class="col-md-3">
                <div class="plate-number">C-١٢٣٤</div>
                <small class="text-muted d-block text-center">متسلسل</small>
            </div>
            <div class="col-md-3">
                <div class="plate-number">D-١٢٢١</div>
                <small class="text-muted d-block text-center">مرآة</small>
            </div>
        </div>
    </div>

    <!-- Control Panel -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-cog me-2"></i>لوحة التحكم</h3>
        
        <div class="control-panel">
            <div class="row">
                <div class="col-md-6">
                    <h5>إعدادات التحويل</h5>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="autoConvert" checked>
                        <label class="form-check-label" for="autoConvert">
                            تحويل تلقائي للأرقام
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="realTimeUpdate" checked>
                        <label class="form-check-label" for="realTimeUpdate">
                            تحديث في الوقت الفعلي
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>إعدادات التأثيرات</h5>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableAnimations" checked>
                        <label class="form-check-label" for="enableAnimations">
                            تفعيل التحريك
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableTyping" checked>
                        <label class="form-check-label" for="enableTyping">
                            تفعيل الكتابة العشوائية
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-primary me-2" onclick="testAllFeatures()">
                    <i class="fas fa-play me-2"></i>اختبار جميع الميزات
                </button>
                <button class="btn btn-success me-2" onclick="resetDemo()">
                    <i class="fas fa-refresh me-2"></i>إعادة تعيين
                </button>
                <button class="btn btn-info" onclick="generateRandomNumbers()">
                    <i class="fas fa-random me-2"></i>أرقام عشوائية
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Numbers Background -->
    <div class="floating-numbers-demo">
        <div class="text-center text-white p-4">
            <h4>أرقام متحركة في الخلفية</h4>
            <p>تأثير بصري جميل للأرقام العربية</p>
        </div>
    </div>
</div>

<!-- Floating Numbers Container -->
<div class="floating-numbers" id="floatingNumbers"></div>
{% endblock %}

{% block extra_js %}
<script>
// Test functions for typing effects
function startSlowTyping() {
    const element = document.getElementById('slowTyping');
    const text = 'مرحباً بكم في نظام معرض السيارات المتطور';
    
    if (window.ArabicNumbers) {
        new window.ArabicNumbers.RandomTyping(element, text, {
            speed: 150,
            randomDelay: 100,
            showCursor: true
        }).start();
    }
}

function startFastTyping() {
    const element = document.getElementById('fastTyping');
    const text = 'أرقام السيارات القطرية المميزة والنادرة';
    
    if (window.ArabicNumbers) {
        new window.ArabicNumbers.RandomTyping(element, text, {
            speed: 50,
            randomDelay: 20,
            showCursor: true
        }).start();
    }
}

function startGradientTyping() {
    const element = document.getElementById('gradientTyping');
    const text = 'نظام شامل ومتطور لإدارة المعارض';
    
    if (window.ArabicNumbers) {
        new window.ArabicNumbers.RandomTyping(element, text, {
            speed: 80,
            randomDelay: 40,
            showCursor: true,
            loop: true
        }).start();
    }
}

// Number conversion demo
document.getElementById('englishInput').addEventListener('input', function() {
    const value = this.value;
    const arabicResult = document.getElementById('arabicResult');
    
    if (window.ArabicNumbers) {
        arabicResult.textContent = window.ArabicNumbers.toArabic(value);
    }
});

document.getElementById('arabicInput').addEventListener('input', function() {
    const value = this.value;
    const englishResult = document.getElementById('englishResult');
    
    if (window.ArabicNumbers) {
        englishResult.textContent = window.ArabicNumbers.toEnglish(value);
    }
});

// Currency formatting demo
document.getElementById('currencyInput').addEventListener('input', function() {
    const value = parseFloat(this.value) || 0;
    
    if (window.ArabicNumbers) {
        document.getElementById('normalCurrency').textContent = 
            window.ArabicNumbers.formatCurrency(value, 'ريال');
        document.getElementById('qatarCurrency').textContent = 
            window.ArabicNumbers.formatCurrency(value, 'ر.ق');
        document.getElementById('formattedCurrency').textContent = 
            window.ArabicNumbers.formatNumber(value);
    }
});

// Countdown demo
function startCountdown() {
    let count = 60;
    const element = document.getElementById('countdownNumber');
    
    const timer = setInterval(() => {
        if (window.ArabicNumbers) {
            element.textContent = window.ArabicNumbers.toArabic(count);
        }
        count--;
        
        if (count < 0) {
            clearInterval(timer);
            element.textContent = 'انتهى!';
        }
    }, 1000);
}

// Test all features
function testAllFeatures() {
    startSlowTyping();
    setTimeout(startFastTyping, 1000);
    setTimeout(startGradientTyping, 2000);
    setTimeout(startCountdown, 3000);
    generateRandomNumbers();
    createFloatingNumbers();
}

// Reset demo
function resetDemo() {
    location.reload();
}

// Generate random numbers
function generateRandomNumbers() {
    const numbers = [
        Math.floor(Math.random() * 1000000),
        Math.floor(Math.random() * 100000),
        Math.floor(Math.random() * 10000),
        Math.floor(Math.random() * 1000)
    ];
    
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach((element, index) => {
        if (numbers[index] && window.ArabicNumbers) {
            window.ArabicNumbers.animateNumber(element, 0, numbers[index], 2000);
        }
    });
}

// Create floating numbers
function createFloatingNumbers() {
    const container = document.getElementById('floatingNumbers');
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    for (let i = 0; i < 20; i++) {
        const number = document.createElement('div');
        number.className = 'floating-number';
        number.textContent = arabicDigits[Math.floor(Math.random() * arabicDigits.length)];
        number.style.left = Math.random() * 100 + '%';
        number.style.animationDelay = Math.random() * 10 + 's';
        number.style.animationDuration = (Math.random() * 10 + 10) + 's';
        
        container.appendChild(number);
        
        // Remove after animation
        setTimeout(() => {
            if (number.parentNode) {
                number.parentNode.removeChild(number);
            }
        }, 20000);
    }
}

// Initialize demo
document.addEventListener('DOMContentLoaded', function() {
    // Start countdown immediately
    setTimeout(startCountdown, 1000);
    
    // Create initial floating numbers
    setTimeout(createFloatingNumbers, 2000);
    
    // Auto-generate random numbers every 10 seconds
    setInterval(generateRandomNumbers, 10000);
});

// Control panel functionality
document.getElementById('autoConvert').addEventListener('change', function() {
    if (this.checked) {
        console.log('تم تفعيل التحويل التلقائي');
    } else {
        console.log('تم إيقاف التحويل التلقائي');
    }
});

document.getElementById('enableAnimations').addEventListener('change', function() {
    const animations = document.querySelectorAll('.pulsing-number, .glowing-number, .special-number');
    animations.forEach(element => {
        if (this.checked) {
            element.style.animationPlayState = 'running';
        } else {
            element.style.animationPlayState = 'paused';
        }
    });
});
</script>
{% endblock %}
