{% extends "base.html" %}

{% block title %}إضافة سيارة جديدة - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إضافة سيارة جديدة</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('cars.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">بيانات السيارة</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="carForm">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم السيارة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <select class="form-select" id="name" name="name" required>
                                        <option value="">اختر اسم السيارة</option>
                                        <option value="كامري فل كامل">كامري فل كامل</option>
                                        <option value="أكورد فل كامل">أكورد فل كامل</option>
                                        <option value="التيما فل كامل">التيما فل كامل</option>
                                        <option value="سيفيك فل كامل">سيفيك فل كامل</option>
                                        <option value="كورولا فل كامل">كورولا فل كامل</option>
                                        <option value="سوناتا فل كامل">سوناتا فل كامل</option>
                                        <option value="إلنترا فل كامل">إلنترا فل كامل</option>
                                        <option value="سنترا فل كامل">سنترا فل كامل</option>
                                        <option value="custom">أخرى (أدخل يدوياً)</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" onclick="addCustomName()" title="إضافة اسم جديد">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <input type="text" class="form-control mt-2 d-none" id="custom_name" placeholder="أدخل اسم السيارة">
                                <div class="form-text">اختر من القائمة أو أدخل اسم جديد</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brand" class="form-label">الماركة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <select class="form-select" id="brand" name="brand" required>
                                        <option value="">اختر الماركة</option>
                                        <option value="تويوتا">تويوتا</option>
                                        <option value="هوندا">هوندا</option>
                                        <option value="نيسان">نيسان</option>
                                        <option value="هيونداي">هيونداي</option>
                                        <option value="كيا">كيا</option>
                                        <option value="مازدا">مازدا</option>
                                        <option value="ميتسوبيشي">ميتسوبيشي</option>
                                        <option value="سوزوكي">سوزوكي</option>
                                        <option value="فولكس واجن">فولكس واجن</option>
                                        <option value="بي إم دبليو">بي إم دبليو</option>
                                        <option value="مرسيدس">مرسيدس</option>
                                        <option value="أودي">أودي</option>
                                        <option value="لكزس">لكزس</option>
                                        <option value="إنفينيتي">إنفينيتي</option>
                                        <option value="أكورا">أكورا</option>
                                        <option value="custom">أخرى (أدخل يدوياً)</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" onclick="addCustomBrand()" title="إضافة ماركة جديدة">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <input type="text" class="form-control mt-2 d-none" id="custom_brand" placeholder="أدخل الماركة">
                                <div class="form-text">اختر من القائمة أو أدخل ماركة جديدة</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">الموديل <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <select class="form-select" id="model" name="model" required>
                                        <option value="">اختر الموديل</option>
                                        <option value="كامري">كامري</option>
                                        <option value="أكورد">أكورد</option>
                                        <option value="التيما">التيما</option>
                                        <option value="سيفيك">سيفيك</option>
                                        <option value="كورولا">كورولا</option>
                                        <option value="سوناتا">سوناتا</option>
                                        <option value="إلنترا">إلنترا</option>
                                        <option value="سنترا">سنترا</option>
                                        <option value="CX-5">CX-5</option>
                                        <option value="CX-9">CX-9</option>
                                        <option value="راف 4">راف 4</option>
                                        <option value="هايلاندر">هايلاندر</option>
                                        <option value="باثفايندر">باثفايندر</option>
                                        <option value="مورانو">مورانو</option>
                                        <option value="توسان">توسان</option>
                                        <option value="سانتا في">سانتا في</option>
                                        <option value="سورينتو">سورينتو</option>
                                        <option value="سبورتاج">سبورتاج</option>
                                        <option value="custom">أخرى (أدخل يدوياً)</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" onclick="addCustomModel()" title="إضافة موديل جديد">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <input type="text" class="form-control mt-2 d-none" id="custom_model" placeholder="أدخل الموديل">
                                <div class="form-text">اختر من القائمة أو أدخل موديل جديد</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="year" class="form-label">سنة الصنع <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="year" name="year" 
                                       min="1990" max="{{ current_datetime.year + 1 }}" required>
                            </div>
                        </div>
                        
                        <!-- Identification -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="chassis_number" class="form-label">رقم الشاسيه <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="chassis_number" name="chassis_number" required>
                                <div class="form-text">رقم فريد لكل سيارة</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="plate_number" class="form-label">رقم اللوحة</label>
                                <input type="text" class="form-control" id="plate_number" name="plate_number">
                                <div class="form-text">اختياري - يمكن إضافته لاحقاً</div>
                            </div>
                        </div>
                        
                        <!-- Specifications -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">اللون</label>
                                <div class="input-group">
                                    <select class="form-select" id="color" name="color">
                                        <option value="">اختر اللون</option>
                                        <option value="أبيض">أبيض</option>
                                        <option value="أبيض لؤلؤي">أبيض لؤلؤي</option>
                                        <option value="أسود">أسود</option>
                                        <option value="أسود لامع">أسود لامع</option>
                                        <option value="فضي">فضي</option>
                                        <option value="فضي معدني">فضي معدني</option>
                                        <option value="رمادي">رمادي</option>
                                        <option value="رمادي غامق">رمادي غامق</option>
                                        <option value="أحمر">أحمر</option>
                                        <option value="أحمر كرزي">أحمر كرزي</option>
                                        <option value="أزرق">أزرق</option>
                                        <option value="أزرق غامق">أزرق غامق</option>
                                        <option value="أزرق معدني">أزرق معدني</option>
                                        <option value="ذهبي">ذهبي</option>
                                        <option value="بني">بني</option>
                                        <option value="بني غامق">بني غامق</option>
                                        <option value="أخضر">أخضر</option>
                                        <option value="أخضر غامق">أخضر غامق</option>
                                        <option value="بيج">بيج</option>
                                        <option value="كريمي">كريمي</option>
                                        <option value="برتقالي">برتقالي</option>
                                        <option value="أصفر">أصفر</option>
                                        <option value="وردي">وردي</option>
                                        <option value="بنفسجي">بنفسجي</option>
                                        <option value="custom">أخرى (أدخل يدوياً)</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" onclick="addCustomColor()" title="إضافة لون جديد">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <input type="text" class="form-control mt-2 d-none" id="custom_color" placeholder="أدخل اللون">
                                <div class="form-text">اختر من القائمة أو أدخل لون جديد</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="engine_size" class="form-label">حجم المحرك</label>
                                <input type="text" class="form-control" id="engine_size" name="engine_size" 
                                       placeholder="مثال: 2.5L">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fuel_type" class="form-label">نوع الوقود</label>
                                <select class="form-select" id="fuel_type" name="fuel_type">
                                    <option value="">اختر نوع الوقود</option>
                                    <option value="بنزين">بنزين</option>
                                    <option value="ديزل">ديزل</option>
                                    <option value="هايبرد">هايبرد</option>
                                    <option value="كهربائي">كهربائي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="transmission" class="form-label">ناقل الحركة</label>
                                <select class="form-select" id="transmission" name="transmission">
                                    <option value="">اختر ناقل الحركة</option>
                                    <option value="أوتوماتيك">أوتوماتيك</option>
                                    <option value="عادي">عادي</option>
                                    <option value="CVT">CVT</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mileage" class="form-label">المسافة المقطوعة (كم)</label>
                                <input type="number" class="form-control" id="mileage" name="mileage" min="0">
                            </div>
                        </div>

                        <!-- Car Type and Condition -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="car_type" class="form-label">نوع السيارة <span class="text-danger">*</span></label>
                                <select class="form-select" id="car_type" name="car_type" required>
                                    <option value="">اختر نوع السيارة</option>
                                    <option value="new">جديدة</option>
                                    <option value="used">مستعملة</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="condition_rating" class="form-label">تقييم الحالة</label>
                                <select class="form-select" id="condition_rating" name="condition_rating">
                                    <option value="">اختر تقييم الحالة</option>
                                    <option value="excellent">ممتاز</option>
                                    <option value="very_good">جيد جداً</option>
                                    <option value="good">جيد</option>
                                    <option value="fair">مقبول</option>
                                    <option value="poor">ضعيف</option>
                                </select>
                            </div>
                        </div>

                        <!-- Pricing -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control currency-input" id="price"
                                           name="price" data-format="currency" required
                                           placeholder="أدخل السعر" autocomplete="off">
                                    <span class="input-group-text">ريال قطري</span>
                                    <button class="btn btn-outline-secondary" type="button" onclick="resetPrice()" title="إعادة تعيين السعر">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </div>
                                <div class="form-text">مثال: 50000 أو 50,000 | <button type="button" class="btn btn-link btn-sm p-0" onclick="setSamplePrice()">استخدام سعر تجريبي</button></div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة</label>
                                <div class="input-group">
                                    <input type="text" class="form-control currency-input" id="cost_price"
                                           name="cost_price" data-format="currency"
                                           placeholder="أدخل سعر التكلفة" autocomplete="off">
                                    <span class="input-group-text">ريال قطري</span>
                                    <button class="btn btn-outline-secondary" type="button" onclick="resetCostPrice()" title="إعادة تعيين سعر التكلفة">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </div>
                                <div class="form-text">اختياري - للتقارير المالية | <button type="button" class="btn btn-link btn-sm p-0" onclick="setSampleCostPrice()">استخدام سعر تجريبي</button></div>
                            </div>
                        </div>
                        
                        <!-- Description -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف السيارة</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="أضف أي تفاصيل إضافية عن السيارة..."></textarea>
                            </div>
                        </div>

                        <!-- Condition Notes -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="condition_notes" class="form-label">ملاحظات الحالة</label>
                                <textarea class="form-control" id="condition_notes" name="condition_notes" rows="2"
                                          placeholder="أضف ملاحظات حول حالة السيارة (خدوش، أعطال، إصلاحات، إلخ)..."></textarea>
                            </div>
                        </div>
                        
                        <!-- Images -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="images" class="form-label">صور السيارة</label>
                                <input type="file" class="form-control image-input" id="images" 
                                       name="images" multiple accept="image/*">
                                <div class="form-text">يمكنك اختيار عدة صور (PNG, JPG, JPEG, GIF)</div>
                                <div class="image-preview mt-2"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ السيارة
                            </button>
                            <button type="button" class="btn btn-outline-warning ms-2" onclick="resetPricesOnly()">
                                <i class="fas fa-dollar-sign me-2"></i>
                                إعادة تعيين الأسعار
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="resetFormCompletely()">
                                <i class="fas fa-redo me-2"></i>
                                إعادة تعيين النموذج
                            </button>
                            <a href="{{ url_for('cars.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">نصائح مهمة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                    <ul class="mb-0">
                        <li>رقم الشاسيه يجب أن يكون فريداً لكل سيارة</li>
                        <li>حدد نوع السيارة (جديدة أم مستعملة)</li>
                        <li>اختر تقييم الحالة المناسب</li>
                        <li>أضف ملاحظات مفصلة عن حالة السيارة</li>
                        <li>تأكد من صحة البيانات قبل الحفظ</li>
                        <li>الصور تساعد في عرض السيارة بشكل أفضل</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                    <p class="mb-0">الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة ولا يمكن تركها فارغة.</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">إدارة القوائم</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="addCustomName()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة اسم سيارة جديد
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="addCustomBrand()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة ماركة جديدة
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="addCustomModel()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة موديل جديد
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="addCustomColor()">
                        <i class="fas fa-palette me-2"></i>
                        إضافة لون جديد
                    </button>
                    <hr>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="fillSampleCarData()">
                        <i class="fas fa-magic me-2"></i>
                        بيانات سيارة تجريبية
                    </button>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">إعادة تعيين الأسعار</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="resetPrice()">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين سعر البيع
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="resetCostPrice()">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين سعر التكلفة
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="resetPricesOnly()">
                        <i class="fas fa-dollar-sign me-2"></i>
                        إعادة تعيين جميع الأسعار
                    </button>
                    <hr>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="setSamplePrice()">
                        <i class="fas fa-magic me-2"></i>
                        سعر بيع تجريبي (75,000)
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="setSampleCostPrice()">
                        <i class="fas fa-magic me-2"></i>
                        سعر تكلفة تجريبي (65,000)
                    </button>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('cars.index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع السيارات
                    </a>
                    <a href="{{ url_for('customers.index') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-users me-2"></i>
                        إدارة العملاء
                    </a>
                    <a href="{{ url_for('sales.index') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-handshake me-2"></i>
                        المبيعات
                    </a>
                    <hr>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="resetFormCompletely()">
                        <i class="fas fa-redo me-2"></i>
                        إعادة تعيين النموذج بالكامل
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-save form data
    $('#carForm input, #carForm select, #carForm textarea').on('change', function() {
        saveFormData('carForm');
    });
    
    // Load saved form data
    loadFormData('carForm');
    
    // Form validation
    $('#carForm').on('submit', function(e) {
        if (!validateCarForm()) {
            e.preventDefault();
        }
    });
    
    // DIRECT PRICE INPUT FIX - Works immediately
    function setupPriceInput() {
        const priceInput = $('#price');

        if (priceInput.length) {
            console.log('🔧 Setting up price input directly...');

            // Remove any existing handlers
            priceInput.off('input focus blur');

            // Input event - clean as user types (allow all input first)
            priceInput.on('input', function(e) {
                let value = $(this).val();
                let cursorPos = this.selectionStart;

                // Convert Arabic digits to English
                value = value.replace(/[٠-٩]/g, function(d) {
                    return '٠١٢٣٤٥٦٧٨٩'.indexOf(d);
                });

                // Remove non-numeric characters except decimal point
                let cleanValue = value.replace(/[^\d.]/g, '');

                // Handle multiple decimal points
                const parts = cleanValue.split('.');
                if (parts.length > 2) {
                    cleanValue = parts[0] + '.' + parts.slice(1).join('');
                }

                // Only update if value actually changed
                if ($(this).val() !== cleanValue) {
                    $(this).val(cleanValue);
                    // Restore cursor position
                    this.setSelectionRange(cursorPos, cursorPos);
                }

                $(this).removeClass('is-invalid');
            });

            // Focus event - remove formatting
            priceInput.on('focus', function() {
                let value = $(this).val().replace(/[^\d.]/g, '');
                $(this).val(value);
            });

            // Blur event - format and validate
            priceInput.on('blur', function() {
                let value = parseFloat($(this).val());
                if (!isNaN(value) && value >= 0) {
                    // Format with commas (allow zero)
                    $(this).val(value.toLocaleString('en-US'));
                }
            });

            console.log('✅ Price input setup complete');
        }
    }

    // Setup cost price input too
    function setupCostPriceInput() {
        const costPriceInput = $('#cost_price');

        if (costPriceInput.length) {
            console.log('🔧 Setting up cost price input directly...');

            // Remove any existing handlers
            costPriceInput.off('input focus blur');

            // Input event - clean as user types (allow all input first)
            costPriceInput.on('input', function(e) {
                let value = $(this).val();
                let cursorPos = this.selectionStart;

                // Convert Arabic digits to English
                value = value.replace(/[٠-٩]/g, function(d) {
                    return '٠١٢٣٤٥٦٧٨٩'.indexOf(d);
                });

                // Remove non-numeric characters except decimal point
                let cleanValue = value.replace(/[^\d.]/g, '');

                // Handle multiple decimal points
                const parts = cleanValue.split('.');
                if (parts.length > 2) {
                    cleanValue = parts[0] + '.' + parts.slice(1).join('');
                }

                // Only update if value actually changed
                if ($(this).val() !== cleanValue) {
                    $(this).val(cleanValue);
                    // Restore cursor position
                    this.setSelectionRange(cursorPos, cursorPos);
                }

                $(this).removeClass('is-invalid');
            });

            // Focus event - remove formatting
            costPriceInput.on('focus', function() {
                let value = $(this).val().replace(/[^\d.]/g, '');
                $(this).val(value);
            });

            // Blur event - format
            costPriceInput.on('blur', function() {
                let value = parseFloat($(this).val());
                if (!isNaN(value) && value >= 0) {
                    // Format with commas (allow zero)
                    $(this).val(value.toLocaleString('en-US'));
                }
            });

            console.log('✅ Cost price input setup complete');
        }
    }

    // Setup both price inputs immediately
    setupPriceInput();
    setupCostPriceInput();

    // Also setup when Simple Price Handler is ready
    if (window.simplePriceHandler) {
        window.simplePriceHandler.refresh();
    }

    // Ensure no conflicting handlers
    $('.currency-input').not('#price, #cost_price').off('input').on('input', function() {
        let value = $(this).val();
        // Convert Arabic digits
        value = value.replace(/[٠-٩]/g, function(d) {
            return '٠١٢٣٤٥٦٧٨٩'.indexOf(d);
        });
        // Clean non-numeric
        value = value.replace(/[^\d.]/g, '');
        $(this).val(value);
    });

    // Handle car type change
    $('#car_type').on('change', function() {
        const carType = $(this).val();
        const mileageField = $('#mileage').closest('.mb-3');
        const conditionField = $('#condition_rating').closest('.mb-3');

        if (carType === 'new') {
            // For new cars, hide mileage and set default condition
            mileageField.hide();
            $('#mileage').val(0);
            $('#condition_rating').val('excellent');
            conditionField.find('label').html('تقييم الحالة <span class="text-muted">(جديدة)</span>');
        } else if (carType === 'used') {
            // For used cars, show mileage and reset condition
            mileageField.show();
            $('#mileage').val('');
            $('#condition_rating').val('');
            conditionField.find('label').text('تقييم الحالة');
        } else {
            // Default state
            mileageField.show();
            conditionField.find('label').text('تقييم الحالة');
        }
    });

    // Trigger change event on page load
    $('#car_type').trigger('change');

    // Setup custom dropdowns
    setupCustomDropdowns();
});

// Custom dropdown functions
function setupCustomDropdowns() {
    // Handle name dropdown
    $('#name').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom_name').removeClass('d-none').focus();
            $(this).removeAttr('name');
            $('#custom_name').attr('name', 'name').attr('required', true);
        } else {
            $('#custom_name').addClass('d-none').removeAttr('name required');
            $(this).attr('name', 'name');
        }
    });

    // Handle brand dropdown
    $('#brand').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom_brand').removeClass('d-none').focus();
            $(this).removeAttr('name');
            $('#custom_brand').attr('name', 'brand').attr('required', true);
        } else {
            $('#custom_brand').addClass('d-none').removeAttr('name required');
            $(this).attr('name', 'brand');

            // Update model options based on brand
            updateModelOptions($(this).val());
        }
    });

    // Handle model dropdown
    $('#model').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom_model').removeClass('d-none').focus();
            $(this).removeAttr('name');
            $('#custom_model').attr('name', 'model').attr('required', true);
        } else {
            $('#custom_model').addClass('d-none').removeAttr('name required');
            $(this).attr('name', 'model');
        }
    });

    // Handle color dropdown
    $('#color').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom_color').removeClass('d-none').focus();
            $(this).removeAttr('name');
            $('#custom_color').attr('name', 'color');
        } else {
            $('#custom_color').addClass('d-none').removeAttr('name');
            $(this).attr('name', 'color');
        }
    });
}

function addCustomName() {
    $('#name').val('custom').trigger('change');
}

function addCustomBrand() {
    $('#brand').val('custom').trigger('change');
}

function addCustomModel() {
    $('#model').val('custom').trigger('change');
}

function addCustomColor() {
    $('#color').val('custom').trigger('change');
}

function updateModelOptions(brand) {
    const modelSelect = $('#model');
    const currentValue = modelSelect.val();

    // Clear current options except first and last
    modelSelect.find('option:not(:first):not(:last)').remove();

    // Brand-specific models
    const brandModels = {
        'تويوتا': ['كامري', 'كورولا', 'راف 4', 'هايلاندر', 'أفالون', 'برادو', 'لاند كروزر'],
        'هوندا': ['أكورد', 'سيفيك', 'CR-V', 'بايلوت', 'أوديسي', 'ريدج لاين'],
        'نيسان': ['التيما', 'سنترا', 'مورانو', 'باثفايندر', 'أرمادا', 'روج'],
        'هيونداي': ['سوناتا', 'إلنترا', 'توسان', 'سانتا في', 'جينيسيس', 'أزيرا'],
        'كيا': ['أوبتيما', 'فورتي', 'سورينتو', 'سبورتاج', 'كادينزا', 'ستينجر'],
        'مازدا': ['مازدا 6', 'مازدا 3', 'CX-5', 'CX-9', 'CX-3', 'MX-5'],
        'بي إم دبليو': ['الفئة الثالثة', 'الفئة الخامسة', 'الفئة السابعة', 'X3', 'X5', 'X7'],
        'مرسيدس': ['C-Class', 'E-Class', 'S-Class', 'GLC', 'GLE', 'GLS'],
        'أودي': ['A3', 'A4', 'A6', 'A8', 'Q3', 'Q5', 'Q7', 'Q8'],
        'لكزس': ['ES', 'IS', 'LS', 'RX', 'GX', 'LX', 'NX'],
        'إنفينيتي': ['Q50', 'Q60', 'Q70', 'QX50', 'QX60', 'QX80']
    };

    if (brandModels[brand]) {
        brandModels[brand].forEach(model => {
            const option = $('<option></option>').attr('value', model).text(model);
            modelSelect.find('option:last').before(option);
        });
    }

    // Restore previous value if it exists
    if (currentValue && modelSelect.find(`option[value="${currentValue}"]`).length) {
        modelSelect.val(currentValue);
    }
}

function validateCarForm() {
    let isValid = true;

    // Check chassis number uniqueness (this would need an AJAX call in real implementation)
    const chassisNumber = $('#chassis_number').val().trim();
    if (chassisNumber.length < 5) {
        showFieldError($('#chassis_number'), 'رقم الشاسيه يجب أن يكون 5 أحرف على الأقل');
        isValid = false;
    }

    // Check year
    const year = parseInt($('#year').val());
    const currentYear = new Date().getFullYear();
    if (year < 1990 || year > currentYear + 1) {
        showFieldError($('#year'), 'سنة الصنع غير صحيحة');
        isValid = false;
    }

    // Check car type
    const carType = $('#car_type').val();
    if (!carType) {
        showFieldError($('#car_type'), 'يجب اختيار نوع السيارة');
        isValid = false;
    }

    // Check mileage for used cars
    if (carType === 'used') {
        const mileage = parseInt($('#mileage').val());
        if (!mileage || mileage < 0) {
            showFieldError($('#mileage'), 'يجب إدخال المسافة المقطوعة للسيارات المستعملة');
            isValid = false;
        }
    }

    // Check price - allow zero and positive numbers
    const priceValue = $('#price').val().replace(/[^\d.]/g, '');
    const price = parseFloat(priceValue);
    if (!priceValue || isNaN(price) || price < 0) {
        showFieldError($('#price'), 'يجب إدخال سعر صحيح (يمكن أن يكون صفر أو أكبر)');
        isValid = false;
    }

    return isValid;
}

// Price Reset Functions
function resetPrice() {
    $('#price').val('').removeClass('is-invalid').focus();
    console.log('✅ Price reset');
}

function resetCostPrice() {
    $('#cost_price').val('').removeClass('is-invalid').focus();
    console.log('✅ Cost price reset');
}

function setSamplePrice() {
    $('#price').val('75000').trigger('blur');
    console.log('✅ Sample price set: 75000');
}

function setSampleCostPrice() {
    $('#cost_price').val('65000').trigger('blur');
    console.log('✅ Sample cost price set: 65000');
}

function fillSampleCarData() {
    if (confirm('هل تريد ملء النموذج ببيانات سيارة تجريبية؟ سيتم استبدال البيانات الحالية.')) {
        // Basic Information
        $('#name').val('كامري فل كامل');
        $('#brand').val('تويوتا').trigger('change');

        // Wait a bit for model options to update
        setTimeout(() => {
            $('#model').val('كامري');
            $('#year').val('2023');

            // Identification
            $('#chassis_number').val('JTDKN3DP0E3' + Math.floor(Math.random() * 100000));
            $('#plate_number').val('123456');

            // Specifications
            $('#color').val('أبيض لؤلؤي');
            $('#engine_size').val('2.5L');
            $('#fuel_type').val('بنزين');
            $('#transmission').val('أوتوماتيك');
            $('#mileage').val('15000');

            // Car Type and Condition
            $('#car_type').val('used').trigger('change');
            $('#condition_rating').val('excellent');

            // Pricing
            $('#price').val('75000').trigger('blur');
            $('#cost_price').val('65000').trigger('blur');

            // Description
            $('#description').val('سيارة كامري 2023 فل كامل، حالة ممتازة، صيانة دورية منتظمة، لون أبيض لؤلؤي، جلد بيج، فتحة سقف، شاشة تاتش، كاميرا خلفية، حساسات أمامية وخلفية.');
            $('#condition_notes').val('لا توجد خدوش أو أعطال، تم تغيير الزيت مؤخراً، الإطارات بحالة جيدة.');

            showNotification('تم ملء البيانات التجريبية بنجاح', 'success');
            console.log('✅ Sample car data filled');
        }, 100);
    }
}

function resetPricesOnly() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الأسعار؟')) {
        $('#price').val('').removeClass('is-invalid');
        $('#cost_price').val('').removeClass('is-invalid');
        $('#price').focus();

        // Show success message
        showNotification('تم إعادة تعيين الأسعار بنجاح', 'success');
        console.log('✅ All prices reset');
    }
}

function resetFormCompletely() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج بالكامل؟ سيتم فقدان جميع البيانات المدخلة.')) {
        // Reset the form
        $('#carForm')[0].reset();

        // Clear any validation errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Clear saved form data
        localStorage.removeItem('carForm');

        // Reset image preview
        $('.image-preview').empty();

        // Trigger car type change to reset dependent fields
        $('#car_type').trigger('change');

        // Focus on first field
        $('#brand').focus();

        // Show success message
        showNotification('تم إعادة تعيين النموذج بالكامل', 'info');
        console.log('✅ Form completely reset');
    }
}

// Helper function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = $(`
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    // Add to body
    $('body').append(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.alert('close');
    }, 3000);
}
</script>
{% endblock %}
