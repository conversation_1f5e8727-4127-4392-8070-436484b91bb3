{% extends "base.html" %}

{% block title %}اختبار تنسيق الأرقام - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        اختبار تنسيق الأرقام والعملة
                    </h3>
                </div>
                <div class="card-body">
                    
                    <!-- Number Formatting Examples -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>تنسيق العملة القطرية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">أمثلة العملة:</label>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ صغير:</span>
                                                <span class="currency">1500</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ متوسط:</span>
                                                <span class="currency">50000</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ كبير:</span>
                                                <span class="currency">125000</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ بعشري:</span>
                                                <span class="currency">1666.67</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">العملة بالأرقام العربية:</label>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ صغير:</span>
                                                <span class="currency arabic-digits short-form">1500</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ متوسط:</span>
                                                <span class="currency arabic-digits short-form">50000</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>مبلغ كبير:</span>
                                                <span class="currency arabic-digits short-form">125000</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>تنسيق الأرقام العامة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">أرقام بالفواصل:</label>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>رقم صغير:</span>
                                                <span class="number">1234</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>رقم متوسط:</span>
                                                <span class="number">123456</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>رقم كبير:</span>
                                                <span class="number">1234567</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>رقم بعشري:</span>
                                                <span class="number" data-decimals="2">1666.67</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">النسب المئوية:</label>
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>نسبة منخفضة:</span>
                                                <span class="percentage">15.5</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>نسبة متوسطة:</span>
                                                <span class="percentage">45.75</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>نسبة عالية:</span>
                                                <span class="percentage">89.25</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Input Testing -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>اختبار إدخال الأرقام</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="currencyInput" class="form-label">إدخال العملة</label>
                                                    <input type="text" class="form-control" id="currencyInput" 
                                                           data-format="currency" placeholder="أدخل مبلغ">
                                                    <div class="form-text">سيتم تنسيق المبلغ تلقائياً</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="numberInput" class="form-label">إدخال رقم</label>
                                                    <input type="text" class="form-control" id="numberInput" 
                                                           data-format="number" data-decimals="2" placeholder="أدخل رقم">
                                                    <div class="form-text">يدعم الأرقام العشرية</div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="phoneInput" class="form-label">رقم الهاتف القطري</label>
                                                    <input type="text" class="form-control" id="phoneInput" 
                                                           data-format="phone" placeholder="55123456" maxlength="8">
                                                    <div class="form-text">8 أرقام فقط</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="idInput" class="form-label">رقم الهوية القطرية</label>
                                                    <input type="text" class="form-control" id="idInput" 
                                                           data-format="qatar-id" placeholder="12345678901" maxlength="11">
                                                    <div class="form-text">11 رقم فقط</div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Currency Display Variants -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>أنواع عرض العملة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="currency-display">
                                                <div class="currency number-xl">75000</div>
                                                <small>عرض أساسي</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="currency-primary">
                                                <span class="currency">50000</span>
                                            </div>
                                            <small class="d-block text-center mt-2">نجاح</small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="currency-warning">
                                                <span class="currency">25000</span>
                                            </div>
                                            <small class="d-block text-center mt-2">تحذير</small>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="currency-danger">
                                                <span class="currency">10000</span>
                                            </div>
                                            <small class="d-block text-center mt-2">خطر</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Real Data Examples -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>أمثلة من البيانات الحقيقية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>العنصر</th>
                                                    <th>القيمة الأصلية</th>
                                                    <th>القيمة المُنسقة</th>
                                                    <th>بالأرقام العربية</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>قسط شهري</td>
                                                    <td class="text-monospace">1666.6666666666667</td>
                                                    <td><span class="currency">1666.67</span></td>
                                                    <td><span class="currency arabic-digits short-form">1666.67</span></td>
                                                </tr>
                                                <tr>
                                                    <td>سعر سيارة</td>
                                                    <td class="text-monospace">75000.0</td>
                                                    <td><span class="currency">75000</span></td>
                                                    <td><span class="currency arabic-digits short-form">75000</span></td>
                                                </tr>
                                                <tr>
                                                    <td>دفعة مقدمة</td>
                                                    <td class="text-monospace">15000.00</td>
                                                    <td><span class="currency">15000</span></td>
                                                    <td><span class="currency arabic-digits short-form">15000</span></td>
                                                </tr>
                                                <tr>
                                                    <td>رقم هاتف</td>
                                                    <td class="text-monospace">55123456</td>
                                                    <td><span class="number">55123456</span></td>
                                                    <td><span class="number arabic-digits">55123456</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- JavaScript Testing -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>اختبار JavaScript</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <button type="button" class="btn btn-primary mb-2" onclick="testNumberFormatting()">
                                                اختبار تنسيق الأرقام
                                            </button>
                                            <button type="button" class="btn btn-secondary mb-2" onclick="addDynamicNumbers()">
                                                إضافة أرقام ديناميكية
                                            </button>
                                            <button type="button" class="btn btn-info mb-2" onclick="validateInputs()">
                                                فحص المدخلات
                                            </button>
                                        </div>
                                        <div class="col-md-6">
                                            <div id="testResults" class="alert alert-info">
                                                <strong>نتائج الاختبار:</strong>
                                                <div id="testOutput">انقر على الأزرار لاختبار الوظائف</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testNumberFormatting() {
    const testNumbers = [1666.6666666666667, 50000, 125000.50, 1500.00, 999.99];
    let output = '<strong>اختبار تنسيق الأرقام:</strong><br>';
    
    testNumbers.forEach(num => {
        const formatted = window.numberFormatter.formatQatarCurrency(num);
        const arabicFormatted = window.numberFormatter.formatQatarCurrency(num, { useArabicDigits: true, shortForm: true });
        
        output += `${num} → ${formatted} → ${arabicFormatted}<br>`;
    });
    
    document.getElementById('testOutput').innerHTML = output;
}

function addDynamicNumbers() {
    const container = document.getElementById('testOutput');
    const dynamicNumbers = [
        { value: 25000, class: 'currency currency-primary' },
        { value: 75000, class: 'currency currency-secondary' },
        { value: 125000, class: 'currency currency-warning' }
    ];
    
    let html = '<strong>أرقام ديناميكية:</strong><br>';
    dynamicNumbers.forEach(item => {
        html += `<span class="${item.class}">${item.value}</span><br>`;
    });
    
    container.innerHTML = html;
    
    // Re-format the new numbers
    window.numberFormatter.formatAllNumbers();
}

function validateInputs() {
    const phoneInput = document.getElementById('phoneInput');
    const idInput = document.getElementById('idInput');
    
    let output = '<strong>فحص المدخلات:</strong><br>';
    
    if (phoneInput.value) {
        const isValidPhone = window.numberFormatter.validateQatarPhone(phoneInput.value);
        output += `رقم الهاتف: ${phoneInput.value} - ${isValidPhone ? '✅ صحيح' : '❌ خطأ'}<br>`;
    }
    
    if (idInput.value) {
        const isValidId = window.numberFormatter.validateQatarId(idInput.value);
        output += `رقم الهوية: ${idInput.value} - ${isValidId ? '✅ صحيح' : '❌ خطأ'}<br>`;
    }
    
    if (!phoneInput.value && !idInput.value) {
        output += 'أدخل رقم هاتف أو هوية للفحص';
    }
    
    document.getElementById('testOutput').innerHTML = output;
}

// Auto-format numbers when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for number formatter to load
    setTimeout(() => {
        if (window.numberFormatter) {
            window.numberFormatter.formatAllNumbers();
        }
    }, 500);
});
</script>
{% endblock %}
