from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, jsonify
from flask_login import login_required, current_user
from models import db, Sale, Car, Customer
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfutils
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase import pdfmetrics
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
import os
from datetime import datetime, date
import tempfile

contracts_bp = Blueprint('contracts', __name__)

def setup_arabic_font():
    """Setup Arabic font for PDF generation"""
    try:
        # Try to register multiple Arabic fonts
        fonts_registered = []

        # Primary font: Cairo Variable Font
        cairo_path = os.path.join('static', 'fonts', 'Cairo[slnt,wght].ttf')
        if os.path.exists(cairo_path):
            pdfmetrics.registerFont(TTFont('Cairo', cairo_path))
            fonts_registered.append('Cairo')

        # Secondary font: Noto Sans Arabic Variable Font
        noto_sans_path = os.path.join('static', 'fonts', 'NotoSansArabic[wdth,wght].ttf')
        if os.path.exists(noto_sans_path):
            pdfmetrics.registerFont(TTFont('NotoSansArabic', noto_sans_path))
            fonts_registered.append('NotoSansArabic')

        # Traditional font: Noto Naskh Arabic
        noto_naskh_path = os.path.join('static', 'fonts', 'NotoNaskhArabic-Regular.ttf')
        if os.path.exists(noto_naskh_path):
            pdfmetrics.registerFont(TTFont('NotoNaskhArabic', noto_naskh_path))
            fonts_registered.append('NotoNaskhArabic')

        # Classical font: Amiri
        amiri_path = os.path.join('static', 'fonts', 'Amiri-Regular.ttf')
        if os.path.exists(amiri_path):
            pdfmetrics.registerFont(TTFont('Amiri', amiri_path))
            fonts_registered.append('Amiri')

        # Fallback to old font
        old_noto_path = os.path.join('static', 'fonts', 'NotoSansArabic-Regular.ttf')
        if os.path.exists(old_noto_path) and 'NotoSansArabic' not in fonts_registered:
            pdfmetrics.registerFont(TTFont('NotoSansArabic', old_noto_path))
            fonts_registered.append('NotoSansArabic')

        # Return the best available font
        if 'Cairo' in fonts_registered:
            return 'Cairo'
        elif 'NotoSansArabic' in fonts_registered:
            return 'NotoSansArabic'
        elif 'NotoNaskhArabic' in fonts_registered:
            return 'NotoNaskhArabic'
        elif 'Amiri' in fonts_registered:
            return 'Amiri'
        else:
            # Fallback to default font
            return 'Helvetica'
    except Exception as e:
        print(f"Error setting up Arabic font: {e}")
        return 'Helvetica'

def prepare_arabic_text(text):
    """Prepare Arabic text for display"""
    if not text:
        return ""

    if not ARABIC_SUPPORT:
        return text

    try:
        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        # Apply bidirectional algorithm
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except:
        return text

@contracts_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Sale.query.join(Customer).join(Car)
    
    if search:
        query = query.filter(
            Customer.full_name.contains(search) |
            Car.name.contains(search) |
            Car.chassis_number.contains(search)
        )
    
    sales = query.order_by(Sale.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )
    
    return render_template('contracts/index.html', sales=sales, search=search)

@contracts_bp.route('/generate/<int:sale_id>')
@login_required
def generate_contract(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    return render_template('contracts/generate.html', sale=sale)

@contracts_bp.route('/pdf/<int:sale_id>')
@login_required
def generate_pdf(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
    temp_path = temp_file.name
    temp_file.close()
    
    try:
        # Create PDF
        c = canvas.Canvas(temp_path, pagesize=A4)
        width, height = A4
        
        # Setup fonts
        primary_font = setup_arabic_font()

        # Register additional fonts for different purposes
        title_font = primary_font
        body_font = primary_font

        # Title with larger, bold appearance
        c.setFont(title_font, 24)
        title = prepare_arabic_text("عقد بيع سيارة")
        c.drawRightString(width - 50, height - 60, title)

        # Subtitle
        c.setFont(title_font, 16)
        subtitle = prepare_arabic_text("معرض بوخليفة للسيارات - قطر")
        c.drawRightString(width - 50, height - 90, subtitle)

        # Company info with better formatting
        c.setFont(body_font, 12)
        company_address = prepare_arabic_text("الدوحة، دولة قطر")
        c.drawRightString(width - 50, height - 115, company_address)

        company_phone = prepare_arabic_text("هاتف: +974 4444 5555")
        c.drawRightString(width - 50, height - 135, company_phone)

        # Add decorative line
        c.setStrokeColorRGB(0.2, 0.2, 0.2)
        c.setLineWidth(2)
        c.line(50, height - 155, width - 50, height - 155)
        
        # Contract details
        y_position = height - 200
        
        # First party (Company)
        first_party_text = prepare_arabic_text("الطرف الأول: معرض بوخليفة للسيارات")
        c.drawRightString(width - 50, y_position, first_party_text)
        y_position -= 30
        
        # Second party (Customer)
        customer_text = prepare_arabic_text(f"الطرف الثاني: السيد/ {sale.customer.full_name}")
        c.drawRightString(width - 50, y_position, customer_text)
        y_position -= 20
        
        customer_id_text = prepare_arabic_text(f"رقم الهوية: {sale.customer.national_id}")
        c.drawRightString(width - 50, y_position, customer_id_text)
        y_position -= 20
        
        customer_phone_text = prepare_arabic_text(f"رقم الهاتف: {sale.customer.phone}")
        c.drawRightString(width - 50, y_position, customer_phone_text)
        y_position -= 40
        
        # Car details
        car_details_title = prepare_arabic_text("تفاصيل السيارة:")
        c.drawRightString(width - 50, y_position, car_details_title)
        y_position -= 25
        
        car_name_text = prepare_arabic_text(f"اسم السيارة: {sale.car.brand} {sale.car.model} {sale.car.year}")
        c.drawRightString(width - 50, y_position, car_name_text)
        y_position -= 20
        
        chassis_text = prepare_arabic_text(f"رقم الشاسيه: {sale.car.chassis_number}")
        c.drawRightString(width - 50, y_position, chassis_text)
        y_position -= 20
        
        if sale.car.plate_number:
            plate_text = prepare_arabic_text(f"رقم اللوحة: {sale.car.plate_number}")
            c.drawRightString(width - 50, y_position, plate_text)
            y_position -= 20
        
        # Sale details
        y_position -= 20
        sale_details_title = prepare_arabic_text("تفاصيل البيع:")
        c.drawRightString(width - 50, y_position, sale_details_title)
        y_position -= 25
        
        price_text = prepare_arabic_text(f"سعر البيع: {sale.sale_price:,.0f} ريال سعودي")
        c.drawRightString(width - 50, y_position, price_text)
        y_position -= 20
        
        if sale.sale_type == 'installment':
            down_payment_text = prepare_arabic_text(f"الدفعة المقدمة: {sale.down_payment:,.0f} ريال")
            c.drawRightString(width - 50, y_position, down_payment_text)
            y_position -= 20
            
            installment_text = prepare_arabic_text(f"قيمة القسط الشهري: {sale.installment_amount:,.0f} ريال")
            c.drawRightString(width - 50, y_position, installment_text)
            y_position -= 20
            
            installment_count_text = prepare_arabic_text(f"عدد الأقساط: {sale.installment_count} قسط")
            c.drawRightString(width - 50, y_position, installment_count_text)
            y_position -= 20
        
        # Date
        y_position -= 40
        date_text = prepare_arabic_text(f"تاريخ العقد: {sale.sale_date.strftime('%Y/%m/%d')}")
        c.drawRightString(width - 50, y_position, date_text)
        
        # Signatures
        y_position -= 80
        signature_title = prepare_arabic_text("التوقيعات:")
        c.drawRightString(width - 50, y_position, signature_title)
        y_position -= 40
        
        # First party signature
        first_party_sig = prepare_arabic_text("توقيع الطرف الأول")
        c.drawRightString(width - 50, y_position, first_party_sig)
        c.line(50, y_position - 10, 200, y_position - 10)
        
        # Second party signature
        second_party_sig = prepare_arabic_text("توقيع الطرف الثاني")
        c.drawRightString(width - 300, y_position, second_party_sig)
        c.line(width - 450, y_position - 10, width - 300, y_position - 10)
        
        c.save()
        
        # Update sale with contract path
        contract_filename = f"contract_{sale_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        contract_path = os.path.join('static/uploads/contracts', contract_filename)
        
        # Copy temp file to permanent location
        import shutil
        shutil.copy2(temp_path, contract_path)
        
        # Update database
        sale.contract_path = f'uploads/contracts/{contract_filename}'
        db.session.commit()
        
        return send_file(temp_path, 
                        as_attachment=True, 
                        download_name=f"contract_{sale.customer.full_name}_{sale.car.brand}_{sale.car.model}.pdf",
                        mimetype='application/pdf')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء العقد: {str(e)}', 'error')
        return redirect(url_for('contracts.generate_contract', sale_id=sale_id))
    
    finally:
        # Clean up temp file
        try:
            os.unlink(temp_path)
        except:
            pass

@contracts_bp.route('/word/<int:sale_id>')
@login_required
def generate_word(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
    temp_path = temp_file.name
    temp_file.close()
    
    try:
        # Create Word document
        doc = Document()

        # Set document direction to RTL
        sections = doc.sections
        for section in sections:
            section.page_height = Inches(11.69)  # A4 height
            section.page_width = Inches(8.27)    # A4 width
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)

        # Title with Arabic font
        title_paragraph = doc.add_paragraph()
        title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title_paragraph.add_run("عقد بيع سيارة")
        title_run.font.name = 'Cairo'
        title_run.font.size = Inches(0.3)
        title_run.bold = True

        # Subtitle
        subtitle_paragraph = doc.add_paragraph()
        subtitle_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle_paragraph.add_run("معرض بوخليفة للسيارات - قطر")
        subtitle_run.font.name = 'Cairo'
        subtitle_run.font.size = Inches(0.2)

        # Company info
        company_paragraph = doc.add_paragraph()
        company_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        company_run = company_paragraph.add_run("الدوحة، دولة قطر\nهاتف: +974 4444 5555")
        company_run.font.name = 'Cairo'
        company_run.font.size = Inches(0.15)

        # Add spacing
        doc.add_paragraph()
        
        # Add space
        doc.add_paragraph()
        
        # Contract content with proper formatting
        # Section 1: Agreement parties
        parties_paragraph = doc.add_paragraph()
        parties_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        parties_run = parties_paragraph.add_run("تم الاتفاق بين:")
        parties_run.font.name = 'Cairo'
        parties_run.font.size = Inches(0.18)
        parties_run.bold = True

        doc.add_paragraph()

        # First party
        first_party_paragraph = doc.add_paragraph()
        first_party_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        first_party_run = first_party_paragraph.add_run("الطرف الأول: معرض بوخليفة للسيارات")
        first_party_run.font.name = 'Cairo'
        first_party_run.font.size = Inches(0.16)

        # Second party
        second_party_paragraph = doc.add_paragraph()
        second_party_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        second_party_text = f"الطرف الثاني: السيد/ {sale.customer.full_name}\nرقم الهوية: {sale.customer.national_id}\nرقم الهاتف: {sale.customer.phone}"
        second_party_run = second_party_paragraph.add_run(second_party_text)
        second_party_run.font.name = 'Cairo'
        second_party_run.font.size = Inches(0.16)

        doc.add_paragraph()

        # Car details section
        car_header_paragraph = doc.add_paragraph()
        car_header_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        car_header_run = car_header_paragraph.add_run("تفاصيل السيارة:")
        car_header_run.font.name = 'Cairo'
        car_header_run.font.size = Inches(0.18)
        car_header_run.bold = True

        car_details_text = f"• اسم السيارة: {sale.car.brand} {sale.car.model} {sale.car.year}\n• رقم الشاسيه: {sale.car.chassis_number}"

        if sale.car.plate_number:
            car_details_text += f"\n• رقم اللوحة: {sale.car.plate_number}"

        car_details_paragraph = doc.add_paragraph()
        car_details_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        car_details_run = car_details_paragraph.add_run(car_details_text)
        car_details_run.font.name = 'Cairo'
        car_details_run.font.size = Inches(0.16)

        doc.add_paragraph()

        # Sale details section
        sale_header_paragraph = doc.add_paragraph()
        sale_header_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        sale_header_run = sale_header_paragraph.add_run("تفاصيل البيع:")
        sale_header_run.font.name = 'Cairo'
        sale_header_run.font.size = Inches(0.18)
        sale_header_run.bold = True

        sale_details_text = f"• سعر البيع: {sale.sale_price:,.0f} ريال قطري"

        if sale.sale_type == 'installment':
            sale_details_text += f"\n• الدفعة المقدمة: {sale.down_payment:,.0f} ريال"
            sale_details_text += f"\n• قيمة القسط الشهري: {sale.installment_amount:,.0f} ريال"
            sale_details_text += f"\n• عدد الأقساط: {sale.installment_count} قسط"
            if sale.first_installment_date:
                sale_details_text += f"\n• تاريخ أول قسط: {sale.first_installment_date.strftime('%Y/%m/%d')}"

        sale_details_text += f"\n• تاريخ العقد: {sale.sale_date.strftime('%Y/%m/%d')}"

        sale_details_paragraph = doc.add_paragraph()
        sale_details_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        sale_details_run = sale_details_paragraph.add_run(sale_details_text)
        sale_details_run.font.name = 'Cairo'
        sale_details_run.font.size = Inches(0.16)

        doc.add_paragraph()

        # Terms and conditions
        terms_header_paragraph = doc.add_paragraph()
        terms_header_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        terms_header_run = terms_header_paragraph.add_run("الشروط والأحكام:")
        terms_header_run.font.name = 'Cairo'
        terms_header_run.font.size = Inches(0.18)
        terms_header_run.bold = True

        terms_text = """1. يتعهد الطرف الثاني بدفع المبلغ المتفق عليه في المواعيد المحددة
2. في حالة التأخير عن السداد، يحق للطرف الأول اتخاذ الإجراءات القانونية اللازمة
3. يتم تسليم السيارة بعد استكمال جميع الإجراءات القانونية
4. هذا العقد ملزم للطرفين ولا يمكن تعديله إلا بموافقة الطرفين
5. يخضع هذا العقد لقوانين دولة قطر"""

        terms_paragraph = doc.add_paragraph()
        terms_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        terms_run = terms_paragraph.add_run(terms_text)
        terms_run.font.name = 'Cairo'
        terms_run.font.size = Inches(0.16)

        # Add spacing before signatures
        doc.add_paragraph()
        doc.add_paragraph()

        # Signatures section
        signatures_paragraph = doc.add_paragraph()
        signatures_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        signatures_run = signatures_paragraph.add_run("التوقيعات")
        signatures_run.font.name = 'Cairo'
        signatures_run.font.size = Inches(0.18)
        signatures_run.bold = True

        doc.add_paragraph()

        # Signature lines
        sig_lines_paragraph = doc.add_paragraph()
        sig_lines_paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        sig_lines_run = sig_lines_paragraph.add_run("الطرف الأول: _________________     الطرف الثاني: _________________")
        sig_lines_run.font.name = 'Cairo'
        sig_lines_run.font.size = Inches(0.16)

        doc.add_paragraph()

        date_lines_paragraph = doc.add_paragraph()
        date_lines_paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        date_lines_run = date_lines_paragraph.add_run("التاريخ: _______________           التاريخ: _______________")
        date_lines_run.font.name = 'Cairo'
        date_lines_run.font.size = Inches(0.16)
        
        # Save document
        doc.save(temp_path)
        
        # Update sale with contract path
        contract_filename = f"contract_{sale_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        contract_path = os.path.join('static/uploads/contracts', contract_filename)
        
        # Copy temp file to permanent location
        import shutil
        shutil.copy2(temp_path, contract_path)
        
        # Update database
        sale.contract_path = f'uploads/contracts/{contract_filename}'
        db.session.commit()
        
        return send_file(temp_path, 
                        as_attachment=True, 
                        download_name=f"contract_{sale.customer.full_name}_{sale.car.brand}_{sale.car.model}.docx",
                        mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء العقد: {str(e)}', 'error')
        return redirect(url_for('contracts.generate_contract', sale_id=sale_id))
    
    finally:
        # Clean up temp file
        try:
            os.unlink(temp_path)
        except:
            pass

@contracts_bp.route('/download/<int:sale_id>')
@login_required
def download_contract(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    if not sale.contract_path:
        flash('لم يتم إنشاء العقد بعد', 'error')
        return redirect(url_for('contracts.generate_contract', sale_id=sale_id))
    
    contract_path = os.path.join('static', sale.contract_path)
    
    if not os.path.exists(contract_path):
        flash('ملف العقد غير موجود', 'error')
        return redirect(url_for('contracts.generate_contract', sale_id=sale_id))
    
    return send_file(contract_path, as_attachment=True)

@contracts_bp.route('/preview/<int:sale_id>')
@login_required
def preview_contract(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    return render_template('contracts/preview.html', sale=sale)
