{% extends "base.html" %}

{% block title %}{{ car.brand }} {{ car.model }} - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ car.brand }} {{ car.model }} {{ car.year }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('edit_cars') %}
            <a href="{{ url_for('cars.edit', car_id=car.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            {% endif %}
            
            {% if car.status == 'available' and current_user.has_permission('create_sales') %}
            <a href="{{ url_for('sales.add') }}?car_id={{ car.id }}" class="btn btn-success">
                <i class="fas fa-handshake me-2"></i>
                بيع السيارة
            </a>
            {% endif %}
        </div>
        <a href="{{ url_for('cars.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- Car Images -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">صور السيارة</h5>
            </div>
            <div class="card-body">
                {% if images %}
                <div id="carCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        {% for image in images %}
                        <div class="carousel-item {{ 'active' if loop.first }}">
                            <img src="{{ url_for('static', filename=image) }}" 
                                 class="d-block w-100" style="height: 300px; object-fit: cover;" 
                                 alt="صورة السيارة {{ loop.index }}">
                        </div>
                        {% endfor %}
                    </div>
                    
                    {% if images|length > 1 %}
                    <button class="carousel-control-prev" type="button" data-bs-target="#carCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#carCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                    
                    <div class="carousel-indicators">
                        {% for image in images %}
                        <button type="button" data-bs-target="#carCarousel" data-bs-slide-to="{{ loop.index0 }}" 
                                {{ 'class="active"' if loop.first }}></button>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <!-- Thumbnail Gallery -->
                {% if images|length > 1 %}
                <div class="row mt-3">
                    {% for image in images %}
                    <div class="col-3">
                        <img src="{{ url_for('static', filename=image) }}" 
                             class="img-thumbnail" style="height: 80px; object-fit: cover; cursor: pointer;"
                             onclick="goToSlide({{ loop.index0 }})" alt="صورة مصغرة {{ loop.index }}">
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-car fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد صور للسيارة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Car Details -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل السيارة</h5>
                <span class="badge 
                    {% if car.status == 'available' %}bg-success
                    {% elif car.status == 'sold' %}bg-danger
                    {% elif car.status == 'reserved' %}bg-warning text-dark
                    {% else %}bg-secondary{% endif %} fs-6">
                    {% if car.status == 'available' %}متاحة للبيع
                    {% elif car.status == 'sold' %}مباعة
                    {% elif car.status == 'reserved' %}محجوزة
                    {% else %}في الصيانة{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم السيارة:</strong></td>
                                <td>{{ car.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الماركة:</strong></td>
                                <td>{{ car.brand }}</td>
                            </tr>
                            <tr>
                                <td><strong>الموديل:</strong></td>
                                <td>{{ car.model }}</td>
                            </tr>
                            <tr>
                                <td><strong>سنة الصنع:</strong></td>
                                <td>{{ car.year }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الشاسيه:</strong></td>
                                <td><code>{{ car.chassis_number }}</code></td>
                            </tr>
                            {% if car.plate_number %}
                            <tr>
                                <td><strong>رقم اللوحة:</strong></td>
                                <td><span class="badge bg-info">{{ car.plate_number }}</span></td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>نوع السيارة:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if car.car_type == 'new' else 'primary' }}">
                                        {{ 'جديدة' if car.car_type == 'new' else 'مستعملة' }}
                                    </span>
                                </td>
                            </tr>
                            {% if car.condition_rating %}
                            <tr>
                                <td><strong>تقييم الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-{% if car.condition_rating == 'excellent' %}success{% elif car.condition_rating == 'very_good' %}info{% elif car.condition_rating == 'good' %}primary{% elif car.condition_rating == 'fair' %}warning{% else %}danger{% endif %}">
                                        {% if car.condition_rating == 'excellent' %}ممتاز
                                        {% elif car.condition_rating == 'very_good' %}جيد جداً
                                        {% elif car.condition_rating == 'good' %}جيد
                                        {% elif car.condition_rating == 'fair' %}مقبول
                                        {% elif car.condition_rating == 'poor' %}ضعيف
                                        {% else %}{{ car.condition_rating }}{% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if car.color %}
                            <tr>
                                <td><strong>اللون:</strong></td>
                                <td>{{ car.color }}</td>
                            </tr>
                            {% endif %}
                            {% if car.engine_size %}
                            <tr>
                                <td><strong>حجم المحرك:</strong></td>
                                <td>{{ car.engine_size }}</td>
                            </tr>
                            {% endif %}
                            {% if car.fuel_type %}
                            <tr>
                                <td><strong>نوع الوقود:</strong></td>
                                <td>{{ car.fuel_type }}</td>
                            </tr>
                            {% endif %}
                            {% if car.transmission %}
                            <tr>
                                <td><strong>ناقل الحركة:</strong></td>
                                <td>{{ car.transmission }}</td>
                            </tr>
                            {% endif %}
                            {% if car.mileage %}
                            <tr>
                                <td><strong>المسافة المقطوعة:</strong></td>
                                <td>{{ car.mileage|number_format }} كم</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>تاريخ الإضافة:</strong></td>
                                <td>{{ car.created_at|arabic_date }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Pricing -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-success text-center">
                            <h4 class="mb-0">
                                <i class="fas fa-tag me-2"></i>
                                سعر البيع: <strong>{{ car.price|currency }}</strong>
                            </h4>
                            {% if car.cost_price %}
                            <small class="text-muted">سعر التكلفة: {{ car.cost_price|currency }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Description -->
                {% if car.description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>الوصف:</h6>
                        <p class="text-muted">{{ car.description }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Condition Notes -->
                {% if car.condition_notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>ملاحظات الحالة:</h6>
                        <div class="alert alert-light">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ car.condition_notes }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sales History -->
{% if sales %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تاريخ المبيعات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>نوع البيع</th>
                                <th>السعر</th>
                                <th>تاريخ البيع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('customers.view', customer_id=sale.customer.id) }}" class="text-decoration-none">
                                        {{ sale.customer.full_name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                        {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                                    </span>
                                </td>
                                <td>{{ sale.sale_price|currency }}</td>
                                <td>{{ sale.sale_date|arabic_date }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.status == 'active' else 'secondary' }}">
                                        {{ 'نشط' if sale.status == 'active' else sale.status }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('sales.view', sale_id=sale.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                        عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function goToSlide(index) {
    const carousel = new bootstrap.Carousel(document.getElementById('carCarousel'));
    carousel.to(index);
}

$(document).ready(function() {
    // Add lightbox effect to images
    $('.carousel-item img').on('click', function() {
        const src = $(this).attr('src');
        const modal = `
            <div class="modal fade" id="imageModal" tabindex="-1">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-body p-0">
                            <img src="${src}" class="img-fluid w-100" alt="صورة السيارة">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modal);
        $('#imageModal').modal('show');
        $('#imageModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    });
});
</script>
{% endblock %}
