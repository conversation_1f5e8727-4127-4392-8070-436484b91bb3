/**
 * Font Manager for Arabic Car Dealership System
 * Handles font loading, optimization, and Arabic text formatting
 */

class FontManager {
    constructor() {
        this.fonts = [
            { family: 'Cairo', weight: '400' },
            { family: 'Noto Sans Arabic', weight: '400' },
            { family: 'Noto Naskh Arabic', weight: '400' },
            { family: 'Amiri', weight: '400' }
        ];
        
        this.loadingClass = 'font-loading';
        this.loadedClass = 'fonts-loaded';
        this.fallbackTimeout = 3000;
        
        this.init();
    }
    
    /**
     * Initialize font manager
     */
    init() {
        this.addLoadingClass();
        this.loadFonts();
        this.setupFallback();
        this.initializeFormatters();
    }
    
    /**
     * Add loading class to document
     */
    addLoadingClass() {
        document.documentElement.classList.add(this.loadingClass);
    }
    
    /**
     * Load fonts using Font Loading API
     */
    async loadFonts() {
        if (!('fonts' in document)) {
            this.fallbackToTimeout();
            return;
        }
        
        try {
            const fontPromises = this.fonts.map(font => 
                document.fonts.load(`${font.weight} 1rem "${font.family}"`)
            );
            
            await Promise.all(fontPromises);
            this.onFontsLoaded();
        } catch (error) {
            console.warn('Font loading failed:', error);
            this.fallbackToTimeout();
        }
    }
    
    /**
     * Setup fallback timeout
     */
    setupFallback() {
        setTimeout(() => {
            if (document.documentElement.classList.contains(this.loadingClass)) {
                console.warn('Font loading timeout, using fallback');
                this.onFontsLoaded();
            }
        }, this.fallbackTimeout);
    }
    
    /**
     * Fallback to timeout-based loading
     */
    fallbackToTimeout() {
        setTimeout(() => {
            this.onFontsLoaded();
        }, 1000);
    }
    
    /**
     * Handle fonts loaded event
     */
    onFontsLoaded() {
        document.documentElement.classList.remove(this.loadingClass);
        document.documentElement.classList.add(this.loadedClass);
        
        // Trigger custom event
        document.dispatchEvent(new CustomEvent('fontsLoaded'));
        
        // Apply text improvements
        this.applyTextImprovements();
    }
    
    /**
     * Apply text improvements after fonts load
     */
    applyTextImprovements() {
        // Add Arabic text class to elements with Arabic content
        this.detectAndMarkArabicText();
        
        // Format numbers and currency
        this.formatNumbers();
        this.formatCurrency();
    }
    
    /**
     * Detect and mark Arabic text
     */
    detectAndMarkArabicText() {
        const arabicRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        const textElements = document.querySelectorAll('p, span, div, td, th, label');
        
        textElements.forEach(element => {
            if (arabicRegex.test(element.textContent)) {
                element.classList.add('text-arabic-modern');
            }
        });
    }
    
    /**
     * Format numbers for Arabic locale
     */
    formatNumbers() {
        const numberElements = document.querySelectorAll('.number, .arabic-numbers');
        
        numberElements.forEach(element => {
            const number = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
            if (!isNaN(number)) {
                element.textContent = this.formatArabicNumber(number);
            }
        });
    }
    
    /**
     * Format currency for Qatar
     */
    formatCurrency() {
        const currencyElements = document.querySelectorAll('.currency');
        
        currencyElements.forEach(element => {
            const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
            if (!isNaN(amount)) {
                element.textContent = this.formatQatarCurrency(amount);
            }
        });
    }
    
    /**
     * Format number for Arabic locale
     */
    formatArabicNumber(number) {
        return new Intl.NumberFormat('ar-QA').format(number);
    }
    
    /**
     * Format currency for Qatar
     */
    formatQatarCurrency(amount) {
        return new Intl.NumberFormat('ar-QA', {
            style: 'currency',
            currency: 'QAR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    /**
     * Initialize number and currency formatters
     */
    initializeFormatters() {
        // Auto-format currency inputs
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('currency-input')) {
                this.formatCurrencyInput(e.target);
            }
        });
        
        // Auto-format number inputs
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('number-input')) {
                this.formatNumberInput(e.target);
            }
        });
    }
    
    /**
     * Format currency input field
     */
    formatCurrencyInput(input) {
        let value = input.value.replace(/[^\d.-]/g, '');
        if (value) {
            const number = parseFloat(value);
            if (!isNaN(number)) {
                // Store raw value in data attribute
                input.dataset.rawValue = number;
                
                // Format display value
                const formatted = this.formatQatarCurrency(number);
                input.value = formatted;
            }
        }
    }
    
    /**
     * Format number input field
     */
    formatNumberInput(input) {
        let value = input.value.replace(/[^\d.-]/g, '');
        if (value) {
            const number = parseFloat(value);
            if (!isNaN(number)) {
                input.value = this.formatArabicNumber(number);
            }
        }
    }
    
    /**
     * Get font loading status
     */
    isLoaded() {
        return document.documentElement.classList.contains(this.loadedClass);
    }
    
    /**
     * Check if a specific font is available
     */
    isFontAvailable(fontFamily) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        // Test with a character that should look different in the target font
        const testString = 'مرحبا';
        const testSize = '72px';
        
        // Measure with fallback font
        context.font = `${testSize} monospace`;
        const fallbackWidth = context.measureText(testString).width;
        
        // Measure with target font
        context.font = `${testSize} ${fontFamily}, monospace`;
        const targetWidth = context.measureText(testString).width;
        
        return fallbackWidth !== targetWidth;
    }
    
    /**
     * Preload additional font weights
     */
    preloadFontWeights(fontFamily, weights = ['300', '500', '600', '700']) {
        weights.forEach(weight => {
            if ('fonts' in document) {
                document.fonts.load(`${weight} 1rem "${fontFamily}"`);
            }
        });
    }
    
    /**
     * Apply font to specific elements
     */
    applyFontToElements(selector, fontFamily) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.fontFamily = fontFamily;
        });
    }
    
    /**
     * Get optimal font for content type
     */
    getOptimalFont(contentType) {
        const fontMap = {
            'title': 'Cairo, "Noto Sans Arabic", sans-serif',
            'body': 'Cairo, "Noto Sans Arabic", sans-serif',
            'traditional': '"Noto Naskh Arabic", Amiri, serif',
            'modern': 'Cairo, "Noto Sans Arabic", sans-serif',
            'numbers': 'Cairo, "Noto Sans Arabic", monospace'
        };
        
        return fontMap[contentType] || fontMap['body'];
    }
}

// Initialize font manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.fontManager = new FontManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontManager;
}
