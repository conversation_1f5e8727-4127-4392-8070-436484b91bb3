{% extends "base.html" %}

{% block title %}اختبار حقول الأسعار - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        اختبار حقول الأسعار
                    </h3>
                </div>
                <div class="card-body">
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار</h6>
                        <p class="mb-0">
                            هذه الصفحة لاختبار عمل حقول الأسعار والتأكد من أن InputValidator يعمل بشكل صحيح.
                            جرب إدخال أرقام مختلفة في الحقول أدناه.
                        </p>
                    </div>

                    <form id="priceTestForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="test_price1" class="form-label">سعر البيع (مطلوب)</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control currency-input" id="test_price1"
                                               name="test_price1" data-format="currency" required
                                               placeholder="أدخل السعر" autocomplete="off">
                                        <span class="input-group-text">ريال قطري</span>
                                    </div>
                                    <div class="form-text">مثال: 50000 أو 50,000</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="test_price2" class="form-label">سعر التكلفة (اختياري)</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control currency-input" id="test_price2"
                                               name="test_price2" data-format="currency"
                                               placeholder="أدخل سعر التكلفة" autocomplete="off">
                                        <span class="input-group-text">ريال قطري</span>
                                    </div>
                                    <div class="form-text">اختياري - للتقارير المالية</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="test_down_payment" class="form-label">الدفعة المقدمة</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control currency-input" id="test_down_payment"
                                               name="test_down_payment" data-format="currency"
                                               placeholder="أدخل الدفعة المقدمة" autocomplete="off">
                                        <span class="input-group-text">ريال قطري</span>
                                    </div>
                                    <div class="form-text">سيتم خصمها من إجمالي المبلغ</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="test_installment" class="form-label">قيمة القسط</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control currency-input" id="test_installment"
                                               name="test_installment" data-format="currency"
                                               placeholder="قيمة القسط الشهري" autocomplete="off">
                                        <span class="input-group-text">ريال قطري</span>
                                    </div>
                                    <div class="form-text">القسط الشهري المطلوب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <button type="button" class="btn btn-primary" onclick="testValidation()">
                                        <i class="fas fa-check me-2"></i>
                                        اختبار التحقق
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-2" onclick="clearForm()">
                                        <i class="fas fa-eraser me-2"></i>
                                        مسح الحقول
                                    </button>
                                    <button type="button" class="btn btn-info ms-2" onclick="fillSampleData()">
                                        <i class="fas fa-magic me-2"></i>
                                        بيانات تجريبية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Test Results -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">نتائج الاختبار</h6>
                                </div>
                                <div class="card-body">
                                    <div id="testResults">
                                        <p class="text-muted">اضغط "اختبار التحقق" لرؤية النتائج</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Info -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">معلومات التشخيص</h6>
                                </div>
                                <div class="card-body">
                                    <div id="debugInfo">
                                        <p><strong>InputValidator متاح:</strong> <span id="validatorStatus">جاري التحقق...</span></p>
                                        <p><strong>Simple Price Handler متاح:</strong> <span id="priceHandlerStatus">جاري التحقق...</span></p>
                                        <p><strong>jQuery متاح:</strong> <span id="jqueryStatus">جاري التحقق...</span></p>
                                        <p><strong>عدد حقول العملة:</strong> <span id="currencyFieldsCount">جاري العد...</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update debug info
    updateDebugInfo();
    
    // Ensure InputValidator is initialized
    if (window.inputValidator) {
        window.inputValidator.setupCurrencyInputs();
        console.log('✅ InputValidator initialized for price test');
    } else {
        console.log('⚠️ InputValidator not available, using fallback');
        // Fallback currency formatting
        $('.currency-input').on('input', function() {
            let value = $(this).val().replace(/[^\d.]/g, '');
            if (value) {
                $(this).val(value);
            }
        });
    }
});

function updateDebugInfo() {
    // Check InputValidator
    $('#validatorStatus').text(window.inputValidator ? '✅ متاح' : '❌ غير متاح');
    $('#validatorStatus').removeClass('text-success text-danger')
                         .addClass(window.inputValidator ? 'text-success' : 'text-danger');

    // Check Simple Price Handler
    $('#priceHandlerStatus').text(window.simplePriceHandler ? '✅ متاح' : '❌ غير متاح');
    $('#priceHandlerStatus').removeClass('text-success text-danger')
                            .addClass(window.simplePriceHandler ? 'text-success' : 'text-danger');

    // Check jQuery
    $('#jqueryStatus').text(window.jQuery ? '✅ متاح' : '❌ غير متاح');
    $('#jqueryStatus').removeClass('text-success text-danger')
                      .addClass(window.jQuery ? 'text-success' : 'text-danger');

    // Count currency fields
    const currencyFields = $('.currency-input').length;
    $('#currencyFieldsCount').text(currencyFields + ' حقل');
}

function testValidation() {
    const results = [];
    
    // Test each currency field
    $('.currency-input').each(function() {
        const field = $(this);
        const value = field.val();
        const cleanValue = value.replace(/[^\d.]/g, '');
        const numericValue = parseFloat(cleanValue);
        
        results.push({
            label: field.prev('label').text() || field.attr('placeholder'),
            originalValue: value,
            cleanValue: cleanValue,
            numericValue: isNaN(numericValue) ? 'غير صحيح' : numericValue,
            isValid: !isNaN(numericValue) && numericValue > 0
        });
    });
    
    // Display results
    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>الحقل</th><th>القيمة المدخلة</th><th>القيمة المنظفة</th><th>القيمة الرقمية</th><th>صحيح؟</th></tr></thead><tbody>';
    
    results.forEach(result => {
        const statusClass = result.isValid ? 'text-success' : 'text-danger';
        const statusIcon = result.isValid ? '✅' : '❌';
        
        html += `<tr>
            <td>${result.label}</td>
            <td>${result.originalValue || '-'}</td>
            <td>${result.cleanValue || '-'}</td>
            <td>${result.numericValue}</td>
            <td class="${statusClass}">${statusIcon}</td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    
    $('#testResults').html(html);
}

function clearForm() {
    $('#priceTestForm')[0].reset();
    $('#testResults').html('<p class="text-muted">اضغط "اختبار التحقق" لرؤية النتائج</p>');
}

function fillSampleData() {
    $('#test_price1').val('75000').trigger('blur');
    $('#test_price2').val('65000').trigger('blur');
    $('#test_down_payment').val('15000').trigger('blur');
    $('#test_installment').val('2500').trigger('blur');
    
    setTimeout(() => {
        testValidation();
    }, 500);
}
</script>
{% endblock %}
