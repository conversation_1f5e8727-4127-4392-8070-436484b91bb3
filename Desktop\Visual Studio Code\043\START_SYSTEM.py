#!/usr/bin/env python3
"""
🚀 تشغيل نظام معرض السيارات - النسخة النهائية المحسنة
Car Dealership System - Final Enhanced Version
"""

import os
import sys
import webbrowser
import time
from datetime import datetime

def print_banner():
    """Print system banner"""
    print("=" * 60)
    print("🚗 نظام إدارة معرض السيارات - النسخة النهائية")
    print("   Car Dealership Management System - Final Version")
    print("=" * 60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 الحالة: جميع الإصلاحات مطبقة")
    print("✅ التخطيط: RTL مثالي")
    print("✅ النصوص: واضحة ومقروءة")
    print("✅ الأيقونات: تعمل بشكل صحيح")
    print("✅ التصميم: احترافي ومتجاوب")
    print("=" * 60)

def check_system():
    """Check if system is ready"""
    print("🔍 فحص النظام...")
    
    # Check if app.py exists
    if not os.path.exists('app.py'):
        print("❌ خطأ: ملف app.py غير موجود")
        print("💡 تأكد من أنك في المجلد الصحيح")
        return False
    
    # Check if templates exist
    if not os.path.exists('templates'):
        print("❌ خطأ: مجلد templates غير موجود")
        return False
    
    # Check if static exists
    if not os.path.exists('static'):
        print("❌ خطأ: مجلد static غير موجود")
        return False
    
    # Check if CSS fixes exist
    css_files = [
        'static/css/complete-fix.css',
        'static/css/ultimate-fix.css',
        'static/css/font-fix.css'
    ]
    
    missing_files = []
    for css_file in css_files:
        if not os.path.exists(css_file):
            missing_files.append(css_file)
    
    if missing_files:
        print("⚠️ تحذير: بعض ملفات CSS مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("💡 سيتم تطبيق الإصلاحات تلقائياً...")
        return "partial"
    
    print("✅ النظام جاهز للتشغيل")
    return True

def apply_fixes():
    """Apply all fixes automatically"""
    print("🔧 تطبيق الإصلاحات...")
    
    try:
        # Run complete fix
        os.system('python complete_fix.py')
        print("✅ تم تطبيق الإصلاح الشامل")
        return True
    except Exception as e:
        print(f"❌ خطأ في تطبيق الإصلاحات: {str(e)}")
        return False

def start_system():
    """Start the system"""
    print("🚀 بدء تشغيل النظام...")
    
    # Set environment variables
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'true'
    os.environ['FORCE_RTL'] = 'true'
    os.environ['ULTIMATE_FIX'] = 'true'
    
    try:
        from app import create_app
        app = create_app()
        
        print("✅ تم تحميل النظام بنجاح")
        print("\n🌐 النظام متاح على الروابط التالية:")
        print("   • الصفحة الرئيسية: http://localhost:5000")
        print("   • اختبار التخطيط: http://localhost:5000/layout-test")
        print("   • اختبار النصوص: http://localhost:5000/text-test")
        print("   • الإصلاح الطارئ: http://localhost:5000/emergency-fix")
        print("   • التشخيص: http://localhost:5000/debug-layout")
        print("   • اختبار الأرقام: http://localhost:5000/arabic-numbers-test")
        
        print("\n👤 بيانات الدخول:")
        print("   • المستخدم: admin")
        print("   • كلمة المرور: admin123")
        
        print("\n🎯 الميزات المتاحة:")
        print("   ✅ إدارة السيارات (مع الحالة والجودة واللون)")
        print("   ✅ إدارة العملاء (معايير قطر)")
        print("   ✅ أرقام السيارات القطرية")
        print("   ✅ نظام العقود والتقارير")
        print("   ✅ الأرقام العربية")
        print("   ✅ واتساب تيمبليتس")
        print("   ✅ نظام الإشعارات")
        
        print("\n🎨 التصميم:")
        print("   ✅ تخطيط RTL مثالي")
        print("   ✅ شريط جانبي متجاوب")
        print("   ✅ نصوص واضحة ومقروءة")
        print("   ✅ أيقونات Font Awesome")
        print("   ✅ ألوان احترافية")
        print("   ✅ تصميم متجاوب")
        
        print("\n🛑 للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        
        # Auto-open browser after 2 seconds
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                pass
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the app
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بنجاح")
        print("💾 جميع البيانات محفوظة")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n💡 جرب الحلول التالية:")
        print("   1. python complete_fix.py")
        print("   2. python START_SYSTEM.py")
        print("   3. تأكد من تثبيت Flask: pip install flask")

def main():
    """Main function"""
    print_banner()
    
    # Check system status
    system_status = check_system()
    
    if system_status == False:
        print("❌ النظام غير جاهز للتشغيل")
        sys.exit(1)
    elif system_status == "partial":
        if not apply_fixes():
            print("❌ فشل في تطبيق الإصلاحات")
            sys.exit(1)
    
    # Start the system
    start_system()

if __name__ == '__main__':
    main()
