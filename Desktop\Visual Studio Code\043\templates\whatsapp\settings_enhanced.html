{% extends "base.html" %}

{% block title %}إعدادات الواتساب المحسنة - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fab fa-whatsapp me-2"></i>
                        إعدادات الواتساب المحسنة
                    </h3>
                    <a href="{{ url_for('whatsapp.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للواتساب
                    </a>
                </div>
                <div class="card-body">
                    
                    <!-- Current Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-{% if settings.demo_mode %}warning{% else %}success{% endif %}">
                                <div class="card-body text-center">
                                    <i class="fab fa-whatsapp fa-3x mb-3 text-{% if settings.demo_mode %}warning{% else %}success{% endif %}"></i>
                                    <h5>حالة النظام</h5>
                                    {% if settings.demo_mode %}
                                        <span class="badge bg-warning fs-6">وضع تجريبي</span>
                                        <p class="mt-2 text-muted">النظام يعمل في الوضع التجريبي - لن يتم إرسال رسائل فعلية</p>
                                    {% else %}
                                        <span class="badge bg-success fs-6">متصل</span>
                                        <p class="mt-2 text-muted">النظام متصل ويمكن إرسال الرسائل</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs fa-3x mb-3 text-info"></i>
                                    <h5>Twilio SDK</h5>
                                    {% if settings.twilio_available %}
                                        <span class="badge bg-success fs-6">مثبت</span>
                                        <p class="mt-2 text-muted">مكتبة Twilio متاحة للاستخدام</p>
                                    {% else %}
                                        <span class="badge bg-danger fs-6">غير مثبت</span>
                                        <p class="mt-2 text-muted">يرجى تثبيت مكتبة Twilio: pip install twilio</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Form -->
                    <form method="POST">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    إعدادات الإرسال
                                </h5>
                            </div>
                        </div>

                        <!-- Demo Mode -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-warning">
                                    <div class="card-body">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="demo_mode" name="demo_mode" 
                                                   {{ 'checked' if settings.demo_mode }}>
                                            <label class="form-check-label" for="demo_mode">
                                                <strong>الوضع التجريبي</strong>
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            في الوضع التجريبي، سيتم محاكاة إرسال الرسائل دون إرسال فعلي. مفيد للاختبار والتدريب.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Twilio Settings -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">
                                    <i class="fas fa-cloud me-2"></i>
                                    إعدادات Twilio
                                </h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="twilio_sid" class="form-label">Account SID</label>
                                <input type="text" class="form-control" id="twilio_sid" name="twilio_sid" 
                                       value="{{ settings.twilio_sid }}" placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                                <div class="form-text">معرف حساب Twilio الخاص بك</div>
                            </div>
                            <div class="col-md-6">
                                <label for="twilio_token" class="form-label">Auth Token</label>
                                <input type="password" class="form-control" id="twilio_token" name="twilio_token" 
                                       value="{{ settings.twilio_token }}" placeholder="••••••••••••••••••••••••••••••••">
                                <div class="form-text">رمز المصادقة الخاص بحساب Twilio</div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="twilio_number" class="form-label">رقم الواتساب</label>
                                <input type="text" class="form-control" id="twilio_number" name="twilio_number" 
                                       value="{{ settings.twilio_number }}" placeholder="whatsapp:+14155238886">
                                <div class="form-text">رقم الواتساب المسجل في Twilio</div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                                    <i class="fas fa-plug me-2"></i>
                                    اختبار الاتصال
                                </button>
                            </div>
                        </div>

                        <!-- Help Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-question-circle me-2"></i>كيفية الحصول على إعدادات Twilio:</h6>
                                    <ol class="mb-0">
                                        <li>قم بإنشاء حساب في <a href="https://www.twilio.com" target="_blank">Twilio.com</a></li>
                                        <li>اذهب إلى Console Dashboard</li>
                                        <li>انسخ Account SID و Auth Token</li>
                                        <li>قم بإعداد WhatsApp Sandbox أو احصل على رقم WhatsApp معتمد</li>
                                        <li>أدخل الإعدادات هنا واحفظها</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ الإعدادات
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-success" onclick="sendTestMessage()">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            إرسال رسالة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Message Modal -->
<div class="modal fade" id="testMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة تجريبية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="testPhone" class="form-label">رقم الهاتف</label>
                    <input type="text" class="form-control" id="testPhone" placeholder="+974xxxxxxxx">
                </div>
                <div class="mb-3">
                    <label for="testMessage" class="form-label">نص الرسالة</label>
                    <textarea class="form-control" id="testMessage" rows="3">مرحباً! هذه رسالة تجريبية من معرض بوخليفة للسيارات.</textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="sendTest()">إرسال</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testConnection() {
    alert('اختبار الاتصال... (ميزة قيد التطوير)');
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
        document.querySelector('form').reset();
    }
}

function sendTestMessage() {
    const modal = new bootstrap.Modal(document.getElementById('testMessageModal'));
    modal.show();
}

function sendTest() {
    const phone = document.getElementById('testPhone').value;
    const message = document.getElementById('testMessage').value;
    
    if (!phone || !message) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    alert('تم إرسال الرسالة التجريبية بنجاح!');
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('testMessageModal'));
    modal.hide();
}

// Toggle Twilio fields based on demo mode
document.getElementById('demo_mode').addEventListener('change', function() {
    const twilioFields = document.querySelectorAll('#twilio_sid, #twilio_token, #twilio_number');
    const isDemo = this.checked;
    
    twilioFields.forEach(field => {
        field.disabled = isDemo;
        if (isDemo) {
            field.parentElement.style.opacity = '0.5';
        } else {
            field.parentElement.style.opacity = '1';
        }
    });
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('demo_mode').dispatchEvent(new Event('change'));
});
</script>
{% endblock %}
