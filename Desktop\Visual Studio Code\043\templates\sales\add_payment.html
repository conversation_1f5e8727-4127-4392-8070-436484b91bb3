{% extends "base.html" %}

{% block title %}إضافة دفعة - مبيعة #{{ sale.id }} - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إضافة دفعة - مبيعة #{{ sale.id }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('sales.view', sale_id=sale.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمبيعة
        </a>
    </div>
</div>

<form method="POST" id="paymentForm">
    <div class="row">
        <!-- Payment Form -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">بيانات الدفعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Amount -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="amount" class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control currency-input" id="amount" 
                                           name="amount" step="0.01" min="0" required>
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <div class="form-text">
                                    المبلغ المتبقي: <strong class="text-warning">{{ remaining_amount|currency }}</strong>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Method -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash">نقدي</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                    <option value="installment">قسط</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Payment Date -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payment_date" class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                       value="{{ current_date.strftime('%Y-%m-%d') }}" required>
                            </div>
                        </div>
                        
                        <!-- Reference Number -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reference_number" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number"
                                       placeholder="رقم الشيك، رقم التحويل، إلخ...">
                                <div class="form-text">اختياري - للتحويلات والشيكات</div>
                            </div>
                        </div>
                        
                        <!-- Installment Selection (for installment sales) -->
                        {% if sale.sale_type == 'installment' and pending_installments %}
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="installment_id" class="form-label">ربط بقسط محدد</label>
                                <select class="form-select" id="installment_id" name="installment_id">
                                    <option value="">دفعة عامة (غير مرتبطة بقسط)</option>
                                    {% for installment in pending_installments %}
                                    <option value="{{ installment.id }}" 
                                            data-amount="{{ installment.amount }}"
                                            data-due-date="{{ installment.due_date.strftime('%Y-%m-%d') }}">
                                        قسط #{{ installment.installment_number }} - 
                                        {{ installment.amount|currency }} - 
                                        استحقاق: {{ installment.due_date|arabic_date }}
                                        {% if installment.due_date < current_date %}
                                        <span class="text-danger">(متأخر)</span>
                                        {% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    اختر قسط محدد لربط الدفعة به، أو اتركه فارغاً للدفعة العامة
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Notes -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="أضف أي ملاحظات حول الدفعة..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment Summary -->
            <div class="card" id="summaryCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">ملخص الدفعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>مبلغ الدفعة:</strong></td>
                                    <td id="summaryAmount">-</td>
                                </tr>
                                <tr>
                                    <td><strong>طريقة الدفع:</strong></td>
                                    <td id="summaryMethod">-</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الدفع:</strong></td>
                                    <td id="summaryDate">-</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr id="summaryInstallmentRow" style="display: none;">
                                    <td><strong>القسط المرتبط:</strong></td>
                                    <td id="summaryInstallment">-</td>
                                </tr>
                                <tr id="summaryReferenceRow" style="display: none;">
                                    <td><strong>رقم المرجع:</strong></td>
                                    <td id="summaryReference">-</td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ المتبقي بعد الدفع:</strong></td>
                                    <td id="summaryRemaining" class="text-success">-</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Sale Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات المبيعة</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>العميل:</strong></td>
                            <td>{{ sale.customer.full_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>السيارة:</strong></td>
                            <td>{{ sale.car.brand }} {{ sale.car.model }}</td>
                        </tr>
                        <tr>
                            <td><strong>سعر البيع:</strong></td>
                            <td><strong class="text-primary">{{ sale.sale_price|currency }}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>نوع البيع:</strong></td>
                            <td>
                                <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                    {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Payment Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">حالة المدفوعات</h5>
                </div>
                <div class="card-body">
                    {% set total_paid = (sale.down_payment or 0) %}
                    {% set remaining = sale.sale_price - total_paid %}
                    
                    <div class="text-center">
                        <div class="row">
                            <div class="col-6">
                                <h6>المدفوع</h6>
                                <h5 class="text-success">{{ total_paid|currency }}</h5>
                            </div>
                            <div class="col-6">
                                <h6>المتبقي</h6>
                                <h5 class="text-warning" id="remainingAmount">{{ remaining|currency }}</h5>
                            </div>
                        </div>
                        
                        <div class="progress mt-3">
                            {% set progress = (total_paid / sale.sale_price * 100) if sale.sale_price > 0 else 0 %}
                            <div class="progress-bar" role="progressbar" style="width: {{ progress }}%" id="progressBar">
                                {{ "%.1f"|format(progress) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الدفعة
                        </button>
                        
                        <a href="{{ url_for('sales.view', sale_id=sale.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        
                        <hr>
                        
                        {% if sale.customer.whatsapp_number or sale.customer.phone %}
                        <a href="{{ url_for('whatsapp.send_message') }}?customer_id={{ sale.customer.id }}" 
                           class="btn btn-outline-success btn-sm">
                            <i class="fab fa-whatsapp me-2"></i>
                            إرسال رسالة للعميل
                        </a>
                        {% endif %}
                        
                        <a href="{{ url_for('sales.installments', sale_id=sale.id) }}" 
                           class="btn btn-outline-info btn-sm">
                            <i class="fas fa-calendar-alt me-2"></i>
                            عرض جدول الأقساط
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-fill amount when installment is selected
    $('#installment_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const amount = selectedOption.data('amount');
        if (amount) {
            $('#amount').val(amount);
        }
        updateSummary();
    });
    
    // Update summary when fields change
    $('#amount, #payment_method, #payment_date, #reference_number, #installment_id').on('input change', function() {
        updateSummary();
    });
    
    function updateSummary() {
        const amount = parseFloat($('#amount').val()) || 0;
        const method = $('#payment_method option:selected').text();
        const date = $('#payment_date').val();
        const reference = $('#reference_number').val();
        const installment = $('#installment_id option:selected').text();
        
        if (amount > 0 && method && date) {
            $('#summaryAmount').text(formatCurrency(amount));
            $('#summaryMethod').text(method);
            $('#summaryDate').text(date);
            
            if (reference) {
                $('#summaryReferenceRow').show();
                $('#summaryReference').text(reference);
            } else {
                $('#summaryReferenceRow').hide();
            }
            
            if ($('#installment_id').val()) {
                $('#summaryInstallmentRow').show();
                $('#summaryInstallment').text(installment);
            } else {
                $('#summaryInstallmentRow').hide();
            }
            
            // Calculate remaining amount
            const currentRemaining = {{ remaining_amount }};
            const newRemaining = currentRemaining - amount;
            $('#summaryRemaining').text(formatCurrency(newRemaining));
            
            // Update progress bar
            const totalPrice = {{ sale.sale_price }};
            const totalPaid = {{ total_paid }} + amount;
            const progress = (totalPaid / totalPrice * 100);
            $('#progressBar').css('width', progress + '%').text(progress.toFixed(1) + '%');
            $('#remainingAmount').text(formatCurrency(newRemaining));
            
            $('#summaryCard').show();
        } else {
            $('#summaryCard').hide();
        }
    }
    
    function formatCurrency(amount) {
        return parseFloat(amount).toLocaleString('ar-SA') + ' ريال';
    }
    
    // Form validation
    $('#paymentForm').on('submit', function(e) {
        if (!validatePaymentForm()) {
            e.preventDefault();
        }
    });
    
    function validatePaymentForm() {
        let isValid = true;
        
        const amount = parseFloat($('#amount').val());
        const remaining = {{ remaining_amount }};
        
        if (!amount || amount <= 0) {
            showFieldError($('#amount'), 'مبلغ الدفعة يجب أن يكون أكبر من صفر');
            isValid = false;
        } else if (amount > remaining) {
            showFieldError($('#amount'), 'مبلغ الدفعة لا يمكن أن يكون أكبر من المبلغ المتبقي');
            isValid = false;
        }
        
        if (!$('#payment_method').val()) {
            showFieldError($('#payment_method'), 'يجب اختيار طريقة الدفع');
            isValid = false;
        }
        
        if (!$('#payment_date').val()) {
            showFieldError($('#payment_date'), 'تاريخ الدفع مطلوب');
            isValid = false;
        }
        
        return isValid;
    }
    
    function showFieldError(field, message) {
        field.addClass('is-invalid');
        
        // Remove existing error message
        field.siblings('.invalid-feedback').remove();
        
        // Add new error message
        field.after(`<div class="invalid-feedback">${message}</div>`);
        
        // Remove error styling after user starts typing
        field.one('input change', function() {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        });
        
        // Show alert
        showAlert(message, 'danger');
    }
});
</script>
{% endblock %}
