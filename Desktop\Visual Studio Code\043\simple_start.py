#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Flask Server for Car Dealership System
معرض بوخليفة للسيارات - تشغيل بسيط
"""

from flask import Flask, render_template_string
import os

# Create Flask app
app = Flask(__name__)
app.secret_key = 'simple-key-for-testing'

@app.route('/')
def home():
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض بوخليفة للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                text-align: right;
                background: linear-gradient(135deg, #722f37 0%, #4a1c40 100%);
                color: white;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .main-container {
                background: rgba(255, 255, 255, 0.95);
                color: #2c1810;
                padding: 40px;
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                text-align: center;
                max-width: 800px;
            }
            .btn-main {
                background: linear-gradient(135deg, #722f37 0%, #4a1c40 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 12px;
                margin: 10px;
                text-decoration: none;
                display: inline-block;
                transition: all 0.3s ease;
                font-size: 16px;
            }
            .btn-main:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(114, 47, 55, 0.3);
                color: white;
            }
            .status-ok { color: #28a745; font-weight: bold; }
            .status-error { color: #dc3545; font-weight: bold; }
            .feature-card {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="main-container">
            <h1>🚗 معرض بوخليفة للسيارات</h1>
            <h3>Car Dealership Management System</h3>
            
            <div class="alert alert-success">
                <strong>✅ النظام يعمل بنجاح!</strong> الخادم البسيط نشط ويعمل
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <h5>🔧 حالة الخادم</h5>
                        <p class="status-ok">✅ يعمل بشكل طبيعي</p>
                        <p class="status-ok">✅ Flask متاح</p>
                        <p class="status-ok">✅ Bootstrap محمل</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <h5>📊 الإحصائيات</h5>
                        <p>المنفذ: <strong>1212</strong></p>
                        <p>الحالة: <span class="status-ok">نشط</span></p>
                        <p>الوضع: <strong>بسيط</strong></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <h5>🌐 الروابط</h5>
                        <p><a href="/test" class="btn btn-sm btn-primary">اختبار</a></p>
                        <p><a href="/diagnosis" class="btn btn-sm btn-info">تشخيص</a></p>
                        <p><a href="/full" class="btn btn-sm btn-success">النظام الكامل</a></p>
                    </div>
                </div>
            </div>
            
            <hr>
            
            <h5>الإجراءات المتاحة:</h5>
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="/test" class="btn-main">
                    🔧 اختبار النظام
                </a>
                <a href="/diagnosis" class="btn-main">
                    🩺 تشخيص شامل
                </a>
                <a href="/full" class="btn-main">
                    🚀 تشغيل النظام الكامل
                </a>
                <button onclick="location.reload()" class="btn-main">
                    🔄 إعادة تحميل
                </button>
            </div>
            
            <hr>
            
            <div class="alert alert-info">
                <h6>📋 معلومات مهمة:</h6>
                <ul class="text-start">
                    <li>هذا خادم بسيط للاختبار والتشخيص</li>
                    <li>للوصول للنظام الكامل اضغط "النظام الكامل"</li>
                    <li>إذا واجهت مشاكل استخدم صفحة التشخيص</li>
                    <li>جميع الوظائف الأساسية متاحة</li>
                </ul>
            </div>
            
            <small class="text-muted">
                معرض بوخليفة للسيارات © 2024 - نظام إدارة متطور
            </small>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            console.log('✅ Simple server loaded successfully');
            console.log('🚗 Bukhalifa Auto Dealership System');
        </script>
    </body>
    </html>
    ''')

@app.route('/test')
def test():
    return render_template_string('''
    <h1>🔧 صفحة الاختبار</h1>
    <p>الخادم البسيط يعمل بنجاح!</p>
    <a href="/">العودة للرئيسية</a>
    ''')

@app.route('/diagnosis')
def diagnosis():
    return render_template_string('''
    <h1>🩺 صفحة التشخيص</h1>
    <p>نظام التشخيص البسيط</p>
    <ul>
        <li>✅ Flask يعمل</li>
        <li>✅ الخادم نشط</li>
        <li>✅ الروابط تعمل</li>
    </ul>
    <a href="/">العودة للرئيسية</a>
    ''')

@app.route('/full')
def full_system():
    return render_template_string('''
    <h1>🚀 النظام الكامل</h1>
    <p>لتشغيل النظام الكامل، استخدم الأمر:</p>
    <code>python app.py</code>
    <p>أو اضغط على الرابط أدناه إذا كان النظام الكامل يعمل:</p>
    <a href="http://localhost:1212" target="_blank">النظام الكامل</a>
    <br><br>
    <a href="/">العودة للرئيسية</a>
    ''')

if __name__ == '__main__':
    print("🚗 معرض بوخليفة للسيارات - Simple Server")
    print("🚀 Starting simple server...")
    print("🌐 Server running on: http://localhost:1212")
    print("⚠️  Press Ctrl+C to stop")
    print("=" * 50)

    app.run(
        host='0.0.0.0',
        port=1212,
        debug=True,
        use_reloader=False
    )
