from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض بوخليفة للسيارات - طارئ</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #722f37 0%, #8b1538 100%);
                color: white;
                text-align: center;
                padding: 50px;
                margin: 0;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                color: #722f37;
                padding: 60px;
                border-radius: 20px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                max-width: 600px;
                width: 100%;
            }
            h1 { font-size: 2.5rem; margin-bottom: 20px; }
            h2 { color: #8b1538; margin-bottom: 30px; }
            .status {
                background: #28a745;
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                font-size: 18px;
                font-weight: bold;
            }
            a {
                display: inline-block;
                background: #722f37;
                color: white;
                padding: 15px 30px;
                margin: 10px;
                border-radius: 10px;
                text-decoration: none;
                font-size: 16px;
            }
            a:hover { background: #8b1538; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚗 معرض بوخليفة للسيارات</h1>
            <h2>الخادم الطارئ يعمل!</h2>
            <div class="status">✅ Emergency Server Working!</div>
            <p>الخادم الطارئ يعمل بنجاح على المنفذ 1212</p>
            <p>Emergency server is working on port 1212</p>
            <a href="/test">Test Page</a>
            <a href="/login">Login</a>
        </div>
    </body>
    </html>
    '''

@app.route('/test')
def test():
    return '''
    <h1>Test Page</h1>
    <p>Server is working!</p>
    <a href="/">Home</a>
    '''

if __name__ == '__main__':
    print("Emergency server starting...")
    app.run(host='0.0.0.0', port=1212, debug=True)
