#!/usr/bin/env python3
"""
إصلاح مشاكل التخطيط والعرض في النظام
Fix layout and display issues in the system
"""

import os
import sys
import shutil
from datetime import datetime

def check_file_exists(file_path):
    """Check if file exists and is readable"""
    return os.path.exists(file_path) and os.access(file_path, os.R_OK)

def fix_missing_files():
    """Fix missing CSS and JS files"""
    print("🔧 إصلاح الملفات المفقودة...")
    
    # Check and create missing directories
    directories = [
        'static/css',
        'static/js',
        'static/images',
        'templates/errors'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"   ✅ تم إنشاء مجلد: {directory}")
    
    # Check critical files
    critical_files = {
        'static/css/style.css': create_basic_css,
        'static/js/notifications.js': create_notifications_js,
        'templates/errors/404.html': create_404_template,
        'templates/errors/500.html': create_500_template
    }
    
    for file_path, create_function in critical_files.items():
        if not check_file_exists(file_path):
            print(f"   🔨 إنشاء ملف مفقود: {file_path}")
            create_function(file_path)
        else:
            print(f"   ✅ الملف موجود: {file_path}")

def create_basic_css(file_path):
    """Create basic CSS file"""
    css_content = """
/* Basic CSS for the system */
body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
}

.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
}

.sidebar {
    background: #343a40;
    min-height: 100vh;
    padding: 20px 0;
}

.main-content {
    padding: 20px;
}

.card {
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.btn {
    border-radius: 8px;
    padding: 8px 16px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
}

.table {
    border-radius: 8px;
    overflow: hidden;
}

/* RTL fixes */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .container-fluid {
        padding: 10px;
    }
}
"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(css_content)

def create_notifications_js(file_path):
    """Create notifications JavaScript file"""
    js_content = """
// Notifications system
function showNotification(message, type = 'info', duration = 5000) {
    // Remove existing notifications
    const existing = document.querySelectorAll('.notification-toast');
    existing.forEach(n => n.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification-toast alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
}

// Success notification
function showSuccess(message) {
    showNotification(message, 'success');
}

// Error notification
function showError(message) {
    showNotification(message, 'danger');
}

// Warning notification
function showWarning(message) {
    showNotification(message, 'warning');
}

// Info notification
function showInfo(message) {
    showNotification(message, 'info');
}

// Global error handler
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    showError('حدث خطأ في النظام. يرجى إعادة تحميل الصفحة.');
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    showError('حدث خطأ في النظام. يرجى المحاولة مرة أخرى.');
});
"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(js_content)

def create_404_template(file_path):
    """Create 404 error template"""
    html_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - 404</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 40px;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-message {
            font-size: 1.5rem;
            margin: 20px 0;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: white;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">404</div>
        <div class="error-message">
            <i class="fas fa-search fa-2x mb-3"></i>
            <h2>الصفحة غير موجودة</h2>
            <p>عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.</p>
        </div>
        <a href="/" class="btn-home">
            <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
        </a>
    </div>
</body>
</html>"""
    
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

def create_500_template(file_path):
    """Create 500 error template"""
    html_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في الخادم - 500</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 40px;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-message {
            font-size: 1.5rem;
            margin: 20px 0;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: white;
            color: #f5576c;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">500</div>
        <div class="error-message">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h2>خطأ في الخادم</h2>
            <p>عذراً، حدث خطأ داخلي في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>
        </div>
        <a href="/" class="btn-home">
            <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
        </a>
    </div>
</body>
</html>"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

def fix_base_template():
    """Fix base template issues"""
    print("🔧 إصلاح قالب base.html...")
    
    base_path = 'templates/base.html'
    if not check_file_exists(base_path):
        print(f"   ❌ ملف {base_path} غير موجود")
        return False
    
    # Read current content
    with open(base_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for common issues and fix them
    fixes_applied = []
    
    # Ensure RTL direction
    if 'dir="rtl"' not in content:
        content = content.replace('<html', '<html lang="ar" dir="rtl"')
        fixes_applied.append('إضافة اتجاه RTL')
    
    # Ensure viewport meta tag
    if 'viewport' not in content:
        content = content.replace('<head>', '<head>\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">')
        fixes_applied.append('إضافة viewport meta tag')
    
    # Ensure Bootstrap RTL
    if 'bootstrap.rtl.min.css' not in content:
        content = content.replace('bootstrap@5.3.0/dist/css/bootstrap.min.css', 'bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css')
        fixes_applied.append('تحديث Bootstrap إلى RTL')
    
    if fixes_applied:
        # Create backup
        backup_path = f"{base_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(base_path, backup_path)
        
        # Write fixed content
        with open(base_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   ✅ تم إصلاح: {', '.join(fixes_applied)}")
        print(f"   💾 نسخة احتياطية: {backup_path}")
    else:
        print("   ✅ لا توجد مشاكل في base.html")
    
    return True

def check_permissions():
    """Check file and directory permissions"""
    print("🔒 فحص الصلاحيات...")
    
    paths_to_check = [
        'static',
        'templates',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    issues = []
    for path in paths_to_check:
        if os.path.exists(path):
            if not os.access(path, os.R_OK):
                issues.append(f"{path} - غير قابل للقراءة")
            elif not os.access(path, os.W_OK):
                issues.append(f"{path} - غير قابل للكتابة")
            else:
                print(f"   ✅ {path} - الصلاحيات صحيحة")
        else:
            issues.append(f"{path} - غير موجود")
    
    if issues:
        print("   ⚠️ مشاكل في الصلاحيات:")
        for issue in issues:
            print(f"      • {issue}")
        return False
    
    return True

def test_system():
    """Test system functionality"""
    print("🧪 اختبار النظام...")
    
    try:
        # Test Flask app creation
        sys.path.insert(0, os.getcwd())
        from app import create_app
        
        app = create_app()
        print("   ✅ تم إنشاء التطبيق بنجاح")
        
        # Test routes
        with app.test_client() as client:
            # Test main routes
            routes_to_test = [
                ('/', 'الصفحة الرئيسية'),
                ('/debug-layout', 'صفحة التشخيص')
            ]
            
            for route, name in routes_to_test:
                try:
                    response = client.get(route)
                    if response.status_code in [200, 302]:  # 302 for redirects
                        print(f"   ✅ {name} - يعمل")
                    else:
                        print(f"   ⚠️ {name} - كود الحالة: {response.status_code}")
                except Exception as e:
                    print(f"   ❌ {name} - خطأ: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النظام: {str(e)}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح مشاكل التخطيط والعرض")
    print("=" * 50)
    
    # Check current directory
    if not os.path.exists('app.py'):
        print("❌ يجب تشغيل هذا السكريبت من مجلد المشروع")
        sys.exit(1)
    
    success = True
    
    # Fix missing files
    try:
        fix_missing_files()
    except Exception as e:
        print(f"❌ خطأ في إصلاح الملفات: {str(e)}")
        success = False
    
    # Fix base template
    try:
        fix_base_template()
    except Exception as e:
        print(f"❌ خطأ في إصلاح القالب: {str(e)}")
        success = False
    
    # Check permissions
    try:
        check_permissions()
    except Exception as e:
        print(f"❌ خطأ في فحص الصلاحيات: {str(e)}")
        success = False
    
    # Test system
    try:
        test_system()
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {str(e)}")
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("🌐 يمكنك الآن الوصول لصفحة التشخيص:")
        print("   http://localhost:5000/debug-layout")
    else:
        print("⚠️ تم إصلاح بعض المشاكل، لكن قد تحتاج لمراجعة يدوية")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
