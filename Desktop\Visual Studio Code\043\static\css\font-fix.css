
/* ULTIMATE FONT AND TEXT FIX */

/* Import Google Fonts for better Arabic support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* Force font loading and visibility */
* {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #212529 !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inherit !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

html {
    font-size: 16px !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', <PERSON>hom<PERSON>, <PERSON>l, sans-serif !important;
}

body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #212529 !important;
    background-color: #f8f9fa !important;
}

/* Headers with proper sizing */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-weight: 600 !important;
    color: inherit !important;
    margin-bottom: 0.5rem !important;
}

h1 { font-size: 2.5rem !important; }
h2 { font-size: 2rem !important; }
h3 { font-size: 1.75rem !important; }
h4 { font-size: 1.5rem !important; }
h5 { font-size: 1.25rem !important; }
h6 { font-size: 1rem !important; }

/* Paragraphs and text */
p {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    color: inherit !important;
    margin-bottom: 1rem !important;
}

/* Sidebar specific fixes */
.sidebar {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: white !important;
}

.sidebar * {
    color: white !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.sidebar h4 {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: white !important;
}

.sidebar .nav-link {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: rgba(255,255,255,0.9) !important;
    padding: 12px 20px !important;
    display: block !important;
    text-decoration: none !important;
}

.sidebar .nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1) !important;
}

.sidebar .nav-link i {
    font-size: 1rem !important;
    width: 20px !important;
    margin-left: 10px !important;
    color: inherit !important;
}

/* Card fixes */
.card-header {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: white !important;
}

.card-header * {
    color: white !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.card-body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

.card-body * {
    color: #212529 !important;
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

/* Button fixes */
.btn {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    padding: 10px 20px !important;
    border-radius: 8px !important;
}

.btn * {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: inherit !important;
}

/* Form fixes */
.form-label {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: #212529 !important;
}

.form-control,
.form-select {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: #212529 !important;
}

/* Table fixes */
.table {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.table th,
.table td {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: #212529 !important;
}

/* Icon fixes */
i,
.fa,
.fas,
.far,
.fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    font-size: 1rem !important;
    color: inherit !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: inline-block !important;
}

/* Force text to be visible */
* {
    text-indent: 0 !important;
    letter-spacing: normal !important;
    word-spacing: normal !important;
    white-space: normal !important;
    text-transform: none !important;
    text-shadow: none !important;
    outline: none !important;
}

/* Ensure proper contrast */
.text-white,
.text-white * {
    color: white !important;
}

.text-dark,
.text-dark * {
    color: #212529 !important;
}

/* Main content area */
.main-content {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

.main-content * {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

/* Override any hiding styles */
.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

/* Ensure all text is selectable */
* {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Force font loading */
@font-face {
    font-family: 'Fallback Arabic';
    src: local('Segoe UI'), local('Tahoma'), local('Arial');
    font-display: swap;
}

/* Backup font stack */
.backup-font {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Arial', 'Fallback Arabic', sans-serif !important;
}

/* Apply backup font to everything */
* {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
}
