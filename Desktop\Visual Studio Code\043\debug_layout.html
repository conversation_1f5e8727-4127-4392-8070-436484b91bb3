<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل التخطيط</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .debug-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .debug-title {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .status-good {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        
        .status-error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        
        .status-warning {
            color: #856404;
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .test-card.working {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .test-card.broken {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .console-output {
            background: #212529;
            color: #28a745;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .fix-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        
        .fix-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">🔧 تشخيص مشاكل التخطيط</h1>
        
        <!-- System Status -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-heartbeat me-2"></i>حالة النظام</h3>
            <div id="systemStatus">
                <div class="status-good">
                    <i class="fas fa-check-circle me-2"></i>جاري فحص النظام...
                </div>
            </div>
        </div>
        
        <!-- CSS Loading Test -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-palette me-2"></i>اختبار تحميل CSS</h3>
            <div class="test-grid">
                <div class="test-card working">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5>Bootstrap CSS</h5>
                    <p>تم التحميل بنجاح</p>
                </div>
                <div class="test-card working">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5>Font Awesome</h5>
                    <p>الأيقونات تعمل</p>
                </div>
                <div class="test-card" id="customCssTest">
                    <i class="fas fa-spinner fa-spin fa-2x text-warning mb-2"></i>
                    <h5>CSS المخصص</h5>
                    <p>جاري الفحص...</p>
                </div>
                <div class="test-card" id="arabicCssTest">
                    <i class="fas fa-spinner fa-spin fa-2x text-warning mb-2"></i>
                    <h5>CSS الأرقام العربية</h5>
                    <p>جاري الفحص...</p>
                </div>
            </div>
        </div>
        
        <!-- JavaScript Loading Test -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-code me-2"></i>اختبار تحميل JavaScript</h3>
            <div class="test-grid">
                <div class="test-card working">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5>jQuery</h5>
                    <p>محمل ويعمل</p>
                </div>
                <div class="test-card working">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5>Bootstrap JS</h5>
                    <p>محمل ويعمل</p>
                </div>
                <div class="test-card" id="arabicJsTest">
                    <i class="fas fa-spinner fa-spin fa-2x text-warning mb-2"></i>
                    <h5>الأرقام العربية JS</h5>
                    <p>جاري الفحص...</p>
                </div>
            </div>
        </div>
        
        <!-- Layout Test -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-th-large me-2"></i>اختبار التخطيط</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="test-card working">
                        <h6>الشريط الجانبي</h6>
                        <p>العرض: 25%</p>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="test-card working">
                        <h6>المحتوى الرئيسي</h6>
                        <p>العرض: 75%</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Arabic Numbers Test -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-language me-2"></i>اختبار الأرقام العربية</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>الأرقام الإنجليزية:</h5>
                    <div class="console-output">
                        <div>123456789</div>
                        <div>123.45</div>
                        <div>1,234,567</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>الأرقام العربية:</h5>
                    <div class="console-output" id="arabicNumbers">
                        <div>جاري التحويل...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Console Errors -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-bug me-2"></i>أخطاء وحدة التحكم</h3>
            <div class="console-output" id="consoleErrors">
                <div>جاري فحص الأخطاء...</div>
            </div>
        </div>
        
        <!-- Quick Fixes -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-tools me-2"></i>إصلاحات سريعة</h3>
            <div class="text-center">
                <button class="fix-button" onclick="reloadCSS()">
                    <i class="fas fa-sync me-2"></i>إعادة تحميل CSS
                </button>
                <button class="fix-button" onclick="resetLayout()">
                    <i class="fas fa-th-large me-2"></i>إعادة تعيين التخطيط
                </button>
                <button class="fix-button" onclick="clearCache()">
                    <i class="fas fa-trash me-2"></i>مسح التخزين المؤقت
                </button>
                <button class="fix-button" onclick="testArabicNumbers()">
                    <i class="fas fa-language me-2"></i>اختبار الأرقام العربية
                </button>
                <button class="fix-button" onclick="fullDiagnostic()">
                    <i class="fas fa-stethoscope me-2"></i>فحص شامل
                </button>
            </div>
        </div>
        
        <!-- Recommendations -->
        <div class="debug-section">
            <h3 class="debug-title"><i class="fas fa-lightbulb me-2"></i>التوصيات</h3>
            <div id="recommendations">
                <div class="status-warning">
                    <i class="fas fa-info-circle me-2"></i>جاري تحليل المشاكل وإعداد التوصيات...
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Debug functions
        function checkSystemStatus() {
            const status = document.getElementById('systemStatus');
            let html = '';
            
            // Check jQuery
            if (typeof jQuery !== 'undefined') {
                html += '<div class="status-good"><i class="fas fa-check-circle me-2"></i>jQuery محمل ويعمل</div>';
            } else {
                html += '<div class="status-error"><i class="fas fa-times-circle me-2"></i>jQuery غير محمل</div>';
            }
            
            // Check Bootstrap
            if (typeof bootstrap !== 'undefined') {
                html += '<div class="status-good"><i class="fas fa-check-circle me-2"></i>Bootstrap محمل ويعمل</div>';
            } else {
                html += '<div class="status-error"><i class="fas fa-times-circle me-2"></i>Bootstrap غير محمل</div>';
            }
            
            // Check viewport
            const viewport = window.innerWidth;
            html += `<div class="status-good"><i class="fas fa-desktop me-2"></i>عرض الشاشة: ${viewport}px</div>`;
            
            // Check direction
            const direction = document.dir || document.documentElement.dir;
            if (direction === 'rtl') {
                html += '<div class="status-good"><i class="fas fa-align-right me-2"></i>الاتجاه: من اليمين لليسار</div>';
            } else {
                html += '<div class="status-warning"><i class="fas fa-exclamation-triangle me-2"></i>الاتجاه: من اليسار لليمين</div>';
            }
            
            status.innerHTML = html;
        }
        
        function checkCustomCSS() {
            const testCard = document.getElementById('customCssTest');
            
            // Try to load a CSS file and check if it exists
            fetch('/static/css/style.css')
                .then(response => {
                    if (response.ok) {
                        testCard.innerHTML = `
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5>CSS المخصص</h5>
                            <p>تم التحميل بنجاح</p>
                        `;
                        testCard.className = 'test-card working';
                    } else {
                        throw new Error('CSS file not found');
                    }
                })
                .catch(error => {
                    testCard.innerHTML = `
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h5>CSS المخصص</h5>
                        <p>فشل في التحميل</p>
                    `;
                    testCard.className = 'test-card broken';
                });
        }
        
        function checkArabicCSS() {
            const testCard = document.getElementById('arabicCssTest');
            
            fetch('/static/css/arabic-numbers.css')
                .then(response => {
                    if (response.ok) {
                        testCard.innerHTML = `
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5>CSS الأرقام العربية</h5>
                            <p>تم التحميل بنجاح</p>
                        `;
                        testCard.className = 'test-card working';
                    } else {
                        throw new Error('Arabic CSS file not found');
                    }
                })
                .catch(error => {
                    testCard.innerHTML = `
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h5>CSS الأرقام العربية</h5>
                        <p>فشل في التحميل</p>
                    `;
                    testCard.className = 'test-card broken';
                });
        }
        
        function checkArabicJS() {
            const testCard = document.getElementById('arabicJsTest');
            
            fetch('/static/js/arabic-numbers.js')
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    } else {
                        throw new Error('Arabic JS file not found');
                    }
                })
                .then(jsContent => {
                    // Try to evaluate if the script would work
                    if (jsContent.includes('ArabicNumbers') && jsContent.includes('toArabic')) {
                        testCard.innerHTML = `
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5>الأرقام العربية JS</h5>
                            <p>تم التحميل بنجاح</p>
                        `;
                        testCard.className = 'test-card working';
                    } else {
                        throw new Error('Arabic JS content invalid');
                    }
                })
                .catch(error => {
                    testCard.innerHTML = `
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h5>الأرقام العربية JS</h5>
                        <p>فشل في التحميل</p>
                    `;
                    testCard.className = 'test-card broken';
                });
        }
        
        function testArabicNumbers() {
            const arabicDiv = document.getElementById('arabicNumbers');
            
            // Simple conversion function
            function toArabic(text) {
                const arabicDigits = {
                    '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
                    '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩',
                    '.': '٫', ',': '،'
                };
                
                for (const [english, arabic] of Object.entries(arabicDigits)) {
                    text = text.replace(new RegExp(english, 'g'), arabic);
                }
                return text;
            }
            
            arabicDiv.innerHTML = `
                <div>${toArabic('123456789')}</div>
                <div>${toArabic('123.45')}</div>
                <div>${toArabic('1,234,567')}</div>
            `;
        }
        
        function checkConsoleErrors() {
            const consoleDiv = document.getElementById('consoleErrors');
            let errors = [];
            
            // Override console.error to catch errors
            const originalError = console.error;
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            // Check for common errors
            setTimeout(() => {
                let html = '';
                if (errors.length === 0) {
                    html = '<div style="color: #28a745;">✅ لا توجد أخطاء في وحدة التحكم</div>';
                } else {
                    html = '<div style="color: #dc3545;">❌ تم العثور على أخطاء:</div>';
                    errors.forEach(error => {
                        html += `<div style="color: #dc3545;">• ${error}</div>`;
                    });
                }
                consoleDiv.innerHTML = html;
            }, 2000);
        }
        
        function generateRecommendations() {
            const recommendationsDiv = document.getElementById('recommendations');
            let recommendations = [];
            
            // Check viewport
            if (window.innerWidth < 768) {
                recommendations.push('الشاشة صغيرة - تأكد من التصميم المتجاوب');
            }
            
            // Check if RTL is working
            if (document.dir !== 'rtl') {
                recommendations.push('تأكد من إعداد dir="rtl" في HTML');
            }
            
            // Check CSS loading
            const stylesheets = document.styleSheets;
            if (stylesheets.length < 3) {
                recommendations.push('قد تكون بعض ملفات CSS غير محملة');
            }
            
            let html = '';
            if (recommendations.length === 0) {
                html = '<div class="status-good"><i class="fas fa-thumbs-up me-2"></i>النظام يعمل بشكل جيد!</div>';
            } else {
                recommendations.forEach(rec => {
                    html += `<div class="status-warning"><i class="fas fa-exclamation-triangle me-2"></i>${rec}</div>`;
                });
            }
            
            recommendationsDiv.innerHTML = html;
        }
        
        // Fix functions
        function reloadCSS() {
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            links.forEach(link => {
                const href = link.href;
                link.href = href + '?v=' + new Date().getTime();
            });
            
            setTimeout(() => {
                alert('تم إعادة تحميل CSS');
                location.reload();
            }, 1000);
        }
        
        function resetLayout() {
            document.body.style.cssText = '';
            document.documentElement.style.cssText = '';
            alert('تم إعادة تعيين التخطيط');
        }
        
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            localStorage.clear();
            sessionStorage.clear();
            alert('تم مسح التخزين المؤقت');
        }
        
        function fullDiagnostic() {
            checkSystemStatus();
            checkCustomCSS();
            checkArabicCSS();
            checkArabicJS();
            testArabicNumbers();
            checkConsoleErrors();
            generateRecommendations();
            
            alert('تم إجراء فحص شامل للنظام');
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            checkCustomCSS();
            checkArabicCSS();
            checkArabicJS();
            testArabicNumbers();
            checkConsoleErrors();
            generateRecommendations();
        });
    </script>
</body>
</html>
