#!/usr/bin/env python3
"""
Test script to verify price input fixes
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    try:
        print("🔄 Testing imports...")
        
        from flask import Flask
        print("✅ Flask imported successfully")
        
        from app import create_app
        print("✅ App module imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_app_creation():
    """Test Flask app creation"""
    try:
        print("\n🔄 Testing app creation...")
        
        from app import create_app
        app = create_app()
        
        print("✅ Flask app created successfully")
        print(f"✅ App name: {app.name}")
        
        # Test routes
        with app.test_client() as client:
            # Test price test route
            response = client.get('/price-test')
            if response.status_code == 302:  # Redirect to login
                print("✅ Price test route exists (redirects to login)")
            else:
                print(f"⚠️ Price test route status: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def test_static_files():
    """Test if static files exist"""
    try:
        print("\n🔄 Testing static files...")
        
        static_files = [
            'static/js/input-validator.js',
            'templates/price-test.html',
            'templates/cars/add.html',
            'templates/sales/add.html'
        ]
        
        for file_path in static_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path} exists")
            else:
                print(f"❌ {file_path} missing")
        
        return True
    except Exception as e:
        print(f"❌ Static files test error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 اختبار إصلاحات حقول الأسعار")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_app_creation,
        test_static_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! إصلاحات الأسعار تعمل بشكل صحيح.")
        print("\n📱 للاختبار الفعلي:")
        print("1. شغل الخادم: python simple_run.py")
        print("2. اذهب إلى: http://localhost:5000/price-test")
        print("3. جرب إدخال أرقام مختلفة")
        return True
    else:
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للمتابعة...")
    sys.exit(0 if success else 1)
