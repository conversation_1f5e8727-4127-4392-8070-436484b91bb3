{% extends "base.html" %}

{% block title %}رقم اللوحة {{ plate_number.number }}{% endblock %}

{% block extra_css %}
<style>
.plate-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.plate-hero.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.plate-hero.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.plate-hero.auction {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    animation: pulse 2s infinite;
}

.plate-display {
    font-family: 'Courier New', monospace;
    font-size: 4rem;
    font-weight: bold;
    margin: 20px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.status-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1rem;
    padding: 8px 16px;
    border-radius: 20px;
}

.price-display {
    font-size: 2rem;
    font-weight: bold;
    margin-top: 20px;
}

.auction-timer {
    background: rgba(255,255,255,0.2);
    padding: 15px;
    border-radius: 10px;
    margin-top: 20px;
}

.feature-badge {
    display: inline-block;
    margin: 5px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.info-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: bold;
    color: #666;
}

.info-value {
    color: #333;
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.image-item:hover {
    transform: scale(1.05);
}

.image-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.sales-history {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.bid-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #28a745;
}

.bid-item.winning {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.action-buttons {
    position: sticky;
    top: 20px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(67, 233, 123, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(67, 233, 123, 0); }
    100% { box-shadow: 0 0 0 0 rgba(67, 233, 123, 0); }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Plate Hero Section -->
            <div class="plate-hero {{ plate_number.category }} {{ 'auction' if plate_number.is_auction and plate_number.status == 'available' }}">
                <span class="status-badge badge bg-{{ 'success' if plate_number.status == 'available' else 'danger' if plate_number.status == 'sold' else 'warning' }}">
                    {% if plate_number.status == 'available' %}متاحة
                    {% elif plate_number.status == 'sold' %}مباعة
                    {% elif plate_number.status == 'reserved' %}محجوزة
                    {% else %}محظورة{% endif %}
                </span>
                
                <div class="plate-display">{{ plate_number.number }}</div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>الفئة</h5>
                        <span class="badge bg-light text-dark fs-6">
                            {% if plate_number.category == 'vip' %}VIP - مميزة جداً
                            {% elif plate_number.category == 'special' %}مميزة
                            {% elif plate_number.category == 'regular' %}عادية
                            {% else %}مخصصة{% endif %}
                        </span>
                    </div>
                    <div class="col-md-6">
                        <h5>النوع</h5>
                        <span class="badge bg-light text-dark fs-6">
                            {% if plate_number.type == 'private' %}خاصة
                            {% elif plate_number.type == 'taxi' %}تاكسي
                            {% elif plate_number.type == 'transport' %}نقل
                            {% else %}حكومية{% endif %}
                        </span>
                    </div>
                </div>
                
                <!-- Price Display -->
                {% if plate_number.is_auction and plate_number.status == 'available' %}
                    <div class="price-display">
                        المزايدة الحالية: {{ "{:,.0f}".format(plate_number.current_bid or plate_number.starting_bid) }} ريال قطري
                    </div>
                    {% if plate_number.auction_end_date %}
                    <div class="auction-timer">
                        <i class="fas fa-clock me-2"></i>
                        <strong>ينتهي في:</strong> {{ plate_number.auction_end_date.strftime('%Y-%m-%d %H:%M') }}
                        <div id="countdown" class="mt-2"></div>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="price-display">
                        {{ "{:,.0f}".format(plate_number.price) }} ريال قطري
                    </div>
                {% endif %}
            </div>

            <!-- Special Features -->
            {% if plate_number.is_sequential or plate_number.is_repeated or plate_number.is_mirror or plate_number.is_birthday or plate_number.is_lucky %}
            <div class="info-card">
                <h5><i class="fas fa-star me-2"></i>المميزات الخاصة</h5>
                <div class="mt-3">
                    {% if plate_number.is_sequential %}
                        <span class="feature-badge bg-success text-white">
                            <i class="fas fa-sort-numeric-up me-1"></i>أرقام متسلسلة
                        </span>
                    {% endif %}
                    {% if plate_number.is_repeated %}
                        <span class="feature-badge bg-warning text-dark">
                            <i class="fas fa-clone me-1"></i>أرقام مكررة
                        </span>
                    {% endif %}
                    {% if plate_number.is_mirror %}
                        <span class="feature-badge bg-info text-white">
                            <i class="fas fa-mirror me-1"></i>أرقام مرآة
                        </span>
                    {% endif %}
                    {% if plate_number.is_birthday %}
                        <span class="feature-badge bg-primary text-white">
                            <i class="fas fa-birthday-cake me-1"></i>تاريخ ميلاد
                        </span>
                    {% endif %}
                    {% if plate_number.is_lucky %}
                        <span class="feature-badge bg-danger text-white">
                            <i class="fas fa-clover me-1"></i>أرقام محظوظة
                        </span>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Detailed Information -->
            <div class="info-card">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات تفصيلية</h5>
                
                <div class="info-row">
                    <span class="info-label">رقم اللوحة:</span>
                    <span class="info-value">{{ plate_number.number }}</span>
                </div>
                
                {% if plate_number.code_letter %}
                <div class="info-row">
                    <span class="info-label">الحرف:</span>
                    <span class="info-value">{{ plate_number.code_letter }}</span>
                </div>
                {% endif %}
                
                {% if plate_number.series %}
                <div class="info-row">
                    <span class="info-label">السلسلة:</span>
                    <span class="info-value">{{ plate_number.series }}</span>
                </div>
                {% endif %}
                
                <div class="info-row">
                    <span class="info-label">عدد الأرقام:</span>
                    <span class="info-value">{{ plate_number.digits_count }} أرقام</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ الإضافة:</span>
                    <span class="info-value">{{ plate_number.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                
                {% if plate_number.cost_price %}
                <div class="info-row">
                    <span class="info-label">سعر التكلفة:</span>
                    <span class="info-value">{{ "{:,.0f}".format(plate_number.cost_price) }} ريال</span>
                </div>
                {% endif %}
                
                {% if plate_number.description %}
                <div class="info-row">
                    <span class="info-label">الوصف:</span>
                    <span class="info-value">{{ plate_number.description }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Images Gallery -->
            {% if images %}
            <div class="info-card">
                <h5><i class="fas fa-images me-2"></i>صور رقم اللوحة</h5>
                <div class="image-gallery">
                    {% for image in images %}
                    <div class="image-item">
                        <img src="{{ url_for('static', filename=image) }}" alt="صورة رقم اللوحة" 
                             onclick="showImageModal('{{ url_for('static', filename=image) }}')">
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Auction Bids -->
            {% if plate_number.is_auction and bids %}
            <div class="info-card">
                <h5><i class="fas fa-gavel me-2"></i>تاريخ المزايدات</h5>
                {% for bid in bids %}
                <div class="bid-item {{ 'winning' if bid.is_winning }}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ bid.customer.full_name }}</strong>
                            <small class="text-muted d-block">{{ bid.bid_date.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                        <div class="text-end">
                            <span class="h5 mb-0">{{ "{:,.0f}".format(bid.bid_amount) }} ريال</span>
                            {% if bid.is_winning %}
                            <span class="badge bg-success ms-2">الأعلى</span>
                            {% endif %}
                        </div>
                    </div>
                    {% if bid.notes %}
                    <p class="mb-0 mt-2 text-muted">{{ bid.notes }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Sales History -->
            {% if sales %}
            <div class="sales-history">
                <h5><i class="fas fa-history me-2"></i>تاريخ المبيعات</h5>
                {% for sale in sales %}
                <div class="bg-white p-3 rounded mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>العميل:</strong> {{ sale.customer.full_name }}<br>
                            <strong>تاريخ البيع:</strong> {{ sale.sale_date.strftime('%Y-%m-%d') }}<br>
                            <strong>طريقة الدفع:</strong> 
                            {% if sale.payment_method == 'cash' %}نقد
                            {% elif sale.payment_method == 'bank_transfer' %}تحويل بنكي
                            {% else %}أقساط{% endif %}
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="h5">{{ "{:,.0f}".format(sale.sale_price) }} ريال</span><br>
                            <span class="badge bg-{{ 'success' if sale.status == 'completed' else 'warning' }}">
                                {% if sale.status == 'active' %}نشط
                                {% elif sale.status == 'completed' %}مكتمل
                                {% else %}ملغي{% endif %}
                            </span>
                        </div>
                    </div>
                    {% if sale.notes %}
                    <p class="mb-0 mt-2 text-muted">{{ sale.notes }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="action-buttons">
                <h5><i class="fas fa-bolt me-2"></i>الإجراءات</h5>
                
                <div class="d-grid gap-2">
                    {% if current_user.has_permission('edit_all') %}
                    <a href="{{ url_for('plate_numbers.edit', id=plate_number.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                    {% endif %}
                    
                    {% if plate_number.status == 'available' %}
                        {% if plate_number.is_auction %}
                        <button class="btn btn-success" onclick="showBidModal()">
                            <i class="fas fa-gavel me-2"></i>مزايدة
                        </button>
                        {% else %}
                        <a href="{{ url_for('plate_numbers.sell', id=plate_number.id) }}" class="btn btn-success">
                            <i class="fas fa-handshake me-2"></i>بيع
                        </a>
                        {% endif %}
                    {% endif %}
                    
                    <button class="btn btn-info" onclick="shareNumber()">
                        <i class="fas fa-share me-2"></i>مشاركة
                    </button>
                    
                    <button class="btn btn-secondary" onclick="printDetails()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    
                    <hr>
                    
                    <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                    
                    <a href="{{ url_for('plate_numbers.add') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>إضافة رقم جديد
                    </a>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="info-card mt-3">
                <h6><i class="fas fa-chart-line me-2"></i>إحصائيات سريعة</h6>
                <div class="info-row">
                    <span class="info-label">الفئة:</span>
                    <span class="info-value">
                        {% if plate_number.category == 'vip' %}VIP
                        {% elif plate_number.category == 'special' %}مميزة
                        {% elif plate_number.category == 'regular' %}عادية
                        {% else %}مخصصة{% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد المزايدات:</span>
                    <span class="info-value">{{ bids|length }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد المبيعات:</span>
                    <span class="info-value">{{ sales|length }}</span>
                </div>
                {% if plate_number.cost_price and plate_number.price %}
                <div class="info-row">
                    <span class="info-label">هامش الربح:</span>
                    <span class="info-value text-success">
                        {{ "{:,.0f}".format(plate_number.price - plate_number.cost_price) }} ريال
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صورة رقم اللوحة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="صورة رقم اللوحة">
            </div>
        </div>
    </div>
</div>

<!-- Bid Modal -->
{% if plate_number.is_auction and plate_number.status == 'available' %}
<div class="modal fade" id="bidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">مزايدة على رقم {{ plate_number.number }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bidForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">المزايدة الحالية</label>
                        <input type="text" class="form-control" value="{{ '{:,.0f}'.format(plate_number.current_bid or plate_number.starting_bid) }} ريال" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                        <select class="form-select" id="customer_id" name="customer_id" required>
                            <option value="">اختر العميل</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="bid_amount" class="form-label">مبلغ المزايدة <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="bid_amount" name="bid_amount" required 
                               min="{{ (plate_number.current_bid or plate_number.starting_bid) + 100 }}" step="100">
                        <div class="form-text">يجب أن يكون أعلى من {{ "{:,.0f}".format((plate_number.current_bid or plate_number.starting_bid) + 100) }} ريال</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تأكيد المزايدة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Countdown timer for auction
{% if plate_number.is_auction and plate_number.auction_end_date %}
function updateCountdown() {
    const endDate = new Date('{{ plate_number.auction_end_date.isoformat() }}');
    const now = new Date();
    const diff = endDate - now;
    
    if (diff > 0) {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        document.getElementById('countdown').innerHTML = 
            `${days} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;
    } else {
        document.getElementById('countdown').innerHTML = 'انتهت المزايدة';
    }
}

// Update countdown every second
setInterval(updateCountdown, 1000);
updateCountdown();
{% endif %}

// Image modal
function showImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

// Bid modal
function showBidModal() {
    $('#bidModal').modal('show');
    loadCustomers();
}

function loadCustomers() {
    if ($('#customer_id option').length <= 1) {
        $.get('/customers/api/list', function(data) {
            const select = $('#customer_id');
            select.empty().append('<option value="">اختر العميل</option>');
            data.forEach(customer => {
                select.append(`<option value="${customer.id}">${customer.full_name} - ${customer.phone}</option>`);
            });
        });
    }
}

// Submit bid
$('#bidForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    $.post('{{ url_for("plate_numbers.place_bid", id=plate_number.id) }}', formData)
        .done(function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                $('#bidModal').modal('hide');
                location.reload();
            } else {
                showNotification(response.message, 'error');
            }
        })
        .fail(function() {
            showNotification('حدث خطأ أثناء تسجيل المزايدة', 'error');
        });
});

// Share number
function shareNumber() {
    const url = window.location.href;
    const text = `رقم لوحة مميز: {{ plate_number.number }} - {{ "{:,.0f}".format(plate_number.price) }} ريال`;
    
    if (navigator.share) {
        navigator.share({
            title: 'رقم لوحة مميز',
            text: text,
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(`${text}\n${url}`).then(() => {
            showNotification('تم نسخ الرابط', 'success');
        });
    }
}

// Print details
function printDetails() {
    window.print();
}
</script>
{% endblock %}
