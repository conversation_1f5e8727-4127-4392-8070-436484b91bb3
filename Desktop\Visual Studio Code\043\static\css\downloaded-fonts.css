/* Downloaded Arabic Fonts */

/* Cairo Font Family */
@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

/* Noto Sans Arabic Font Family */
@font-face {
    font-family: 'Noto Sans Arabic';
    src: url('../fonts/NotoSansArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Arabic';
    src: url('../fonts/NotoSansArabic-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Noto Naskh Arabic Font */
@font-face {
    font-family: 'Noto Naskh Arabic';
    src: url('../fonts/NotoNaskhArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Amiri Font */
@font-face {
    font-family: 'Amiri';
    src: url('../fonts/Amiri-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Amiri';
    src: url('../fonts/Amiri-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Font Stack Variables */
:root {
    --font-arabic-modern: 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;
    --font-arabic-traditional: 'Noto Naskh Arabic', 'Amiri', serif;
    --font-arabic-display: 'Cairo', 'Amiri', serif;
}

/* Apply fonts */
body {
    font-family: var(--font-arabic-modern);
}

.text-traditional {
    font-family: var(--font-arabic-traditional);
}

.text-display {
    font-family: var(--font-arabic-display);
}
