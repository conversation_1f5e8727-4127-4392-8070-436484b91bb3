#!/usr/bin/env python3
"""
Font Downloader for Qatar Car Dealership System
Downloads required Arabic fonts from Google Fonts
"""

import os
import requests
import zipfile
from pathlib import Path
import shutil

class FontDownloader:
    def __init__(self, fonts_dir="static/fonts"):
        self.fonts_dir = Path(fonts_dir)
        self.fonts_dir.mkdir(exist_ok=True)
        
        # Google Fonts download URLs
        self.font_urls = {
            'Cairo': 'https://fonts.google.com/download?family=Cairo',
            'Noto Sans Arabic': 'https://fonts.google.com/download?family=Noto%20Sans%20Arabic',
            'Amiri': 'https://fonts.google.com/download?family=Amiri',
            'Noto Naskh Arabic': 'https://fonts.google.com/download?family=Noto%20Naskh%20Arabic'
        }
        
        # Alternative direct URLs (if Google Fonts API changes)
        self.alternative_urls = {
            'Cairo': 'https://github.com/google/fonts/raw/main/ofl/cairo/Cairo%5Bslnt%2Cwght%5D.ttf',
            'Noto Sans Arabic': 'https://github.com/google/fonts/raw/main/ofl/notosansarabic/NotoSansArabic%5Bwdth%2Cwght%5D.ttf',
            'Amiri': 'https://github.com/google/fonts/raw/main/ofl/amiri/Amiri-Regular.ttf',
            'Noto Naskh Arabic': 'https://github.com/google/fonts/raw/main/ofl/notonaskharabic/NotoNaskhArabic-Regular.ttf'
        }
    
    def download_file(self, url, filename):
        """Download a file from URL"""
        try:
            print(f"📥 Downloading {filename}...")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, stream=True)
            response.raise_for_status()
            
            file_path = self.fonts_dir / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(file_path)
            print(f"✅ Downloaded {filename} ({file_size:,} bytes)")
            
            return True
            
        except Exception as e:
            print(f"❌ Error downloading {filename}: {e}")
            return False
    
    def extract_zip(self, zip_path, extract_to):
        """Extract ZIP file and move TTF files"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            
            # Find and move TTF files
            ttf_files = list(extract_to.glob("**/*.ttf"))
            
            for ttf_file in ttf_files:
                dest_path = self.fonts_dir / ttf_file.name
                shutil.move(str(ttf_file), str(dest_path))
                print(f"✅ Extracted: {ttf_file.name}")
            
            # Clean up
            os.remove(zip_path)
            
            # Remove empty directories
            for item in extract_to.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
            
            return len(ttf_files)
            
        except Exception as e:
            print(f"❌ Error extracting {zip_path}: {e}")
            return 0
    
    def download_google_fonts(self):
        """Download fonts from Google Fonts"""
        print("🔽 Downloading fonts from Google Fonts...")
        print("=" * 45)
        
        temp_dir = self.fonts_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        success_count = 0
        
        for font_name, url in self.font_urls.items():
            try:
                print(f"\n📦 Processing {font_name}...")
                
                # Download ZIP file
                zip_filename = f"{font_name.replace(' ', '_')}.zip"
                zip_path = temp_dir / zip_filename
                
                if self.download_file(url, zip_filename):
                    # Move ZIP to temp directory
                    shutil.move(self.fonts_dir / zip_filename, zip_path)
                    
                    # Extract TTF files
                    extracted_count = self.extract_zip(zip_path, temp_dir)
                    
                    if extracted_count > 0:
                        success_count += 1
                        print(f"✅ Successfully processed {font_name}")
                    else:
                        print(f"⚠️  No TTF files found in {font_name}")
                
            except Exception as e:
                print(f"❌ Error processing {font_name}: {e}")
        
        # Clean up temp directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        
        print(f"\n📊 Successfully downloaded {success_count}/{len(self.font_urls)} font families")
        return success_count
    
    def download_alternative_fonts(self):
        """Download fonts from alternative sources"""
        print("🔽 Downloading fonts from alternative sources...")
        print("=" * 50)
        
        success_count = 0
        
        for font_name, url in self.alternative_urls.items():
            try:
                filename = url.split('/')[-1]
                # Clean filename
                filename = filename.replace('%5B', '[').replace('%5D', ']').replace('%2C', ',')
                
                if self.download_file(url, filename):
                    success_count += 1
                
            except Exception as e:
                print(f"❌ Error downloading {font_name}: {e}")
        
        print(f"\n📊 Successfully downloaded {success_count}/{len(self.alternative_urls)} fonts")
        return success_count
    
    def verify_downloads(self):
        """Verify downloaded fonts"""
        print("\n🔍 Verifying downloaded fonts...")
        print("=" * 35)
        
        required_patterns = [
            "Cairo",
            "NotoSansArabic",
            "NotoNaskhArabic", 
            "Amiri"
        ]
        
        found_fonts = []
        all_fonts = list(self.fonts_dir.glob("*.ttf"))
        
        for pattern in required_patterns:
            matching_fonts = [f for f in all_fonts if pattern.lower() in f.name.lower()]
            
            if matching_fonts:
                for font in matching_fonts:
                    file_size = os.path.getsize(font)
                    print(f"✅ Found: {font.name} ({file_size:,} bytes)")
                    found_fonts.extend(matching_fonts)
            else:
                print(f"❌ Missing: {pattern}")
        
        print(f"\n📊 Found {len(found_fonts)} relevant font files")
        
        # List all fonts
        print("\n📋 All font files:")
        print("-" * 20)
        
        total_size = 0
        for font_file in sorted(all_fonts):
            file_size = os.path.getsize(font_file)
            total_size += file_size
            print(f"   {font_file.name} ({file_size:,} bytes)")
        
        print(f"\n📊 Total: {len(all_fonts)} files, {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        
        return len(found_fonts)
    
    def create_font_css(self):
        """Create CSS file with font declarations"""
        css_content = '''/* Downloaded Arabic Fonts */

/* Cairo Font Family */
@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

/* Noto Sans Arabic Font Family */
@font-face {
    font-family: 'Noto Sans Arabic';
    src: url('../fonts/NotoSansArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Arabic';
    src: url('../fonts/NotoSansArabic-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Noto Naskh Arabic Font */
@font-face {
    font-family: 'Noto Naskh Arabic';
    src: url('../fonts/NotoNaskhArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Amiri Font */
@font-face {
    font-family: 'Amiri';
    src: url('../fonts/Amiri-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Amiri';
    src: url('../fonts/Amiri-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Font Stack Variables */
:root {
    --font-arabic-modern: 'Cairo', 'Noto Sans Arabic', system-ui, sans-serif;
    --font-arabic-traditional: 'Noto Naskh Arabic', 'Amiri', serif;
    --font-arabic-display: 'Cairo', 'Amiri', serif;
}

/* Apply fonts */
body {
    font-family: var(--font-arabic-modern);
}

.text-traditional {
    font-family: var(--font-arabic-traditional);
}

.text-display {
    font-family: var(--font-arabic-display);
}
'''
        
        css_path = Path("static/css/downloaded-fonts.css")
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        print(f"✅ Created CSS file: {css_path}")

def main():
    """Main function"""
    print("📥 Font Downloader for Qatar Car Dealership")
    print("=" * 45)
    
    downloader = FontDownloader()
    
    print("Choose download method:")
    print("1. Google Fonts (recommended)")
    print("2. Alternative sources")
    print("3. Both methods")
    print("4. Verify existing fonts only")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        downloader.download_google_fonts()
    elif choice == "2":
        downloader.download_alternative_fonts()
    elif choice == "3":
        downloader.download_google_fonts()
        downloader.download_alternative_fonts()
    elif choice == "4":
        pass
    else:
        print("❌ Invalid choice")
        return
    
    # Always verify and create CSS
    downloader.verify_downloads()
    downloader.create_font_css()
    
    print("\n🎉 Font download process completed!")
    print("\n💡 Next steps:")
    print("   1. Run 'python check_fonts.py' to verify fonts")
    print("   2. Include downloaded-fonts.css in your HTML")
    print("   3. Test the fonts in your application")

if __name__ == "__main__":
    main()
