{% extends "base.html" %}

{% block title %}تاريخ رسائل الواتساب - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تاريخ رسائل الواتساب</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('whatsapp.send_message') }}" class="btn btn-success">
                <i class="fab fa-whatsapp me-2"></i>
                إرسال رسالة جديدة
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="exportHistory()">
                <i class="fas fa-download me-2"></i>
                تصدير التاريخ
            </button>
        </div>
        <a href="{{ url_for('whatsapp.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للواتساب
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">الرسائل المرسلة</h5>
                <h2 class="text-success">{{ total_sent or 0 }}</h2>
                <small class="text-muted">إجمالي</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">في الانتظار</h5>
                <h2 class="text-warning">{{ total_pending or 0 }}</h2>
                <small class="text-muted">رسالة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">فشلت</h5>
                <h2 class="text-danger">{{ total_failed or 0 }}</h2>
                <small class="text-muted">رسالة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">هذا الشهر</h5>
                <h2 class="text-info">{{ this_month or 0 }}</h2>
                <small class="text-muted">رسالة</small>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">فلترة الرسائل</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" class="form-select" id="status">
                    <option value="">جميع الحالات</option>
                    <option value="sent" {{ 'selected' if status == 'sent' }}>مرسلة</option>
                    <option value="pending" {{ 'selected' if status == 'pending' }}>معلقة</option>
                    <option value="failed" {{ 'selected' if status == 'failed' }}>فشلت</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="message_type" class="form-label">نوع الرسالة</label>
                <select name="message_type" class="form-select" id="message_type">
                    <option value="">جميع الأنواع</option>
                    <option value="payment_reminder" {{ 'selected' if message_type == 'payment_reminder' }}>تذكير دفع</option>
                    <option value="welcome" {{ 'selected' if message_type == 'welcome' }}>ترحيب</option>
                    <option value="follow_up" {{ 'selected' if message_type == 'follow_up' }}>متابعة</option>
                    <option value="promotion" {{ 'selected' if message_type == 'promotion' }}>ترويجي</option>
                    <option value="custom" {{ 'selected' if message_type == 'custom' }}>مخصص</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="customer_search" class="form-label">العميل</label>
                <input type="text" class="form-control" id="customer_search" name="customer_search" 
                       value="{{ customer_search }}" placeholder="اسم العميل...">
            </div>
            
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
            </div>
            
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
        
        {% if status or message_type or customer_search or date_from or date_to %}
        <div class="mt-3">
            <a href="{{ url_for('whatsapp.history') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times me-1"></i>
                إلغاء الفلترة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Messages Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">سجل الرسائل</h5>
        <span class="badge bg-primary">{{ messages.total if messages else 0 }} رسالة</span>
    </div>
    <div class="card-body">
        {% if messages and messages.items %}
        <div class="table-responsive">
            <table class="table table-hover" id="messagesTable">
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>العميل</th>
                        <th>رقم الهاتف</th>
                        <th>نوع الرسالة</th>
                        <th>الحالة</th>
                        <th>الرسالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for message in messages.items %}
                    <tr class="{% if message.status == 'failed' %}table-danger{% elif message.status == 'pending' %}table-warning{% endif %}">
                        <td>
                            <div>
                                {{ message.created_at.strftime('%Y/%m/%d') }}
                                <br><small class="text-muted">{{ message.created_at.strftime('%H:%M') }}</small>
                            </div>
                            {% if message.sent_at %}
                            <div class="mt-1">
                                <small class="text-success">
                                    <i class="fas fa-check me-1"></i>
                                    أرسلت: {{ message.sent_at.strftime('%H:%M') }}
                                </small>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            {% if message.customer %}
                            <div class="d-flex align-items-center">
                                {% if message.customer.photo_path %}
                                <img src="{{ url_for('static', filename=message.customer.photo_path) }}" 
                                     class="rounded-circle me-2" width="30" height="30" 
                                     style="object-fit: cover;" alt="صورة العميل">
                                {% else %}
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" 
                                     style="width: 30px; height: 30px; font-size: 0.7rem;">
                                    {{ message.customer.full_name[:2].upper() }}
                                </div>
                                {% endif %}
                                <div>
                                    <a href="{{ url_for('customers.view', customer_id=message.customer.id) }}" 
                                       class="text-decoration-none">
                                        {{ message.customer.full_name }}
                                    </a>
                                    <br><small class="text-muted">{{ message.customer.national_id }}</small>
                                </div>
                            </div>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-user-slash me-1"></i>
                                غير محدد
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="https://wa.me/{{ message.phone_number.replace('+', '').replace(' ', '') }}" 
                               target="_blank" class="text-decoration-none">
                                <i class="fab fa-whatsapp text-success me-1"></i>
                                {{ message.phone_number }}
                            </a>
                        </td>
                        <td>
                            {% if message.message_type == 'payment_reminder' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-bell me-1"></i>
                                    تذكير دفع
                                </span>
                            {% elif message.message_type == 'welcome' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-hand-wave me-1"></i>
                                    ترحيب
                                </span>
                            {% elif message.message_type == 'follow_up' %}
                                <span class="badge bg-info">
                                    <i class="fas fa-phone me-1"></i>
                                    متابعة
                                </span>
                            {% elif message.message_type == 'promotion' %}
                                <span class="badge bg-purple">
                                    <i class="fas fa-tag me-1"></i>
                                    ترويجي
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-comment me-1"></i>
                                    مخصص
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if message.status == 'sent' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    مرسلة
                                </span>
                            {% elif message.status == 'pending' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>
                                    معلقة
                                </span>
                            {% elif message.status == 'failed' %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times-circle me-1"></i>
                                    فشلت
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">{{ message.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="message-preview" style="max-width: 250px;">
                                <div class="text-truncate">
                                    {{ message.message[:80] }}
                                    {% if message.message|length > 80 %}...{% endif %}
                                </div>
                                {% if message.error_message %}
                                <small class="text-danger mt-1 d-block">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    {{ message.error_message[:50] }}
                                </small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#messageModal"
                                        data-message="{{ message.message }}"
                                        data-phone="{{ message.phone_number }}"
                                        data-customer="{{ message.customer.full_name if message.customer else 'غير محدد' }}"
                                        data-date="{{ message.created_at.strftime('%Y/%m/%d %H:%M') }}"
                                        data-status="{{ message.status }}"
                                        data-type="{{ message.message_type }}"
                                        title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                
                                {% if message.status == 'failed' %}
                                <a href="{{ url_for('whatsapp.send_message') }}?customer_id={{ message.customer.id if message.customer else '' }}&phone={{ message.phone_number }}" 
                                   class="btn btn-sm btn-outline-success" title="إعادة الإرسال">
                                    <i class="fas fa-redo"></i>
                                </a>
                                {% endif %}
                                
                                {% if message.customer %}
                                <a href="{{ url_for('whatsapp.send_message') }}?customer_id={{ message.customer.id }}" 
                                   class="btn btn-sm btn-outline-primary" title="إرسال رسالة جديدة">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                {% endif %}
                                
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteMessage({{ message.id }})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if messages.pages > 1 %}
        <nav aria-label="تنقل الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if messages.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('whatsapp.history', page=messages.prev_num, status=status, message_type=message_type, customer_search=customer_search, date_from=date_from, date_to=date_to) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in messages.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != messages.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('whatsapp.history', page=page_num, status=status, message_type=message_type, customer_search=customer_search, date_from=date_from, date_to=date_to) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if messages.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('whatsapp.history', page=messages.next_num, status=status, message_type=message_type, customer_search=customer_search, date_from=date_from, date_to=date_to) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fab fa-whatsapp fa-3x text-muted mb-3"></i>
            <h5>لا توجد رسائل</h5>
            {% if status or message_type or customer_search or date_from or date_to %}
            <p class="text-muted">لم يتم العثور على رسائل تطابق المعايير المحددة.</p>
            <a href="{{ url_for('whatsapp.history') }}" class="btn btn-outline-primary">
                <i class="fas fa-times me-2"></i>
                إلغاء الفلترة
            </a>
            {% else %}
            <p class="text-muted">لم يتم إرسال أي رسائل بعد.</p>
            <a href="{{ url_for('whatsapp.send_message') }}" class="btn btn-success">
                <i class="fab fa-whatsapp me-2"></i>
                إرسال أول رسالة
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Message Details Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الرسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>العميل:</strong></td>
                                <td id="modalCustomer">-</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td id="modalPhone">-</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td id="modalDate">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>نوع الرسالة:</strong></td>
                                <td id="modalType">-</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td id="modalStatus">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>نص الرسالة:</h6>
                    <div class="whatsapp-message-preview">
                        <div class="chat-bubble-preview">
                            <div id="modalMessage" class="message-text-preview">
                                <!-- Message content will be loaded here -->
                            </div>
                            <div class="message-time-preview">
                                <small class="text-muted">الآن</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" id="openWhatsApp">
                    <i class="fab fa-whatsapp me-2"></i>
                    فتح في واتساب
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.whatsapp-message-preview {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    padding: 20px;
    border-radius: 10px;
    min-height: 120px;
}

.chat-bubble-preview {
    background: #DCF8C6;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 85%;
    margin-left: auto;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: relative;
}

.chat-bubble-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: #DCF8C6;
    border-bottom: 0;
    margin-left: -8px;
    margin-bottom: -8px;
}

.message-text-preview {
    color: #303030;
    line-height: 1.4;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message-time-preview {
    text-align: right;
    margin-top: 5px;
}

.message-preview {
    font-size: 0.9rem;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

.table-responsive {
    border-radius: 0.375rem;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 2px;
    }
    
    .message-preview {
        max-width: 150px !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#messagesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 0, "desc" ]], // Sort by date descending
        "pageLength": 25,
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": [6] } // Disable sorting for actions column
        ]
    });
    
    // Handle message modal
    $('#messageModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const message = button.data('message');
        const phone = button.data('phone');
        const customer = button.data('customer');
        const date = button.data('date');
        const status = button.data('status');
        const type = button.data('type');
        
        $('#modalCustomer').text(customer);
        $('#modalPhone').text(phone);
        $('#modalDate').text(date);
        $('#modalMessage').text(message);
        
        // Set status badge
        let statusBadge = '';
        if (status === 'sent') {
            statusBadge = '<span class="badge bg-success">مرسلة</span>';
        } else if (status === 'pending') {
            statusBadge = '<span class="badge bg-warning">معلقة</span>';
        } else if (status === 'failed') {
            statusBadge = '<span class="badge bg-danger">فشلت</span>';
        }
        $('#modalStatus').html(statusBadge);
        
        // Set type badge
        let typeBadge = '';
        if (type === 'payment_reminder') {
            typeBadge = '<span class="badge bg-warning">تذكير دفع</span>';
        } else if (type === 'welcome') {
            typeBadge = '<span class="badge bg-success">ترحيب</span>';
        } else if (type === 'follow_up') {
            typeBadge = '<span class="badge bg-info">متابعة</span>';
        } else if (type === 'promotion') {
            typeBadge = '<span class="badge bg-purple">ترويجي</span>';
        } else {
            typeBadge = '<span class="badge bg-secondary">مخصص</span>';
        }
        $('#modalType').html(typeBadge);
        
        // Set WhatsApp button
        $('#openWhatsApp').off('click').on('click', function() {
            const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        });
    });
});

function deleteMessage(messageId) {
    if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    fetch(`/whatsapp/messages/${messageId}/delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showAlert('حدث خطأ أثناء حذف الرسالة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء حذف الرسالة', 'danger');
    });
}

function exportHistory() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    
    window.location.href = `{{ url_for('whatsapp.history') }}?${params.toString()}`;
}

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}
</script>
{% endblock %}
