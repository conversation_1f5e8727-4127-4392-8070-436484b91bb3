<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ company_name }}{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts (Arabic) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- Preload critical fonts -->
    <link rel="preload" href="{{ url_for('static', filename='fonts/Cairo[slnt,wght].ttf') }}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="{{ url_for('static', filename='fonts/NotoSansArabic[wdth,wght].ttf') }}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="{{ url_for('static', filename='fonts/NotoNaskhArabic-Regular.ttf') }}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="{{ url_for('static', filename='fonts/Amiri-Regular.ttf') }}" as="font" type="font/ttf" crossorigin>

    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/arabic-fonts.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/downloaded-fonts.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/font-enhancements.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/number-formatting.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/arabic-numbers.css') }}" rel="stylesheet">
        <!-- Ultimate Fix CSS -->
    <link href="{{ url_for('static', filename='css/ultimate-fix.css') }}" rel="stylesheet">
        <!-- Font Fix CSS -->
    <link href="{{ url_for('static', filename='css/font-fix.css') }}" rel="stylesheet">
    <!-- Text Fix CSS -->
    <link href="{{ url_for('static', filename='css/text-fix.css') }}" rel="stylesheet">
    <!-- Emergency Fix CSS -->
    <link href="{{ url_for('static', filename='css/emergency-fix.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        /* CRITICAL FIX - Force proper layout */
        * {
            box-sizing: border-box !important;
        }

        html, body {
            direction: rtl !important;
            text-align: right !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
            overflow-x: hidden !important;
        }

        body {
            font-family: 'Cairo', 'Noto Sans Arabic', 'Amiri', 'Segoe UI', Tahoma, Arial, sans-serif !important;
            background-color: #f8f9fa !important;
            font-feature-settings: "liga" 1, "kern" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            line-height: 1.6 !important;
        }

        /* Force container to work */
        .container-fluid {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            direction: rtl !important;
        }

        /* Force row to work */
        .row {
            display: flex !important;
            flex-wrap: wrap !important;
            width: 100% !important;
            margin: 0 !important;
            direction: rtl !important;
        }
        
        .navbar-brand {
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        /* FORCE SIDEBAR TO WORK */
        .sidebar {
            min-height: 100vh !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
            position: relative !important;
            width: 100% !important;
            padding: 20px 0 !important;
            direction: rtl !important;
            text-align: right !important;
        }

        /* Force columns to work */
        .col-md-3, .col-lg-2 {
            flex: 0 0 250px !important;
            max-width: 250px !important;
            width: 250px !important;
            padding: 0 !important;
        }

        .col-md-9, .col-lg-10 {
            flex: 1 !important;
            max-width: calc(100% - 250px) !important;
            width: calc(100% - 250px) !important;
            padding: 20px !important;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8) !important;
            padding: 12px 20px !important;
            margin: 2px 0 !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            display: block !important;
            text-decoration: none !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            padding: 20px !important;
            direction: rtl !important;
            text-align: right !important;
            min-height: 100vh !important;
            background-color: #f8f9fa !important;
        }

        /* Force all text to be RTL */
        * {
            direction: rtl !important;
            text-align: inherit !important;
        }

        /* Force visibility */
        .d-md-block {
            display: block !important;
        }

        .position-sticky {
            position: sticky !important;
            top: 0 !important;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 500;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .badge {
            font-size: 0.8em;
            padding: 6px 12px;
            border-radius: 20px;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stats-card .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
        <!-- Complete Fix CSS - FINAL -->
    <link href="{{ url_for('static', filename='css/complete-fix.css') }}" rel="stylesheet">
    <!-- Emergency Icons CSS -->
    <link href="{{ url_for('static', filename='css/emergency-icons.css') }}" rel="stylesheet">

    <!-- EMERGENCY FONT FIX -->
    <style>
        /* FORCE FONTS TO LOAD IMMEDIATELY */
        * {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            font-size: 16px !important;
            color: #212529 !important;
            visibility: visible !important;
            opacity: 1 !important;
            text-indent: 0 !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            white-space: normal !important;
            text-transform: none !important;
            text-shadow: none !important;
            outline: none !important;
            display: inherit !important;
        }

        /* Force body and html */
        html, body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            font-size: 16px !important;
            direction: rtl !important;
            text-align: right !important;
        }

        .sidebar * {
            color: white !important;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
        }

        /* FORCE ICONS TO SHOW */
        i, .fa, .fas, .far, .fab {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
            font-style: normal !important;
            display: inline-block !important;
            text-rendering: auto !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            visibility: visible !important;
            opacity: 1 !important;
            width: auto !important;
            height: auto !important;
            text-indent: 0 !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            white-space: nowrap !important;
            text-transform: none !important;
            text-shadow: none !important;
            outline: none !important;
        }

        /* Fallback for missing icons */
        .fa::before, .fas::before, .far::before, .fab::before {
            content: "●" !important;
            font-family: Arial !important;
            font-weight: bold !important;
            color: inherit !important;
        }

        /* Specific icon fixes with better emojis */
        .fa-home::before { content: "🏠" !important; font-family: Arial !important; }
        .fa-tachometer-alt::before { content: "📊" !important; font-family: Arial !important; }
        .fa-chart-bar::before { content: "📈" !important; font-family: Arial !important; }
        .fa-car::before { content: "🚗" !important; font-family: Arial !important; }
        .fa-id-card::before { content: "🔢" !important; font-family: Arial !important; }
        .fa-users::before { content: "👥" !important; font-family: Arial !important; }
        .fa-handshake::before { content: "🤝" !important; font-family: Arial !important; }
        .fa-file-contract::before { content: "📋" !important; font-family: Arial !important; }
        .fa-whatsapp::before { content: "💬" !important; font-family: Arial !important; }
        .fa-bell::before { content: "🔔" !important; font-family: Arial !important; }
        .fa-user::before { content: "👤" !important; font-family: Arial !important; }
        .fa-cog::before { content: "⚙️" !important; font-family: Arial !important; }
        .fa-settings::before { content: "⚙️" !important; font-family: Arial !important; }
        .fa-sign-out-alt::before { content: "🚪" !important; font-family: Arial !important; }

        .sidebar .nav-link {
            font-size: 16px !important;
            color: rgba(255,255,255,0.9) !important;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            text-decoration: none !important;
            display: block !important;
            padding: 12px 20px !important;
            margin: 2px 0 !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            text-align: right !important;
            direction: rtl !important;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.1) !important;
            color: white !important;
            transform: translateX(-5px) !important;
        }

        .sidebar .nav-link i {
            margin-left: 10px !important;
            margin-right: 0 !important;
            width: 20px !important;
            text-align: center !important;
            font-size: 16px !important;
        }

        /* Force sidebar text to show */
        .sidebar .nav-link span,
        .sidebar .nav-link {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            font-size: 16px !important;
            font-weight: 500 !important;
            color: rgba(255,255,255,0.9) !important;
            text-decoration: none !important;
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            text-indent: 0 !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            white-space: nowrap !important;
            text-transform: none !important;
            text-shadow: none !important;
            outline: none !important;
        }

        /* Sidebar sections styling */
        .sidebar-section {
            margin-bottom: 25px !important;
            padding: 0 10px !important;
        }

        .sidebar-heading {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            font-size: 12px !important;
            font-weight: 600 !important;
            color: rgba(255,255,255,0.6) !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            margin-bottom: 10px !important;
            padding: 0 10px !important;
            text-align: right !important;
            direction: rtl !important;
        }

        .sidebar-section .nav-link {
            font-size: 14px !important;
            padding: 10px 15px !important;
            margin: 1px 0 !important;
            border-radius: 6px !important;
        }

        .sidebar-section .nav-link:hover {
            background-color: rgba(255,255,255,0.15) !important;
            transform: translateX(-3px) !important;
        }

        .sidebar-section .nav-link i {
            font-size: 14px !important;
            width: 18px !important;
            margin-left: 8px !important;
        }

        .sidebar h4 {
            font-size: 24px !important;
            color: white !important;
            font-weight: 600 !important;
        }

        .card-header * {
            color: white !important;
            font-size: 18px !important;
        }

        .main-content * {
            color: #212529 !important;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            font-weight: 600 !important;
            color: inherit !important;
        }

        h1 { font-size: 32px !important; }
        h2 { font-size: 28px !important; }
        h3 { font-size: 24px !important; }
        h4 { font-size: 20px !important; }
        h5 { font-size: 18px !important; }
        h6 { font-size: 16px !important; }

        p {
            font-size: 16px !important;
            line-height: 1.6 !important;
            color: inherit !important;
        }

        .btn {
            font-size: 16px !important;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
            padding: 10px 20px !important;
        }

        /* Force icons to show */
        i, .fa, .fas, .far, .fab {
            font-family: "Font Awesome 6 Free" !important;
            font-style: normal !important;
            font-weight: 900 !important;
            display: inline-block !important;
            font-size: 16px !important;
            color: inherit !important;
        }
    </style>
</head>
<body>
    {% if current_user.is_authenticated %}
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">{{ company_name }}</h4>
                        <small class="text-white-50">{{ current_user.full_name }}</small>
                    </div>
                    
                    <!-- القسم الرئيسي - الإدارة العامة -->
                    <div class="sidebar-section">
                        <h6 class="sidebar-heading">الإدارة العامة</h6>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('dashboard.index') }}">
                                    <i class="fas fa-home"></i>
                                    <span>لوحة التحكم</span>
                                </a>
                            </li>

                            {% if current_user.has_permission('reports') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('reports.index') }}">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>التقارير والإحصائيات</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>

                    <!-- قسم المخزون والسيارات -->
                    <div class="sidebar-section">
                        <h6 class="sidebar-heading">المخزون والسيارات</h6>
                        <ul class="nav flex-column">
                            {% if current_user.has_permission('view_cars') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('cars.index') }}">
                                    <i class="fas fa-car"></i>
                                    <span>إدارة السيارات</span>
                                </a>
                            </li>
                            {% endif %}

                            {% if current_user.has_permission('view_cars') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('plate_numbers.index') }}">
                                    <i class="fas fa-id-card"></i>
                                    <span>أرقام السيارات القطرية</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>

                    <!-- قسم العملاء والمبيعات -->
                    <div class="sidebar-section">
                        <h6 class="sidebar-heading">العملاء والمبيعات</h6>
                        <ul class="nav flex-column">
                            {% if current_user.has_permission('view_customers') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('customers.index') }}">
                                    <i class="fas fa-users"></i>
                                    <span>إدارة العملاء</span>
                                </a>
                            </li>
                            {% endif %}

                            {% if current_user.has_permission('create_sales') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('sales.index') }}">
                                    <i class="fas fa-handshake"></i>
                                    <span>المبيعات والصفقات</span>
                                </a>
                            </li>
                            {% endif %}

                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('contracts.index') }}">
                                    <i class="fas fa-file-contract"></i>
                                    <span>العقود والاتفاقيات</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- قسم التواصل والتسويق -->
                    <div class="sidebar-section">
                        <h6 class="sidebar-heading">التواصل والتسويق</h6>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('whatsapp.index') }}">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>واتساب تيمبليتس</span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('notifications.index') }}">
                                    <i class="fas fa-bell position-relative"></i>
                                    <span>الإشعارات والتنبيهات</span>
                                    {% if unread_notifications > 0 %}
                                    <span class="notification-badge">{{ unread_notifications }}</span>
                                    {% endif %}
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <hr class="text-white-50">

                    <!-- قسم الحساب والإعدادات -->
                    <div class="sidebar-section">
                        <h6 class="sidebar-heading">الحساب والإعدادات</h6>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.profile') }}">
                                    <i class="fas fa-user"></i>
                                    <span>الملف الشخصي</span>
                                </a>
                            </li>

                            {% if current_user.has_permission('settings') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.users') }}">
                                    <i class="fas fa-cog"></i>
                                    <span>إعدادات النظام</span>
                                </a>
                            </li>
                            {% endif %}

                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>تسجيل الخروج</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
    <!-- Login page content -->
    {% block login_content %}{% endblock %}
    {% endif %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Font Manager -->
    <script src="{{ url_for('static', filename='js/font-manager.js') }}"></script>
    <!-- Number Formatter -->
    <script src="{{ url_for('static', filename='js/number-formatter.js') }}"></script>
    <!-- Input Validator -->
    <script src="{{ url_for('static', filename='js/input-validator.js') }}"></script>
    <!-- Simple Price Handler -->
    <script src="{{ url_for('static', filename='js/simple-price-handler.js') }}"></script>
    <!-- Arabic Numbers System -->
    <script src="{{ url_for('static', filename='js/arabic-numbers.js') }}"></script>
    <!-- Notifications Manager -->
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>

    <!-- Initialize Input Validator -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Input Validator for all forms
            if (!window.inputValidator) {
                window.inputValidator = new InputValidator();
                console.log('✅ InputValidator initialized from base.html');
            }

            // Ensure Simple Price Handler is working
            if (window.simplePriceHandler) {
                console.log('✅ Simple Price Handler is active');
            }
        });
    </script>

    <!-- Font Loading Optimization -->
    <script>
        // Font loading optimization
        document.documentElement.classList.add('font-loading');

        // Check if fonts are loaded
        if ('fonts' in document) {
            Promise.all([
                document.fonts.load('1rem Cairo'),
                document.fonts.load('1rem "Noto Sans Arabic"'),
                document.fonts.load('1rem "Noto Naskh Arabic"')
            ]).then(function() {
                document.documentElement.classList.remove('font-loading');
                document.documentElement.classList.add('fonts-loaded');
            }).catch(function() {
                // Fallback if font loading fails
                setTimeout(function() {
                    document.documentElement.classList.remove('font-loading');
                    document.documentElement.classList.add('fonts-loaded');
                }, 3000);
            });
        } else {
            // Fallback for browsers that don't support Font Loading API
            setTimeout(function() {
                document.documentElement.classList.remove('font-loading');
                document.documentElement.classList.add('fonts-loaded');
            }, 1000);
        }

        // Common utility functions
        function showFieldError(field, message) {
            field.addClass('is-invalid');
            field.next('.invalid-feedback').remove();
            field.after('<div class="invalid-feedback">' + message + '</div>');
        }

        function clearFieldError(field) {
            field.removeClass('is-invalid');
            field.next('.invalid-feedback').remove();
        }

        function saveFormData(formId) {
            const form = document.getElementById(formId);
            if (form) {
                const formData = new FormData(form);
                const data = {};
                for (let [key, value] of formData.entries()) {
                    data[key] = value;
                }
                localStorage.setItem(formId + '_data', JSON.stringify(data));
            }
        }

        function loadFormData(formId) {
            const savedData = localStorage.getItem(formId + '_data');
            if (savedData) {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const field = document.querySelector(`#${formId} [name="${key}"]`);
                    if (field && field.type !== 'file') {
                        field.value = data[key];
                    }
                });
            }
        }

        function clearFormData(formId) {
            localStorage.removeItem(formId + '_data');
        }

        // Number formatting for Arabic
        function formatArabicNumber(number) {
            return new Intl.NumberFormat('ar-QA').format(number);
        }

        // Currency formatting for Qatar
        function formatQatarCurrency(amount) {
            return new Intl.NumberFormat('ar-QA', {
                style: 'currency',
                currency: 'QAR'
            }).format(amount);
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
