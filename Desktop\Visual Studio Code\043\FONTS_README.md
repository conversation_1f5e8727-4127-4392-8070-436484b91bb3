# دليل الخطوط العربية - نظام إدارة معرض السيارات

## 📝 نظرة عامة

تم تحسين نظام الخطوط في التطبيق لدعم اللغة العربية بشكل مثالي مع التركيز على معايير قطر.

## 🔤 الخطوط المستخدمة

### الخطوط الأساسية:
1. **Cairo** - الخط الرئيسي للواجهة
2. **Noto Sans Arabic** - خط احتياطي من Google
3. **Noto Naskh Arabic** - للنصوص التقليدية
4. **Amiri** - للنصوص الكلاسيكية

### الخطوط الاحتياطية:
- Segoe UI
- Tahoma
- Arial
- sans-serif

## 📁 هيكل الملفات

```
static/
├── fonts/
│   ├── Cairo-Regular.ttf
│   ├── NotoNaskhArabic-Regular.ttf
│   ├── DejaVuSans.ttf (للتقارير)
│   └── ...
├── css/
│   ├── arabic-fonts.css (إعدادات الخطوط)
│   └── style.css (التنسيقات العامة)
└── js/
    └── font-manager.js (إدارة الخطوط)
```

## ⚙️ الإعدادات

### متغيرات CSS:
```css
:root {
    --font-primary: 'Cairo', 'Noto Sans Arabic', sans-serif;
    --font-secondary: 'Noto Naskh Arabic', 'Amiri', serif;
    --font-fallback: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}
```

### أحجام الخطوط:
- **xs**: 0.75rem (12px)
- **sm**: 0.875rem (14px)
- **base**: 1rem (16px)
- **lg**: 1.125rem (18px)
- **xl**: 1.25rem (20px)
- **2xl**: 1.5rem (24px)
- **3xl**: 1.875rem (30px)

## 🎨 فئات CSS المتاحة

### فئات النصوص:
- `.text-arabic` - للنصوص العربية التقليدية
- `.text-arabic-modern` - للنصوص العربية الحديثة
- `.text-arabic-title` - لعناوين النصوص العربية

### فئات الأرقام:
- `.number` - لتنسيق الأرقام
- `.currency` - لتنسيق العملة
- `.arabic-numerals` - للأرقام العربية

### فئات التحميل:
- `.font-loading` - أثناء تحميل الخطوط
- `.fonts-loaded` - بعد تحميل الخطوط

## 🚀 الميزات

### 1. تحميل محسن للخطوط:
- Preloading للخطوط الأساسية
- Font Loading API
- Fallback timeout
- Progressive enhancement

### 2. تحسينات الأداء:
- Font-display: swap
- Preconnect لـ Google Fonts
- تحميل غير متزامن
- تخزين مؤقت محسن

### 3. دعم الاستجابة:
- أحجام خطوط متكيفة للموبايل
- Line heights محسنة
- تباعد محسن للنصوص

### 4. إمكانية الوصول:
- دعم High contrast mode
- دعم Reduced motion
- Focus indicators واضحة
- Screen reader friendly

## 📱 التوافق مع الأجهزة

### الشاشات الكبيرة (Desktop):
- خط Cairo بحجم 16px
- Line height: 1.6
- Letter spacing محسن

### الأجهزة اللوحية (Tablet):
- خط Cairo بحجم 15px
- Line height: 1.5
- تباعد محسن

### الهواتف المحمولة (Mobile):
- خط Cairo بحجم 14px
- Line height: 1.6
- أزرار أكبر للمس

## 🔧 الاستخدام

### في HTML:
```html
<h1 class="text-arabic-title">عنوان رئيسي</h1>
<p class="text-arabic">نص عربي تقليدي</p>
<span class="currency">1500</span>
<div class="number">12345</div>
```

### في JavaScript:
```javascript
// تنسيق العملة
fontManager.formatQatarCurrency(1500); // "١٬٥٠٠ ر.ق"

// تنسيق الأرقام
fontManager.formatArabicNumber(12345); // "١٢٬٣٤٥"

// التحقق من تحميل الخطوط
if (fontManager.isLoaded()) {
    // الخطوط محملة
}
```

## 🎯 أفضل الممارسات

### 1. استخدام الفئات المناسبة:
- استخدم `.text-arabic-modern` للواجهات
- استخدم `.text-arabic` للمحتوى الطويل
- استخدم `.currency` للمبالغ المالية

### 2. تحسين الأداء:
- تجنب تحميل خطوط غير ضرورية
- استخدم font-display: swap
- قم بضغط ملفات الخطوط

### 3. إمكانية الوصول:
- تأكد من التباين الكافي
- استخدم أحجام خطوط مناسبة
- اختبر مع قارئات الشاشة

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

1. **الخطوط لا تظهر:**
   - تحقق من مسار الملفات
   - تأكد من تحميل CSS
   - فحص console للأخطاء

2. **بطء في التحميل:**
   - استخدم preload
   - قلل حجم ملفات الخطوط
   - استخدم CDN

3. **مشاكل في العرض:**
   - تحقق من font-feature-settings
   - تأكد من text-rendering
   - فحص line-height

## 📊 مراقبة الأداء

### مؤشرات مهمة:
- Font loading time
- First Contentful Paint (FCP)
- Cumulative Layout Shift (CLS)
- Time to Interactive (TTI)

### أدوات المراقبة:
- Chrome DevTools
- Google PageSpeed Insights
- WebPageTest
- Lighthouse

## 🔄 التحديثات المستقبلية

### خطط التطوير:
1. إضافة المزيد من أوزان الخطوط
2. دعم Variable fonts
3. تحسين تحميل الخطوط
4. إضافة خطوط إضافية للتخصص

### الصيانة:
- مراجعة دورية للأداء
- تحديث الخطوط عند الحاجة
- اختبار التوافق مع المتصفحات الجديدة

## 📞 الدعم

للمساعدة أو الاستفسارات حول الخطوط:
1. راجع هذا الدليل أولاً
2. تحقق من console للأخطاء
3. اختبر في متصفحات مختلفة
4. تواصل مع فريق التطوير

---

**ملاحظة:** هذا النظام محسن خصيصاً لمعايير قطر ويدعم الاتجاه من اليمين إلى اليسار (RTL) بشكل كامل.
