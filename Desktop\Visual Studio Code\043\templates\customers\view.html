{% extends "base.html" %}

{% block title %}{{ customer.full_name }} - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ customer.full_name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('edit_customers') %}
            <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            {% endif %}
            
            {% if current_user.has_permission('create_sales') %}
            <a href="{{ url_for('sales.add') }}?customer_id={{ customer.id }}" class="btn btn-success">
                <i class="fas fa-handshake me-2"></i>
                عملية بيع جديدة
            </a>
            {% endif %}
            
            {% if customer.whatsapp_number or customer.phone %}
            <a href="{{ url_for('whatsapp.send_message') }}?customer_id={{ customer.id }}" class="btn btn-outline-success">
                <i class="fab fa-whatsapp me-2"></i>
                واتساب
            </a>
            {% endif %}
        </div>
        <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- Customer Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">بيانات العميل</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td>{{ customer.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهوية:</strong></td>
                                <td><code>{{ customer.national_id }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        {{ customer.phone }}
                                    </a>
                                </td>
                            </tr>
                            {% if customer.whatsapp_number %}
                            <tr>
                                <td><strong>رقم الواتساب:</strong></td>
                                <td>
                                    <a href="https://wa.me/{{ customer.whatsapp_number.replace('+', '') }}" 
                                       target="_blank" class="text-success text-decoration-none">
                                        <i class="fab fa-whatsapp me-1"></i>
                                        {{ customer.whatsapp_number }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% if customer.email %}
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if customer.date_of_birth %}
                            <tr>
                                <td><strong>تاريخ الميلاد:</strong></td>
                                <td>{{ customer.date_of_birth|arabic_date }}</td>
                            </tr>
                            {% endif %}
                            {% if customer.occupation %}
                            <tr>
                                <td><strong>المهنة:</strong></td>
                                <td>{{ customer.occupation }}</td>
                            </tr>
                            {% endif %}
                            {% if customer.monthly_income %}
                            <tr>
                                <td><strong>الراتب الشهري:</strong></td>
                                <td>{{ customer.monthly_income|currency }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ customer.created_at|arabic_date }}</td>
                            </tr>
                            {% if customer.updated_at != customer.created_at %}
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ customer.updated_at|arabic_date }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if customer.address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>العنوان:</h6>
                        <p class="text-muted">{{ customer.address }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if customer.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>ملاحظات:</h6>
                        <p class="text-muted">{{ customer.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Guarantor Information -->
        {% if customer.guarantor_name %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">بيانات الضامن</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم الضامن:</strong></td>
                                <td>{{ customer.guarantor_name }}</td>
                            </tr>
                            {% if customer.guarantor_id %}
                            <tr>
                                <td><strong>رقم الهوية:</strong></td>
                                <td><code>{{ customer.guarantor_id }}</code></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% if customer.guarantor_phone %}
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    <a href="tel:{{ customer.guarantor_phone }}" class="text-decoration-none">
                                        {{ customer.guarantor_phone }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if customer.guarantor_address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>عنوان الضامن:</h6>
                        <p class="text-muted">{{ customer.guarantor_address }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Sales History -->
        {% if sales %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تاريخ المبيعات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>السيارة</th>
                                <th>نوع البيع</th>
                                <th>السعر</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>تاريخ البيع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('cars.view', car_id=sale.car.id) }}" class="text-decoration-none">
                                        {{ sale.car.brand }} {{ sale.car.model }} {{ sale.car.year }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                        {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                                    </span>
                                </td>
                                <td>{{ sale.sale_price|currency }}</td>
                                <td>
                                    {% set total_paid = sale.payments|sum(attribute='amount') + sale.down_payment %}
                                    {{ total_paid|currency }}
                                </td>
                                <td>
                                    {% set remaining = sale.sale_price - total_paid %}
                                    <span class="text-{{ 'success' if remaining <= 0 else 'warning' }}">
                                        {{ remaining|currency }}
                                    </span>
                                </td>
                                <td>{{ sale.sale_date|arabic_date }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.status == 'active' else 'secondary' }}">
                                        {{ 'نشط' if sale.status == 'active' else sale.status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('sales.view', sale_id=sale.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if sale.sale_type == 'installment' %}
                                        <a href="{{ url_for('sales.installments', sale_id=sale.id) }}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-calendar-alt"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('contracts.preview_contract', sale_id=sale.id) }}" 
                                           class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-file-contract"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Customer Photo -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">صورة العميل</h5>
            </div>
            <div class="card-body text-center">
                {% if customer.photo_path %}
                <img src="{{ url_for('static', filename=customer.photo_path) }}" 
                     class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;" 
                     alt="صورة العميل">
                {% else %}
                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" 
                     style="width: 150px; height: 150px; font-size: 3rem;">
                    <i class="fas fa-user"></i>
                </div>
                {% endif %}
                <h6>{{ customer.full_name }}</h6>
                <p class="text-muted">{{ customer.national_id }}</p>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ sales|length }}</h4>
                        <small class="text-muted">مبيعات</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ sales|sum(attribute='sale_price')|currency }}</h4>
                        <small class="text-muted">إجمالي المبيعات</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Documents -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">المستندات</h5>
            </div>
            <div class="card-body">
                {% if customer.id_copy_path %}
                <div class="mb-3">
                    <label class="form-label">صورة الهوية:</label>
                    <a href="{{ url_for('static', filename=customer.id_copy_path) }}" 
                       target="_blank" class="btn btn-outline-primary btn-sm d-block">
                        <i class="fas fa-file-image me-2"></i>
                        عرض صورة الهوية
                    </a>
                </div>
                {% endif %}
                
                {% if customer.guarantor_id_copy_path %}
                <div class="mb-3">
                    <label class="form-label">صورة هوية الضامن:</label>
                    <a href="{{ url_for('static', filename=customer.guarantor_id_copy_path) }}" 
                       target="_blank" class="btn btn-outline-info btn-sm d-block">
                        <i class="fas fa-file-image me-2"></i>
                        عرض هوية الضامن
                    </a>
                </div>
                {% endif %}
                
                {% if not customer.id_copy_path and not customer.guarantor_id_copy_path %}
                <p class="text-muted text-center">لا توجد مستندات مرفوعة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add tooltips
    $('[title]').tooltip();
});
</script>
{% endblock %}
