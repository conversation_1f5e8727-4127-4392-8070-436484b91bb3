{% extends "base.html" %}

{% block title %}خطأ في الخادم - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
    <div class="text-center">
        <div class="error-code" style="font-size: 8rem; font-weight: bold; color: #dc3545; text-shadow: 2px 2px 4px rgba(0,0,0,0.1);">
            500
        </div>
        <h1 class="display-4 mb-4">خطأ في الخادم</h1>
        <p class="lead mb-4">عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى.</p>
        
        <div class="mb-4">
            <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
        </div>
        
        <div class="d-flex justify-content-center gap-3">
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
            <button onclick="location.reload()" class="btn btn-outline-secondary">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </button>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                إذا استمر هذا الخطأ، يرجى الاتصال بالدعم الفني
            </small>
        </div>
    </div>
</div>

<style>
    .error-code {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>
{% endblock %}
