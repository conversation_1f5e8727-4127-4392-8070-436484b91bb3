<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التخطيط - نظام معرض السيارات</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* FORCE EVERYTHING TO WORK */
        * {
            box-sizing: border-box !important;
            direction: rtl !important;
        }
        
        html, body {
            direction: rtl !important;
            text-align: right !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
            font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
            background-color: #f8f9fa !important;
        }
        
        .container-fluid {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            direction: rtl !important;
        }
        
        .row {
            display: flex !important;
            flex-wrap: wrap !important;
            width: 100% !important;
            margin: 0 !important;
            direction: rtl !important;
        }
        
        .sidebar {
            min-height: 100vh !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
            position: relative !important;
            width: 100% !important;
            padding: 20px 0 !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .col-md-3, .col-lg-2 {
            flex: 0 0 250px !important;
            max-width: 250px !important;
            width: 250px !important;
            padding: 0 !important;
        }
        
        .col-md-9, .col-lg-10 {
            flex: 1 !important;
            max-width: calc(100% - 250px) !important;
            width: calc(100% - 250px) !important;
            padding: 20px !important;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8) !important;
            padding: 12px 20px !important;
            margin: 2px 10px !important;
            border-radius: 8px !important;
            display: block !important;
            text-decoration: none !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1) !important;
            color: white !important;
        }
        
        .main-content {
            padding: 20px !important;
            direction: rtl !important;
            text-align: right !important;
            min-height: 100vh !important;
            background-color: #f8f9fa !important;
        }
        
        .card {
            border-radius: 15px !important;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
            margin-bottom: 20px !important;
            background: white !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .card-body {
            padding: 20px !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .btn {
            border-radius: 8px !important;
            padding: 10px 20px !important;
            direction: rtl !important;
            text-align: center !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
        }
        
        .text-white {
            color: white !important;
        }
        
        .text-center {
            text-align: center !important;
        }
        
        .mb-3 {
            margin-bottom: 1rem !important;
        }
        
        .mb-4 {
            margin-bottom: 1.5rem !important;
        }
        
        .pt-3 {
            padding-top: 1rem !important;
        }
        
        .d-md-block {
            display: block !important;
        }
        
        .position-sticky {
            position: sticky !important;
            top: 0 !important;
        }
        
        .nav {
            display: flex !important;
            flex-direction: column !important;
            padding-left: 0 !important;
            margin-bottom: 0 !important;
            list-style: none !important;
        }
        
        .nav-item {
            display: list-item !important;
        }
        
        .collapse {
            display: block !important;
        }
        
        /* Test colors */
        .test-red {
            background-color: #dc3545 !important;
            color: white !important;
            padding: 20px !important;
            margin: 10px 0 !important;
            border-radius: 8px !important;
        }
        
        .test-green {
            background-color: #28a745 !important;
            color: white !important;
            padding: 20px !important;
            margin: 10px 0 !important;
            border-radius: 8px !important;
        }
        
        .test-blue {
            background-color: #007bff !important;
            color: white !important;
            padding: 20px !important;
            margin: 10px 0 !important;
            border-radius: 8px !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">نظام معرض السيارات</h4>
                        <small class="text-white" style="opacity: 0.8;">اختبار التخطيط</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-tachometer-alt" style="margin-left: 10px;"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-car" style="margin-left: 10px;"></i>
                                السيارات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-id-card" style="margin-left: 10px;"></i>
                                أرقام السيارات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-users" style="margin-left: 10px;"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-handshake" style="margin-left: 10px;"></i>
                                المبيعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-file-contract" style="margin-left: 10px;"></i>
                                العقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fas fa-chart-bar" style="margin-left: 10px;"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="fab fa-whatsapp" style="margin-left: 10px;"></i>
                                واتساب
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 col-lg-10 main-content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">
                            <i class="fas fa-check-circle" style="margin-left: 10px;"></i>
                            اختبار التخطيط نجح!
                        </h3>
                    </div>
                    <div class="card-body">
                        <p>إذا كنت ترى هذه الصفحة بشكل صحيح، فهذا يعني أن التخطيط يعمل!</p>
                        
                        <div class="test-red">
                            <h5>الشريط الجانبي</h5>
                            <p>يجب أن يكون على اليمين باللون الأزرق المتدرج</p>
                        </div>
                        
                        <div class="test-green">
                            <h5>المحتوى الرئيسي</h5>
                            <p>يجب أن يكون في الوسط مع خلفية رمادية فاتحة</p>
                        </div>
                        
                        <div class="test-blue">
                            <h5>اتجاه RTL</h5>
                            <p>النص يجب أن يكون من اليمين إلى اليسار</p>
                        </div>
                        
                        <div class="text-center" style="margin-top: 30px;">
                            <a href="/" class="btn btn-primary">
                                <i class="fas fa-home" style="margin-left: 8px;"></i>
                                العودة للصفحة الرئيسية
                            </a>
                            <a href="/emergency-fix" class="btn btn-primary" style="margin-right: 10px;">
                                <i class="fas fa-tools" style="margin-left: 8px;"></i>
                                صفحة الإصلاح
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Test Grid -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5>عمود 1</h5>
                                <p>هذا عمود اختبار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5>عمود 2</h5>
                                <p>هذا عمود اختبار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5>عمود 3</h5>
                                <p>هذا عمود اختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ صفحة اختبار التخطيط تم تحميلها');
            console.log('✅ jQuery:', typeof jQuery !== 'undefined' ? 'محمل' : 'غير محمل');
            console.log('✅ Bootstrap:', typeof bootstrap !== 'undefined' ? 'محمل' : 'غير محمل');
            console.log('✅ اتجاه الصفحة:', document.dir || document.documentElement.dir);
            console.log('✅ عرض الشاشة:', window.innerWidth + 'px');
        });
    </script>
</body>
</html>
