// Car Dealership Management System - JavaScript Functions

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Confirm delete actions
    $('.delete-btn').on('click', function(e) {
        if (!confirm('هل أنت متأكد من الحذف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
        }
    });

    // Format currency inputs
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        $(this).val(value);
    });

    // Format phone number inputs
    $('.phone-input').on('input', function() {
        let value = $(this).val().replace(/[^\d+]/g, '');
        $(this).val(value);
    });

    // Auto-calculate installment amount
    $('#sale_price, #down_payment, #installment_count').on('input', function() {
        calculateInstallmentAmount();
    });

    // Image preview functionality
    $('.image-input').on('change', function() {
        previewImages(this);
    });

    // Search functionality with debounce
    let searchTimeout;
    $('.search-input').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        const searchUrl = $(this).data('search-url');
        
        searchTimeout = setTimeout(function() {
            if (searchTerm.length >= 2) {
                performSearch(searchTerm, searchUrl);
            }
        }, 300);
    });

    // Mobile sidebar toggle
    $('.sidebar-toggle').on('click', function() {
        $('.sidebar').toggleClass('show');
    });

    // Close sidebar when clicking outside on mobile
    $(document).on('click', function(e) {
        if ($(window).width() <= 768) {
            if (!$(e.target).closest('.sidebar, .sidebar-toggle').length) {
                $('.sidebar').removeClass('show');
            }
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        if (!validateForm(this)) {
            e.preventDefault();
        }
    });

    // Auto-save form data to localStorage
    $('form input, form select, form textarea').on('change', function() {
        const formId = $(this).closest('form').attr('id');
        if (formId) {
            saveFormData(formId);
        }
    });

    // Load saved form data
    $('form[id]').each(function() {
        loadFormData($(this).attr('id'));
    });
});

// Calculate installment amount
function calculateInstallmentAmount() {
    const salePrice = parseFloat($('#sale_price').val()) || 0;
    const downPayment = parseFloat($('#down_payment').val()) || 0;
    const installmentCount = parseInt($('#installment_count').val()) || 1;
    
    const remainingAmount = salePrice - downPayment;
    const installmentAmount = remainingAmount / installmentCount;
    
    if (installmentAmount > 0) {
        $('#installment_amount').val(installmentAmount.toFixed(2));
        $('#installment_amount_display').text(formatCurrency(installmentAmount));
    }
}

// Preview uploaded images
function previewImages(input) {
    const previewContainer = $(input).siblings('.image-preview');
    previewContainer.empty();
    
    if (input.files) {
        Array.from(input.files).forEach(file => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = $('<img>')
                        .attr('src', e.target.result)
                        .addClass('img-thumbnail me-2 mb-2')
                        .css({'width': '100px', 'height': '100px', 'object-fit': 'cover'});
                    previewContainer.append(img);
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Perform AJAX search
function performSearch(searchTerm, searchUrl) {
    $.ajax({
        url: searchUrl,
        method: 'GET',
        data: { q: searchTerm },
        success: function(data) {
            displaySearchResults(data);
        },
        error: function() {
            console.error('Search failed');
        }
    });
}

// Display search results
function displaySearchResults(results) {
    const resultsContainer = $('.search-results');
    resultsContainer.empty();
    
    if (results.length > 0) {
        results.forEach(result => {
            const item = $('<div>')
                .addClass('search-result-item p-2 border-bottom')
                .text(result.display_name)
                .data('id', result.id)
                .on('click', function() {
                    selectSearchResult(result);
                });
            resultsContainer.append(item);
        });
        resultsContainer.show();
    } else {
        resultsContainer.hide();
    }
}

// Select search result
function selectSearchResult(result) {
    // This function should be customized based on the specific search context
    console.log('Selected:', result);
    $('.search-results').hide();
}

// Form validation
function validateForm(form) {
    let isValid = true;
    
    // Check required fields
    $(form).find('[required]').each(function() {
        if (!$(this).val().trim()) {
            showFieldError($(this), 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            clearFieldError($(this));
        }
    });
    
    // Validate email fields
    $(form).find('input[type="email"]').each(function() {
        const email = $(this).val().trim();
        if (email && !isValidEmail(email)) {
            showFieldError($(this), 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
    });
    
    // Validate phone fields
    $(form).find('.phone-input').each(function() {
        const phone = $(this).val().trim();
        if (phone && !isValidPhone(phone)) {
            showFieldError($(this), 'رقم الهاتف غير صحيح');
            isValid = false;
        }
    });
    
    return isValid;
}

// Show field error
function showFieldError(field, message) {
    field.addClass('is-invalid');
    field.siblings('.invalid-feedback').remove();
    field.after('<div class="invalid-feedback">' + message + '</div>');
}

// Clear field error
function clearFieldError(field) {
    field.removeClass('is-invalid');
    field.siblings('.invalid-feedback').remove();
}

// Validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate phone
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

// Save form data to localStorage
function saveFormData(formId) {
    const formData = {};
    $(`#${formId} input, #${formId} select, #${formId} textarea`).each(function() {
        if ($(this).attr('type') !== 'password' && $(this).attr('type') !== 'file') {
            formData[$(this).attr('name')] = $(this).val();
        }
    });
    localStorage.setItem(`form_${formId}`, JSON.stringify(formData));
}

// Load form data from localStorage
function loadFormData(formId) {
    const savedData = localStorage.getItem(`form_${formId}`);
    if (savedData) {
        const formData = JSON.parse(savedData);
        Object.keys(formData).forEach(key => {
            $(`#${formId} [name="${key}"]`).val(formData[key]);
        });
    }
}

// Clear saved form data
function clearSavedFormData(formId) {
    localStorage.removeItem(`form_${formId}`);
}

// Show loading spinner
function showLoading(element) {
    const spinner = '<div class="spinner"></div>';
    $(element).html(spinner);
}

// Hide loading spinner
function hideLoading(element, originalContent) {
    $(element).html(originalContent);
}

// Show notification
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'danger' : type;
    const alert = $(`
        <div class="alert alert-${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    setTimeout(() => {
        alert.fadeOut(() => alert.remove());
    }, 5000);
}

// Confirm action
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// Print functionality
function printElement(elementId) {
    const printContent = document.getElementById(elementId);
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = printContent.innerHTML;
    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
}

// Export table to CSV
function exportTableToCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = Array.from(table.querySelectorAll('tr'));
    
    const csvContent = rows.map(row => {
        const cells = Array.from(row.querySelectorAll('th, td'));
        return cells.map(cell => `"${cell.textContent.trim()}"`).join(',');
    }).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}
