{% extends "base.html" %}

{% block title %}التقارير - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">التقارير والإحصائيات</h1>
</div>

<div class="row">
    <!-- Sales Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    تقارير المبيعات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">عرض وتحليل بيانات المبيعات والإيرادات</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>تقارير المبيعات اليومية والشهرية</li>
                    <li><i class="fas fa-check text-success me-2"></i>مقارنة المبيعات النقدية والتقسيط</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحليل الأداء حسب الفترة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تصدير البيانات إلى Excel</li>
                </ul>
                <a href="{{ url_for('reports.sales_report') }}" class="btn btn-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    عرض تقرير المبيعات
                </a>
            </div>
        </div>
    </div>

    <!-- Installments Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    تقارير الأقساط
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">متابعة الأقساط والمدفوعات</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>الأقساط المستحقة والمتأخرة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تقارير المدفوعات</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحليل التدفق النقدي</li>
                    <li><i class="fas fa-check text-success me-2"></i>إحصائيات الالتزام بالسداد</li>
                </ul>
                <a href="{{ url_for('reports.installments_report') }}" class="btn btn-warning">
                    <i class="fas fa-calendar-alt me-2"></i>
                    عرض تقرير الأقساط
                </a>
            </div>
        </div>
    </div>

    <!-- Cars Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-car me-2"></i>
                    تقارير السيارات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إحصائيات المخزون والسيارات</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>السيارات المتاحة والمباعة</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحليل المخزون حسب الماركة</li>
                    <li><i class="fas fa-check text-success me-2"></i>قيمة المخزون الحالي</li>
                    <li><i class="fas fa-check text-success me-2"></i>معدل دوران المخزون</li>
                </ul>
                <a href="{{ url_for('reports.cars_report') }}" class="btn btn-info">
                    <i class="fas fa-car me-2"></i>
                    عرض تقرير السيارات
                </a>
            </div>
        </div>
    </div>

    <!-- Financial Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-dollar-sign me-2"></i>
                    التقارير المالية
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تحليل الوضع المالي والربحية</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>الإيرادات والمصروفات</li>
                    <li><i class="fas fa-check text-success me-2"></i>تحليل الربحية</li>
                    <li><i class="fas fa-check text-success me-2"></i>التدفق النقدي</li>
                    <li><i class="fas fa-check text-success me-2"></i>طرق الدفع المختلفة</li>
                </ul>
                <a href="{{ url_for('reports.financial_report') }}" class="btn btn-success">
                    <i class="fas fa-dollar-sign me-2"></i>
                    عرض التقرير المالي
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h3 class="text-primary">{{ stats.cars.total if stats else '0' }}</h3>
                            <p class="text-muted mb-0">إجمالي السيارات</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h3 class="text-success">{{ stats.sales.this_month if stats else '0' }}</h3>
                            <p class="text-muted mb-0">مبيعات هذا الشهر</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h3 class="text-warning">{{ stats.installments.pending if stats else '0' }}</h3>
                            <p class="text-muted mb-0">أقساط معلقة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-info">{{ stats.customers.total if stats else '0' }}</h3>
                        <p class="text-muted mb-0">إجمالي العملاء</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">خيارات التصدير</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">يمكنك تصدير التقارير بصيغ مختلفة للاستخدام الخارجي</p>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button class="btn btn-outline-success" onclick="exportData('excel')">
                                <i class="fas fa-file-excel me-2"></i>
                                تصدير إلى Excel
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button class="btn btn-outline-danger" onclick="exportData('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>
                                تصدير إلى PDF
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button class="btn btn-outline-info" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>
                                طباعة التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportData(format) {
    // This would be implemented based on the specific export requirements
    alert('سيتم تنفيذ تصدير البيانات بصيغة ' + format);
}

$(document).ready(function() {
    // Add some animation to the cards
    $('.card').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
});
</script>
{% endblock %}
