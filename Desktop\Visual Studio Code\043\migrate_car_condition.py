#!/usr/bin/env python3
"""
Migration script to add car condition fields to existing database
"""

import sqlite3
import os

def migrate_database():
    """Add new columns to the car table"""
    db_path = 'instance/car_dealership.db'

    if not os.path.exists(db_path):
        print("Database file not found. Please run the application first to create the database.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(car)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add car_type column if it doesn't exist
        if 'car_type' not in columns:
            cursor.execute("ALTER TABLE car ADD COLUMN car_type VARCHAR(20) DEFAULT 'used'")
            print("✅ Added car_type column")
        else:
            print("ℹ️  car_type column already exists")
        
        # Add condition_rating column if it doesn't exist
        if 'condition_rating' not in columns:
            cursor.execute("ALTER TABLE car ADD COLUMN condition_rating VARCHAR(20)")
            print("✅ Added condition_rating column")
        else:
            print("ℹ️  condition_rating column already exists")
        
        # Add condition_notes column if it doesn't exist
        if 'condition_notes' not in columns:
            cursor.execute("ALTER TABLE car ADD COLUMN condition_notes TEXT")
            print("✅ Added condition_notes column")
        else:
            print("ℹ️  condition_notes column already exists")
        
        # Update existing cars with default values
        cursor.execute("""
            UPDATE car 
            SET car_type = 'used', 
                condition_rating = CASE 
                    WHEN mileage = 0 OR mileage IS NULL THEN 'excellent'
                    WHEN mileage < 50000 THEN 'very_good'
                    WHEN mileage < 100000 THEN 'good'
                    WHEN mileage < 150000 THEN 'fair'
                    ELSE 'poor'
                END
            WHERE car_type IS NULL OR condition_rating IS NULL
        """)
        
        updated_rows = cursor.rowcount
        if updated_rows > 0:
            print(f"✅ Updated {updated_rows} existing cars with default condition values")
        
        conn.commit()
        print("🎉 Migration completed successfully!")
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 Starting car condition migration...")
    migrate_database()
