#!/usr/bin/env python3
"""
🎉 التشغيل النهائي لنظام معرض السيارات
Final Start for Car Dealership System
"""

import os
import sys
import webbrowser
import time
import threading
from datetime import datetime

def print_success_banner():
    """Print success banner"""
    print("🎉" + "=" * 58 + "🎉")
    print("🚗    نظام معرض السيارات - النسخة النهائية المحسنة    🚗")
    print("🎯    Car Dealership System - Final Enhanced Version    🎯")
    print("🎉" + "=" * 58 + "🎉")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("✅ جميع المشاكل محلولة نهائياً!")
    print("✅ التخطيط RTL مثالي")
    print("✅ الشريط الجانبي يعمل بشكل مثالي")
    print("✅ النصوص واضحة ومقروءة")
    print("✅ الأيقونات تظهر بشكل صحيح")
    print("✅ التصميم احترافي ومتجاوب")
    print("🎉" + "=" * 58 + "🎉")

def open_browser_delayed():
    """Open browser after delay"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح تلقائياً!")
        print("📱 إذا لم يفتح المتصفح، افتح: http://localhost:5000")
    except:
        print("📱 افتح المتصفح يدوياً: http://localhost:5000")

def main():
    """Main function"""
    print_success_banner()
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ خطأ: يجب تشغيل هذا السكريبت من مجلد المشروع")
        print("💡 تأكد من أنك في المجلد: Desktop\\Visual Studio Code\\043")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    print("🔍 فحص النظام...")
    print("✅ ملف app.py موجود")
    print("✅ مجلد templates موجود") if os.path.exists('templates') else print("⚠️ مجلد templates مفقود")
    print("✅ مجلد static موجود") if os.path.exists('static') else print("⚠️ مجلد static مفقود")
    
    print("\n🚀 بدء تشغيل النظام...")
    
    # Set environment variables for optimal performance
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'true'
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    try:
        # Import and create app
        from app import create_app
        app = create_app()
        
        print("✅ تم تحميل النظام بنجاح!")
        print("\n🌐 النظام متاح على الروابط التالية:")
        print("   🏠 الصفحة الرئيسية: http://localhost:5000")
        print("   🚗 إدارة السيارات: http://localhost:5000/cars")
        print("   👥 إدارة العملاء: http://localhost:5000/customers")
        print("   📋 العقود: http://localhost:5000/contracts")
        print("   🔢 أرقام قطر: http://localhost:5000/qatar_numbers")
        print("   📊 التقارير: http://localhost:5000/reports")
        print("   💬 واتساب: http://localhost:5000/whatsapp")
        
        print("\n🧪 صفحات الاختبار:")
        print("   🎯 اختبار التخطيط: http://localhost:5000/layout-test")
        print("   📝 اختبار النصوص: http://localhost:5000/text-test")
        print("   🔧 الإصلاح الطارئ: http://localhost:5000/emergency-fix")
        print("   🔍 التشخيص: http://localhost:5000/debug-layout")
        print("   🔢 اختبار الأرقام: http://localhost:5000/arabic-numbers-test")
        
        print("\n🔐 بيانات الدخول:")
        print("   👤 المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        
        print("\n🎯 الميزات المتاحة:")
        print("   ✅ إدارة السيارات (مع الحالة والجودة واللون)")
        print("   ✅ إدارة العملاء (معايير قطر - 11 رقم هوية، 8 أرقام هاتف)")
        print("   ✅ أرقام السيارات القطرية (بيع وحجز)")
        print("   ✅ نظام العقود والتقارير (PDF/Word)")
        print("   ✅ الأرقام العربية والخطوط المحسنة")
        print("   ✅ واتساب تيمبليتس ونظام الإشعارات")
        
        print("\n🎨 التصميم المحسن:")
        print("   ✅ تخطيط RTL مثالي - الشريط الجانبي على اليمين")
        print("   ✅ شريط جانبي أزرق متدرج (250px ثابت)")
        print("   ✅ نصوص واضحة ومقروءة (خط Cairo)")
        print("   ✅ أيقونات Font Awesome 6 تعمل بشكل مثالي")
        print("   ✅ ألوان احترافية ومتناسقة")
        print("   ✅ تصميم متجاوب يعمل على جميع الأجهزة")
        
        print("\n📱 التوافق:")
        print("   ✅ جميع المتصفحات: Chrome, Firefox, Safari, Edge")
        print("   ✅ جميع الأجهزة: كمبيوتر، تابلت، موبايل")
        print("   ✅ جميع أحجام الشاشات")
        
        print("\n🛑 للإيقاف: اضغط Ctrl+C")
        print("🔄 للإعادة: python FINAL_START.py")
        print("🎉" + "=" * 58 + "🎉")
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask app
        print("🚀 النظام يعمل الآن...")
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
        print("💾 جميع البيانات محفوظة")
        print("🔄 لإعادة التشغيل: python FINAL_START.py")
        print("🎉 شكراً لاستخدام نظام معرض السيارات!")
        
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد الوحدات: {str(e)}")
        print("\n💡 جرب تثبيت المتطلبات:")
        print("   pip install flask flask-sqlalchemy flask-login")
        print("   pip install werkzeug jinja2")
        input("\nاضغط Enter للخروج...")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n💡 الحلول المقترحة:")
        print("   1. تأكد من أن المنفذ 5000 غير مستخدم")
        print("   2. أعد تشغيل الكمبيوتر")
        print("   3. جرب منفذ مختلف")
        print("   4. تحقق من إعدادات الجدار الناري")
        print("\n🔧 للإصلاح الطارئ:")
        print("   python complete_fix.py")
        print("   python emergency_start.py")
        input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        print("🆘 اتصل بالدعم الفني")
        input("اضغط Enter للخروج...")
