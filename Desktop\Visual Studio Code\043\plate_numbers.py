from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, PlateNumber, PlateNumberSale, PlateNumberBid, PlateNumberPayment, Customer
from datetime import datetime, date
import json
import os
from werkzeug.utils import secure_filename

plate_numbers_bp = Blueprint('plate_numbers', __name__, url_prefix='/plate-numbers')

# Configuration
UPLOAD_FOLDER = 'static/uploads/plate_numbers'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_files(files):
    """Save uploaded files and return list of file paths"""
    if not files:
        return []
    
    # Create upload directory if it doesn't exist
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    
    file_paths = []
    for file in files:
        if file and file.filename and allowed_file(file.filename):
            # Generate unique filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{secure_filename(file.filename)}"
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(file_path)
            # Store relative path for database
            file_paths.append(f"uploads/plate_numbers/{filename}")
    
    return file_paths

@plate_numbers_bp.route('/')
@login_required
def index():
    """Display all plate numbers"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    status = request.args.get('status', '')
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')
    
    # Build query
    query = PlateNumber.query
    
    # Apply filters
    if search:
        query = query.filter(
            PlateNumber.number.contains(search) |
            PlateNumber.description.contains(search)
        )
    
    if category:
        query = query.filter(PlateNumber.category == category)
    
    if status:
        query = query.filter(PlateNumber.status == status)
    
    # Apply sorting
    if sort_order == 'desc':
        query = query.order_by(getattr(PlateNumber, sort_by).desc())
    else:
        query = query.order_by(getattr(PlateNumber, sort_by))
    
    # Paginate
    plate_numbers = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get statistics
    stats = {
        'total': PlateNumber.query.count(),
        'available': PlateNumber.query.filter_by(status='available').count(),
        'sold': PlateNumber.query.filter_by(status='sold').count(),
        'reserved': PlateNumber.query.filter_by(status='reserved').count(),
        'auction': PlateNumber.query.filter_by(is_auction=True, status='available').count()
    }
    
    return render_template('plate_numbers/index.html', 
                         plate_numbers=plate_numbers, 
                         stats=stats,
                         search=search,
                         category=category,
                         status=status,
                         sort_by=sort_by,
                         sort_order=sort_order)

@plate_numbers_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """Add new plate number"""
    if request.method == 'POST':
        try:
            # Get form data
            number = request.form.get('number', '').strip()
            category = request.form.get('category')
            type = request.form.get('type')
            price = float(request.form.get('price', 0))
            cost_price = float(request.form.get('cost_price', 0)) if request.form.get('cost_price') else None
            description = request.form.get('description', '').strip()
            
            # Qatar specific fields
            code_letter = request.form.get('code_letter', '').strip()
            series = request.form.get('series', '').strip()
            
            # Special features
            is_sequential = 'is_sequential' in request.form
            is_repeated = 'is_repeated' in request.form
            is_mirror = 'is_mirror' in request.form
            is_birthday = 'is_birthday' in request.form
            is_lucky = 'is_lucky' in request.form
            
            # Auction fields
            is_auction = 'is_auction' in request.form
            starting_bid = float(request.form.get('starting_bid', 0)) if request.form.get('starting_bid') else None
            reserve_price = float(request.form.get('reserve_price', 0)) if request.form.get('reserve_price') else None
            auction_start_date = None
            auction_end_date = None
            
            if is_auction:
                if request.form.get('auction_start_date'):
                    auction_start_date = datetime.strptime(request.form.get('auction_start_date'), '%Y-%m-%dT%H:%M')
                if request.form.get('auction_end_date'):
                    auction_end_date = datetime.strptime(request.form.get('auction_end_date'), '%Y-%m-%dT%H:%M')
            
            # Handle file uploads
            files = request.files.getlist('images')
            image_paths = save_uploaded_files(files)
            
            # Calculate digits count
            digits_count = len([c for c in number if c.isdigit()])
            
            # Create special features JSON
            special_features = {
                'is_sequential': is_sequential,
                'is_repeated': is_repeated,
                'is_mirror': is_mirror,
                'is_birthday': is_birthday,
                'is_lucky': is_lucky
            }
            
            # Create new plate number
            plate_number = PlateNumber(
                number=number,
                category=category,
                type=type,
                digits_count=digits_count,
                price=price,
                cost_price=cost_price,
                description=description,
                code_letter=code_letter,
                series=series,
                is_sequential=is_sequential,
                is_repeated=is_repeated,
                is_mirror=is_mirror,
                is_birthday=is_birthday,
                is_lucky=is_lucky,
                is_auction=is_auction,
                starting_bid=starting_bid,
                current_bid=starting_bid if is_auction else None,
                reserve_price=reserve_price,
                auction_start_date=auction_start_date,
                auction_end_date=auction_end_date,
                special_features=json.dumps(special_features),
                images=json.dumps(image_paths),
                created_by=current_user.id
            )
            
            db.session.add(plate_number)
            db.session.commit()
            
            flash(f'تم إضافة رقم اللوحة {number} بنجاح', 'success')
            return redirect(url_for('plate_numbers.index'))
            
        except ValueError as e:
            flash('خطأ في البيانات المدخلة. تأكد من صحة الأرقام.', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة رقم اللوحة: {str(e)}', 'error')
    
    return render_template('plate_numbers/add.html')

@plate_numbers_bp.route('/<int:id>')
@login_required
def view(id):
    """View plate number details"""
    plate_number = PlateNumber.query.get_or_404(id)
    
    # Get sales history
    sales = PlateNumberSale.query.filter_by(plate_number_id=id).order_by(PlateNumberSale.created_at.desc()).all()
    
    # Get bids if auction
    bids = []
    if plate_number.is_auction:
        bids = PlateNumberBid.query.filter_by(plate_number_id=id).order_by(PlateNumberBid.bid_date.desc()).all()
    
    # Parse images
    images = []
    if plate_number.images:
        try:
            images = json.loads(plate_number.images)
        except:
            images = []
    
    return render_template('plate_numbers/view.html', 
                         plate_number=plate_number, 
                         sales=sales, 
                         bids=bids,
                         images=images)

@plate_numbers_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """Edit plate number"""
    plate_number = PlateNumber.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # Update fields
            plate_number.number = request.form.get('number', '').strip()
            plate_number.category = request.form.get('category')
            plate_number.type = request.form.get('type')
            plate_number.price = float(request.form.get('price', 0))
            plate_number.cost_price = float(request.form.get('cost_price', 0)) if request.form.get('cost_price') else None
            plate_number.description = request.form.get('description', '').strip()
            plate_number.status = request.form.get('status')
            
            # Qatar specific fields
            plate_number.code_letter = request.form.get('code_letter', '').strip()
            plate_number.series = request.form.get('series', '').strip()
            
            # Special features
            plate_number.is_sequential = 'is_sequential' in request.form
            plate_number.is_repeated = 'is_repeated' in request.form
            plate_number.is_mirror = 'is_mirror' in request.form
            plate_number.is_birthday = 'is_birthday' in request.form
            plate_number.is_lucky = 'is_lucky' in request.form
            
            # Update digits count
            plate_number.digits_count = len([c for c in plate_number.number if c.isdigit()])
            
            # Handle new file uploads
            files = request.files.getlist('images')
            if files and files[0].filename:
                new_image_paths = save_uploaded_files(files)
                # Merge with existing images
                existing_images = []
                if plate_number.images:
                    try:
                        existing_images = json.loads(plate_number.images)
                    except:
                        existing_images = []
                
                all_images = existing_images + new_image_paths
                plate_number.images = json.dumps(all_images)
            
            # Update special features JSON
            special_features = {
                'is_sequential': plate_number.is_sequential,
                'is_repeated': plate_number.is_repeated,
                'is_mirror': plate_number.is_mirror,
                'is_birthday': plate_number.is_birthday,
                'is_lucky': plate_number.is_lucky
            }
            plate_number.special_features = json.dumps(special_features)
            
            db.session.commit()
            flash(f'تم تحديث رقم اللوحة {plate_number.number} بنجاح', 'success')
            return redirect(url_for('plate_numbers.view', id=id))
            
        except ValueError as e:
            flash('خطأ في البيانات المدخلة. تأكد من صحة الأرقام.', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث رقم اللوحة: {str(e)}', 'error')
    
    # Parse images for display
    images = []
    if plate_number.images:
        try:
            images = json.loads(plate_number.images)
        except:
            images = []
    
    return render_template('plate_numbers/edit.html', plate_number=plate_number, images=images)

@plate_numbers_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """Delete plate number"""
    if not current_user.has_permission('delete_all'):
        flash('ليس لديك صلاحية لحذف أرقام اللوحات', 'error')
        return redirect(url_for('plate_numbers.index'))

    plate_number = PlateNumber.query.get_or_404(id)

    try:
        # Check if plate number has sales
        if plate_number.sales:
            flash('لا يمكن حذف رقم اللوحة لأنه مرتبط بمبيعات', 'error')
            return redirect(url_for('plate_numbers.view', id=id))

        db.session.delete(plate_number)
        db.session.commit()
        flash(f'تم حذف رقم اللوحة {plate_number.number} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف رقم اللوحة: {str(e)}', 'error')

    return redirect(url_for('plate_numbers.index'))

@plate_numbers_bp.route('/<int:id>/sell', methods=['GET', 'POST'])
@login_required
def sell(id):
    """Sell plate number"""
    plate_number = PlateNumber.query.get_or_404(id)

    if plate_number.status != 'available':
        flash('رقم اللوحة غير متاح للبيع', 'error')
        return redirect(url_for('plate_numbers.view', id=id))

    if request.method == 'POST':
        try:
            customer_id = int(request.form.get('customer_id'))
            sale_price = float(request.form.get('sale_price'))
            payment_method = request.form.get('payment_method')
            down_payment = float(request.form.get('down_payment', 0))
            notes = request.form.get('notes', '').strip()

            # Create sale record
            sale = PlateNumberSale(
                plate_number_id=id,
                customer_id=customer_id,
                sale_price=sale_price,
                payment_method=payment_method,
                down_payment=down_payment,
                notes=notes,
                created_by=current_user.id
            )

            # Handle installments
            if payment_method == 'installment':
                installment_count = int(request.form.get('installment_count', 0))
                if installment_count > 0:
                    remaining_amount = sale_price - down_payment
                    installment_amount = remaining_amount / installment_count
                    sale.installment_count = installment_count
                    sale.installment_amount = installment_amount

            db.session.add(sale)

            # Update plate number status
            plate_number.status = 'sold'

            # Create initial payment if down payment
            if down_payment > 0:
                payment = PlateNumberPayment(
                    sale_id=sale.id,
                    amount=down_payment,
                    payment_method=payment_method,
                    notes='دفعة أولى',
                    created_by=current_user.id
                )
                db.session.add(payment)

            db.session.commit()
            flash(f'تم بيع رقم اللوحة {plate_number.number} بنجاح', 'success')
            return redirect(url_for('plate_numbers.view', id=id))

        except ValueError as e:
            flash('خطأ في البيانات المدخلة. تأكد من صحة الأرقام.', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء بيع رقم اللوحة: {str(e)}', 'error')

    # Get customers for dropdown
    customers = Customer.query.order_by(Customer.full_name).all()

    return render_template('plate_numbers/sell.html',
                         plate_number=plate_number,
                         customers=customers)

@plate_numbers_bp.route('/<int:id>/bid', methods=['POST'])
@login_required
def place_bid(id):
    """Place bid on auction plate number"""
    plate_number = PlateNumber.query.get_or_404(id)

    if not plate_number.is_auction or plate_number.status != 'available':
        return jsonify({'success': False, 'message': 'رقم اللوحة غير متاح للمزايدة'})

    # Check if auction is active
    now = datetime.now()
    if plate_number.auction_end_date and now > plate_number.auction_end_date:
        return jsonify({'success': False, 'message': 'انتهت فترة المزايدة'})

    try:
        customer_id = int(request.form.get('customer_id'))
        bid_amount = float(request.form.get('bid_amount'))

        # Validate bid amount
        if plate_number.current_bid and bid_amount <= plate_number.current_bid:
            return jsonify({'success': False, 'message': 'يجب أن يكون المبلغ أعلى من المزايدة الحالية'})

        if plate_number.starting_bid and bid_amount < plate_number.starting_bid:
            return jsonify({'success': False, 'message': 'يجب أن يكون المبلغ أعلى من سعر البداية'})

        # Mark previous bids as outbid
        PlateNumberBid.query.filter_by(
            plate_number_id=id,
            status='winning'
        ).update({'status': 'outbid', 'is_winning': False})

        # Create new bid
        bid = PlateNumberBid(
            plate_number_id=id,
            customer_id=customer_id,
            bid_amount=bid_amount,
            is_winning=True,
            status='winning',
            created_by=current_user.id
        )

        db.session.add(bid)

        # Update current bid
        plate_number.current_bid = bid_amount

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تسجيل المزايدة بنجاح',
            'current_bid': bid_amount
        })

    except ValueError as e:
        return jsonify({'success': False, 'message': 'خطأ في البيانات المدخلة'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@plate_numbers_bp.route('/auctions')
@login_required
def auctions():
    """Display active auctions"""
    now = datetime.now()

    # Get active auctions
    active_auctions = PlateNumber.query.filter(
        PlateNumber.is_auction == True,
        PlateNumber.status == 'available',
        PlateNumber.auction_end_date > now
    ).order_by(PlateNumber.auction_end_date).all()

    # Get ended auctions
    ended_auctions = PlateNumber.query.filter(
        PlateNumber.is_auction == True,
        PlateNumber.auction_end_date <= now
    ).order_by(PlateNumber.auction_end_date.desc()).limit(10).all()

    return render_template('plate_numbers/auctions.html',
                         active_auctions=active_auctions,
                         ended_auctions=ended_auctions)

@plate_numbers_bp.route('/search')
@login_required
def search():
    """Search plate numbers"""
    query = request.args.get('q', '').strip()
    category = request.args.get('category', '')
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    digits = request.args.get('digits', type=int)

    if not query and not category and not min_price and not max_price and not digits:
        return render_template('plate_numbers/search.html', results=[])

    # Build search query
    search_query = PlateNumber.query.filter(PlateNumber.status == 'available')

    if query:
        search_query = search_query.filter(
            PlateNumber.number.contains(query) |
            PlateNumber.description.contains(query)
        )

    if category:
        search_query = search_query.filter(PlateNumber.category == category)

    if min_price:
        search_query = search_query.filter(PlateNumber.price >= min_price)

    if max_price:
        search_query = search_query.filter(PlateNumber.price <= max_price)

    if digits:
        search_query = search_query.filter(PlateNumber.digits_count == digits)

    results = search_query.order_by(PlateNumber.price).limit(50).all()

    return render_template('plate_numbers/search.html',
                         results=results,
                         query=query,
                         category=category,
                         min_price=min_price,
                         max_price=max_price,
                         digits=digits)

@plate_numbers_bp.route('/api/check-number')
@login_required
def check_number():
    """Check if plate number already exists"""
    number = request.args.get('number', '').strip()

    if not number:
        return jsonify({'exists': False})

    exists = PlateNumber.query.filter_by(number=number).first() is not None
    return jsonify({'exists': exists})

@plate_numbers_bp.route('/api/analyze-number')
@login_required
def analyze_number():
    """Analyze plate number for special features"""
    number = request.args.get('number', '').strip()

    if not number:
        return jsonify({'features': {}})

    # Extract digits only
    digits = ''.join([c for c in number if c.isdigit()])

    features = {
        'digits_count': len(digits),
        'is_sequential': False,
        'is_repeated': False,
        'is_mirror': False,
        'suggested_category': 'regular'
    }

    if len(digits) >= 2:
        # Check for sequential numbers
        is_sequential = True
        for i in range(1, len(digits)):
            if int(digits[i]) != (int(digits[i-1]) + 1) % 10:
                is_sequential = False
                break
        features['is_sequential'] = is_sequential

        # Check for repeated digits
        features['is_repeated'] = len(set(digits)) == 1

        # Check for mirror/palindrome
        features['is_mirror'] = digits == digits[::-1]

        # Suggest category
        if features['is_repeated'] or features['is_mirror']:
            features['suggested_category'] = 'vip'
        elif features['is_sequential'] or len(digits) <= 3:
            features['suggested_category'] = 'special'

    return jsonify({'features': features})
