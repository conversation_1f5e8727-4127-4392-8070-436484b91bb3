import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///car_dealership.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload settings
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}
    
    # Session settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # WhatsApp/Twilio settings
    TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
    TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')
    TWILIO_WHATSAPP_NUMBER = os.environ.get('TWILIO_WHATSAPP_NUMBER') or 'whatsapp:+***********'
    
    # Company information
    COMPANY_NAME = "معرض بوخليفة للسيارات"
    COMPANY_NAME_EN = "Bukhalifa Auto Show"
    COMPANY_ADDRESS = "الرياض، المملكة العربية السعودية"
    COMPANY_PHONE = "+************"
    COMPANY_EMAIL = "<EMAIL>"
    
    # Pagination
    CARS_PER_PAGE = 12
    CUSTOMERS_PER_PAGE = 20
    SALES_PER_PAGE = 15
    
    # Default installment settings
    DEFAULT_INSTALLMENT_MONTHS = 24
    MIN_DOWN_PAYMENT_PERCENTAGE = 20
    MAX_INSTALLMENT_MONTHS = 60
