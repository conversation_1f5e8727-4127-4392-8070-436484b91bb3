{% extends "base.html" %}

{% block title %}إضافة عميل جديد - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إضافة عميل جديد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<form method="POST" enctype="multipart/form-data" id="customerForm">
    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">بيانات العميل الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="national_id" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control id-input" id="national_id" name="national_id"
                                       data-format="qatar-id" maxlength="11" required autocomplete="off"
                                       placeholder="أدخل رقم الهوية">
                                <div class="form-text">11 رقم (قطر) - مثال: 12345678901</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control phone-input" id="phone" name="phone"
                                       data-format="phone" maxlength="8" required autocomplete="off"
                                       placeholder="أدخل رقم الهاتف">
                                <div class="form-text">8 أرقام (قطر) - مثال: 55123456</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="whatsapp_number" class="form-label">رقم الواتساب</label>
                                <input type="text" class="form-control phone-input" id="whatsapp_number" name="whatsapp_number"
                                       data-format="phone" maxlength="8" autocomplete="off"
                                       placeholder="أدخل رقم الواتساب">
                                <div class="form-text">8 أرقام (قطر) - لإرسال التذكيرات</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="occupation" class="form-label">المهنة</label>
                                <input type="text" class="form-control" id="occupation" name="occupation">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="monthly_income" class="form-label">الراتب الشهري</label>
                                <div class="input-group">
                                    <input type="text" class="form-control currency-input" id="monthly_income"
                                           name="monthly_income" data-format="currency" autocomplete="off"
                                           placeholder="أدخل الراتب الشهري">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">مثال: 15000 أو 15,000</div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Guarantor Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">بيانات الضامن (اختياري)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="guarantor_name" class="form-label">اسم الضامن</label>
                                <input type="text" class="form-control" id="guarantor_name" name="guarantor_name">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="guarantor_id" class="form-label">رقم هوية الضامن</label>
                                <input type="text" class="form-control id-input" id="guarantor_id" name="guarantor_id"
                                       data-format="qatar-id" maxlength="11" autocomplete="off"
                                       placeholder="أدخل رقم هوية الضامن">
                                <div class="form-text">11 رقم (قطر) - مثال: 12345678901</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="guarantor_phone" class="form-label">هاتف الضامن</label>
                                <input type="text" class="form-control phone-input" id="guarantor_phone" name="guarantor_phone"
                                       data-format="phone" maxlength="8" autocomplete="off"
                                       placeholder="أدخل هاتف الضامن">
                                <div class="form-text">8 أرقام (قطر) - مثال: 55123456</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="guarantor_address" class="form-label">عنوان الضامن</label>
                                <textarea class="form-control" id="guarantor_address" name="guarantor_address" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Documents -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">المستندات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="id_copy" class="form-label">صورة الهوية</label>
                                <input type="file" class="form-control image-input" id="id_copy" 
                                       name="id_copy" accept="image/*,application/pdf">
                                <div class="image-preview mt-2"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="photo" class="form-label">صورة شخصية</label>
                                <input type="file" class="form-control image-input" id="photo" 
                                       name="photo" accept="image/*">
                                <div class="image-preview mt-2"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="guarantor_id_copy" class="form-label">صورة هوية الضامن</label>
                                <input type="file" class="form-control image-input" id="guarantor_id_copy" 
                                       name="guarantor_id_copy" accept="image/*,application/pdf">
                                <div class="image-preview mt-2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">نصائح مهمة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                        <ul class="mb-0">
                            <li>رقم الهوية يجب أن يكون 11 رقم (قطر)</li>
                            <li>رقم الهاتف يجب أن يكون 8 أرقام (قطر)</li>
                            <li>رقم الواتساب مهم لإرسال التذكيرات</li>
                            <li>بيانات الضامن اختيارية للبيع بالتقسيط</li>
                            <li>تأكد من صحة أرقام الهواتف</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                        <p class="mb-0">الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة.</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ العميل
                        </button>
                        <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <hr>
                        <a href="{{ url_for('cars.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-car me-2"></i>
                            إدارة السيارات
                        </a>
                        <a href="{{ url_for('sales.index') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-handshake me-2"></i>
                            المبيعات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-save form data
    $('#customerForm input, #customerForm select, #customerForm textarea').on('change', function() {
        saveFormData('customerForm');
    });
    
    // Load saved form data
    loadFormData('customerForm');
    
    // Form validation
    $('#customerForm').on('submit', function(e) {
        if (!validateCustomerForm()) {
            e.preventDefault();
        }
    });
    
    // Format national ID input (Qatar: 11 digits)
    $('#national_id, #guarantor_id').on('input', function() {
        let value = $(this).val().replace(/[^\d]/g, '');
        if (value.length > 11) {
            value = value.substring(0, 11);
        }
        $(this).val(value);
    });

    // Format phone number input (Qatar: 8 digits)
    $('#phone, #whatsapp_number, #guarantor_phone').on('input', function() {
        let value = $(this).val().replace(/[^\d]/g, '');
        if (value.length > 8) {
            value = value.substring(0, 8);
        }
        $(this).val(value);
    });
    
    // Auto-fill WhatsApp number from phone
    $('#phone').on('blur', function() {
        const phone = $(this).val();
        const whatsapp = $('#whatsapp_number').val();
        
        if (phone && !whatsapp) {
            $('#whatsapp_number').val(phone);
        }
    });
});

function validateCustomerForm() {
    let isValid = true;

    // Validate national ID (Qatar: 11 digits)
    const nationalId = $('#national_id').val().trim();
    if (nationalId.length !== 11 || !/^\d{11}$/.test(nationalId)) {
        showFieldError($('#national_id'), 'رقم الهوية يجب أن يكون 11 رقم (قطر)');
        isValid = false;
    }

    // Validate guarantor ID if provided (Qatar: 11 digits)
    const guarantorId = $('#guarantor_id').val().trim();
    if (guarantorId && (guarantorId.length !== 11 || !/^\d{11}$/.test(guarantorId))) {
        showFieldError($('#guarantor_id'), 'رقم هوية الضامن يجب أن يكون 11 رقم (قطر)');
        isValid = false;
    }

    // Validate phone numbers (Qatar: 8 digits)
    const phone = $('#phone').val().trim();
    if (phone.length !== 8 || !/^\d{8}$/.test(phone)) {
        showFieldError($('#phone'), 'رقم الهاتف يجب أن يكون 8 أرقام (قطر)');
        isValid = false;
    }

    // Validate WhatsApp number if provided (Qatar: 8 digits)
    const whatsapp = $('#whatsapp_number').val().trim();
    if (whatsapp && (whatsapp.length !== 8 || !/^\d{8}$/.test(whatsapp))) {
        showFieldError($('#whatsapp_number'), 'رقم الواتساب يجب أن يكون 8 أرقام (قطر)');
        isValid = false;
    }

    // Validate guarantor phone if provided (Qatar: 8 digits)
    const guarantorPhone = $('#guarantor_phone').val().trim();
    if (guarantorPhone && (guarantorPhone.length !== 8 || !/^\d{8}$/.test(guarantorPhone))) {
        showFieldError($('#guarantor_phone'), 'هاتف الضامن يجب أن يكون 8 أرقام (قطر)');
        isValid = false;
    }

    return isValid;
}
</script>
{% endblock %}
