{% extends "base.html" %}

{% block title %}تعديل السيارة - {{ car.brand }} {{ car.model }} - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل السيارة - {{ car.brand }} {{ car.model }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('cars.view', car_id=car.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة لعرض السيارة
        </a>
    </div>
</div>

<form method="POST" enctype="multipart/form-data" id="editCarForm">
    <div class="row">
        <!-- Car Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات السيارة الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Brand -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brand" class="form-label">الماركة <span class="text-danger">*</span></label>
                                <select class="form-select" id="brand" name="brand" required>
                                    <option value="">اختر الماركة</option>
                                    <option value="تويوتا" {{ 'selected' if car.brand == 'تويوتا' }}>تويوتا</option>
                                    <option value="هوندا" {{ 'selected' if car.brand == 'هوندا' }}>هوندا</option>
                                    <option value="نيسان" {{ 'selected' if car.brand == 'نيسان' }}>نيسان</option>
                                    <option value="هيونداي" {{ 'selected' if car.brand == 'هيونداي' }}>هيونداي</option>
                                    <option value="كيا" {{ 'selected' if car.brand == 'كيا' }}>كيا</option>
                                    <option value="مازدا" {{ 'selected' if car.brand == 'مازدا' }}>مازدا</option>
                                    <option value="شيفروليه" {{ 'selected' if car.brand == 'شيفروليه' }}>شيفروليه</option>
                                    <option value="فورد" {{ 'selected' if car.brand == 'فورد' }}>فورد</option>
                                    <option value="بي إم دبليو" {{ 'selected' if car.brand == 'بي إم دبليو' }}>بي إم دبليو</option>
                                    <option value="مرسيدس" {{ 'selected' if car.brand == 'مرسيدس' }}>مرسيدس</option>
                                    <option value="أودي" {{ 'selected' if car.brand == 'أودي' }}>أودي</option>
                                    <option value="لكزس" {{ 'selected' if car.brand == 'لكزس' }}>لكزس</option>
                                    <option value="أخرى" {{ 'selected' if car.brand == 'أخرى' }}>أخرى</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Model -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">الموديل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="model" name="model" 
                                       value="{{ car.model }}" required placeholder="مثال: كامري، أكورد">
                            </div>
                        </div>
                        
                        <!-- Year -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="year" class="form-label">سنة الصنع <span class="text-danger">*</span></label>
                                <select class="form-select" id="year" name="year" required>
                                    <option value="">اختر السنة</option>
                                    {% for year in range(2024, 1989, -1) %}
                                    <option value="{{ year }}" {{ 'selected' if car.year == year }}>{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <!-- Color -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">اللون <span class="text-danger">*</span></label>
                                <select class="form-select" id="color" name="color" required>
                                    <option value="">اختر اللون</option>
                                    <option value="أبيض" {{ 'selected' if car.color == 'أبيض' }}>أبيض</option>
                                    <option value="أسود" {{ 'selected' if car.color == 'أسود' }}>أسود</option>
                                    <option value="فضي" {{ 'selected' if car.color == 'فضي' }}>فضي</option>
                                    <option value="رمادي" {{ 'selected' if car.color == 'رمادي' }}>رمادي</option>
                                    <option value="أحمر" {{ 'selected' if car.color == 'أحمر' }}>أحمر</option>
                                    <option value="أزرق" {{ 'selected' if car.color == 'أزرق' }}>أزرق</option>
                                    <option value="أخضر" {{ 'selected' if car.color == 'أخضر' }}>أخضر</option>
                                    <option value="بني" {{ 'selected' if car.color == 'بني' }}>بني</option>
                                    <option value="ذهبي" {{ 'selected' if car.color == 'ذهبي' }}>ذهبي</option>
                                    <option value="بيج" {{ 'selected' if car.color == 'بيج' }}>بيج</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- VIN -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vin" class="form-label">رقم الهيكل (VIN)</label>
                                <input type="text" class="form-control" id="vin" name="vin" 
                                       value="{{ car.vin or '' }}" placeholder="17 رقم/حرف">
                                <div class="form-text">رقم الهيكل المكون من 17 رقم وحرف</div>
                            </div>
                        </div>
                        
                        <!-- License Plate -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="license_plate" class="form-label">رقم اللوحة</label>
                                <input type="text" class="form-control" id="license_plate" name="license_plate" 
                                       value="{{ car.license_plate or '' }}" placeholder="مثال: أ ب ج 123">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Technical Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">التفاصيل التقنية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Engine Size -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="engine_size" class="form-label">حجم المحرك</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="engine_size" name="engine_size" 
                                           value="{{ car.engine_size or '' }}" step="0.1" min="0" placeholder="2.0">
                                    <span class="input-group-text">لتر</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fuel Type -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fuel_type" class="form-label">نوع الوقود</label>
                                <select class="form-select" id="fuel_type" name="fuel_type">
                                    <option value="">اختر نوع الوقود</option>
                                    <option value="بنزين" {{ 'selected' if car.fuel_type == 'بنزين' }}>بنزين</option>
                                    <option value="ديزل" {{ 'selected' if car.fuel_type == 'ديزل' }}>ديزل</option>
                                    <option value="هايبرد" {{ 'selected' if car.fuel_type == 'هايبرد' }}>هايبرد</option>
                                    <option value="كهربائي" {{ 'selected' if car.fuel_type == 'كهربائي' }}>كهربائي</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Transmission -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="transmission" class="form-label">ناقل الحركة</label>
                                <select class="form-select" id="transmission" name="transmission">
                                    <option value="">اختر ناقل الحركة</option>
                                    <option value="أوتوماتيك" {{ 'selected' if car.transmission == 'أوتوماتيك' }}>أوتوماتيك</option>
                                    <option value="عادي" {{ 'selected' if car.transmission == 'عادي' }}>عادي</option>
                                    <option value="CVT" {{ 'selected' if car.transmission == 'CVT' }}>CVT</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Mileage -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mileage" class="form-label">المسافة المقطوعة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="mileage" name="mileage" 
                                           value="{{ car.mileage or '' }}" min="0" placeholder="100000">
                                    <span class="input-group-text">كم</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Condition and Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">الحالة والوضع</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Condition -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="condition" class="form-label">حالة السيارة <span class="text-danger">*</span></label>
                                <select class="form-select" id="condition" name="condition" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="جديدة" {{ 'selected' if car.condition == 'جديدة' }}>جديدة</option>
                                    <option value="مستعملة" {{ 'selected' if car.condition == 'مستعملة' }}>مستعملة</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Quality -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quality" class="form-label">تقييم الجودة</label>
                                <select class="form-select" id="quality" name="quality">
                                    <option value="">اختر التقييم</option>
                                    <option value="ممتازة" {{ 'selected' if car.quality == 'ممتازة' }}>ممتازة</option>
                                    <option value="جيدة جداً" {{ 'selected' if car.quality == 'جيدة جداً' }}>جيدة جداً</option>
                                    <option value="جيدة" {{ 'selected' if car.quality == 'جيدة' }}>جيدة</option>
                                    <option value="مقبولة" {{ 'selected' if car.quality == 'مقبولة' }}>مقبولة</option>
                                    <option value="تحتاج صيانة" {{ 'selected' if car.quality == 'تحتاج صيانة' }}>تحتاج صيانة</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة التوفر <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="available" {{ 'selected' if car.status == 'available' }}>متاحة</option>
                                    <option value="sold" {{ 'selected' if car.status == 'sold' }}>مباعة</option>
                                    <option value="reserved" {{ 'selected' if car.status == 'reserved' }}>محجوزة</option>
                                    <option value="maintenance" {{ 'selected' if car.status == 'maintenance' }}>في الصيانة</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Purchase Price -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">سعر الشراء</label>
                                <div class="input-group">
                                    <input type="number" class="form-control currency-input" id="cost_price" 
                                           name="cost_price" value="{{ car.cost_price or '' }}" 
                                           step="0.01" min="0" placeholder="50000">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sale Price -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control currency-input" id="price" 
                                           name="price" value="{{ car.price }}" 
                                           step="0.01" min="0" required placeholder="60000">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Description and Notes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">الوصف والملاحظات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف السيارة</label>
                        <textarea class="form-control" id="description" name="description" rows="4"
                                  placeholder="اكتب وصفاً تفصيلياً للسيارة، المميزات، الإضافات، إلخ...">{{ car.description or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات داخلية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="ملاحظات للاستخدام الداخلي فقط...">{{ car.notes or '' }}</textarea>
                        <div class="form-text">هذه الملاحظات لن تظهر للعملاء</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Current Images -->
            {% if car.photos %}
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">الصور الحالية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for photo in car.photos %}
                        <div class="col-6 mb-3">
                            <div class="position-relative">
                                <img src="{{ url_for('static', filename=photo.file_path) }}" 
                                     class="img-fluid rounded" alt="صورة السيارة">
                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                                        onclick="deletePhoto({{ photo.id }})" title="حذف الصورة">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Upload New Images -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">إضافة صور جديدة</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="photos" class="form-label">اختر الصور</label>
                        <input type="file" class="form-control" id="photos" name="photos" 
                               multiple accept="image/*">
                        <div class="form-text">يمكنك اختيار عدة صور (JPG, PNG, GIF)</div>
                    </div>
                    
                    <div id="imagePreview" class="row"></div>
                </div>
            </div>
            
            <!-- Car Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">ملخص السيارة</h6>
                </div>
                <div class="card-body" id="carSummary">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>الماركة:</strong></td>
                            <td id="summaryBrand">{{ car.brand }}</td>
                        </tr>
                        <tr>
                            <td><strong>الموديل:</strong></td>
                            <td id="summaryModel">{{ car.model }}</td>
                        </tr>
                        <tr>
                            <td><strong>السنة:</strong></td>
                            <td id="summaryYear">{{ car.year }}</td>
                        </tr>
                        <tr>
                            <td><strong>اللون:</strong></td>
                            <td id="summaryColor">{{ car.color }}</td>
                        </tr>
                        <tr>
                            <td><strong>سعر البيع:</strong></td>
                            <td id="summarySalePrice" class="text-primary">{{ car.price|currency }}</td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td id="summaryStatus">
                                <span class="badge bg-{{ 'success' if car.status == 'available' else 'secondary' }}">
                                    {{ 'متاحة' if car.status == 'available' else car.status }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                        
                        <a href="{{ url_for('cars.view', car_id=car.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        
                        <hr>
                        
                        <a href="{{ url_for('cars.index') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-list me-2"></i>
                            قائمة السيارات
                        </a>
                        
                        {% if car.status == 'available' %}
                        <a href="{{ url_for('sales.add') }}?car_id={{ car.id }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-handshake me-2"></i>
                            بيع السيارة
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update summary when fields change
    $('#brand, #model, #year, #color, #price, #status').on('change input', function() {
        updateSummary();
    });

    // Image preview
    $('#photos').on('change', function() {
        previewImages(this);
    });

    function updateSummary() {
        $('#summaryBrand').text($('#brand').val() || '-');
        $('#summaryModel').text($('#model').val() || '-');
        $('#summaryYear').text($('#year').val() || '-');
        $('#summaryColor').text($('#color').val() || '-');

        const price = $('#price').val();
        if (price) {
            $('#summarySalePrice').text(formatCurrency(price));
        }

        const status = $('#status').val();
        const statusText = {
            'available': 'متاحة',
            'sold': 'مباعة',
            'reserved': 'محجوزة',
            'maintenance': 'في الصيانة'
        };
        const statusClass = {
            'available': 'success',
            'sold': 'danger',
            'reserved': 'warning',
            'maintenance': 'info'
        };

        $('#summaryStatus').html(`
            <span class="badge bg-${statusClass[status] || 'secondary'}">
                ${statusText[status] || status}
            </span>
        `);
    }

    function previewImages(input) {
        const preview = $('#imagePreview');
        preview.empty();

        if (input.files) {
            Array.from(input.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.append(`
                            <div class="col-6 mb-2">
                                <img src="${e.target.result}" class="img-fluid rounded" alt="معاينة">
                            </div>
                        `);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    }

    function formatCurrency(amount) {
        return parseFloat(amount).toLocaleString('ar-SA') + ' ريال';
    }
});

function deletePhoto(photoId) {
    if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
        return;
    }

    fetch(`/cars/photos/${photoId}/delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showAlert('حدث خطأ أثناء حذف الصورة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء حذف الصورة', 'danger');
    });
}

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}
</script>
{% endblock %}
