/* Font Enhancements for Qatar Car Dealership System */

/* Advanced Typography Settings */
.text-optimized {
    font-feature-settings: 
        "liga" 1,      /* Enable ligatures */
        "kern" 1,      /* Enable kerning */
        "calt" 1,      /* Enable contextual alternates */
        "ss01" 1,      /* Enable stylistic set 1 */
        "dlig" 1;      /* Enable discretionary ligatures */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Qatar-specific text styling */
.qatar-text {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.7;
    word-spacing: 0.1em;
}

/* Enhanced readability for long texts */
.readable-text {
    font-family: 'Noto Naskh Arabic', 'Cairo', serif;
    font-size: 1.1rem;
    line-height: 1.8;
    letter-spacing: 0.02em;
    word-spacing: 0.05em;
    text-align: justify;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
}

/* Modern Arabic headings */
.heading-modern {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.02em;
    text-transform: none;
}

/* Traditional Arabic headings */
.heading-traditional {
    font-family: 'Noto Naskh Arabic', 'Amiri', serif;
    font-weight: 600;
    line-height: 1.3;
    text-align: center;
}

/* Enhanced form styling */
.form-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

.form-arabic .form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.75rem;
}

.form-arabic .form-control,
.form-arabic .form-select {
    font-size: 1rem;
    line-height: 1.6;
    padding: 0.875rem 1rem;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    transition: all 0.2s ease;
}

.form-arabic .form-control:focus,
.form-arabic .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Enhanced button styling */
.btn-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
    letter-spacing: 0.025em;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.btn-arabic:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Card enhancements */
.card-arabic {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.card-arabic .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border-bottom: none;
}

.card-arabic .card-body {
    padding: 1.5rem;
    line-height: 1.7;
}

/* Table enhancements */
.table-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-size: 0.95rem;
}

.table-arabic th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 2px solid #e2e8f0;
    padding: 1rem 0.75rem;
}

.table-arabic td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f5f9;
}

.table-arabic tbody tr:hover {
    background-color: #f8fafc;
}

/* Navigation enhancements */
.nav-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

.nav-arabic .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav-arabic .nav-link:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-arabic .nav-link.active {
    background-color: #667eea;
    color: white;
}

/* Badge enhancements */
.badge-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 500;
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    letter-spacing: 0.025em;
}

/* Alert enhancements */
.alert-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
    line-height: 1.6;
}

.alert-arabic .alert-heading {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Breadcrumb enhancements */
.breadcrumb-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-arabic .breadcrumb-item {
    font-size: 0.9rem;
    color: #6b7280;
}

.breadcrumb-arabic .breadcrumb-item.active {
    color: #374151;
    font-weight: 500;
}

/* Pagination enhancements */
.pagination-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

.pagination-arabic .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    font-weight: 500;
}

.pagination-arabic .page-link:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.pagination-arabic .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

/* Modal enhancements */
.modal-arabic .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.modal-arabic .modal-title {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
}

.modal-arabic .modal-body {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    line-height: 1.6;
    padding: 1.5rem;
}

/* Dropdown enhancements */
.dropdown-arabic .dropdown-menu {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.dropdown-arabic .dropdown-item {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-arabic .dropdown-item:hover {
    background-color: #f8fafc;
    color: #667eea;
}

/* Tooltip enhancements */
.tooltip-arabic .tooltip-inner {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-size: 0.875rem;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
}

/* Progress bar enhancements */
.progress-arabic {
    border-radius: 10px;
    background-color: #f1f5f9;
}

.progress-arabic .progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Spinner enhancements */
.spinner-arabic {
    color: #667eea;
}

/* Print optimizations */
@media print {
    .text-optimized,
    .qatar-text,
    .readable-text {
        font-family: 'Cairo', 'Noto Sans Arabic', serif !important;
        color: #000 !important;
        background: transparent !important;
    }
    
    .heading-modern,
    .heading-traditional {
        color: #000 !important;
        page-break-after: avoid;
    }
    
    .card-arabic {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card-arabic {
        background-color: #2d3748;
        color: #f7fafc;
    }
    
    .table-arabic th {
        background-color: #4a5568;
        color: #f7fafc;
    }
    
    .form-arabic .form-control,
    .form-arabic .form-select {
        background-color: #4a5568;
        border-color: #718096;
        color: #f7fafc;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .text-optimized,
    .qatar-text,
    .readable-text {
        font-weight: 600;
    }
    
    .form-arabic .form-control,
    .form-arabic .form-select {
        border-width: 3px;
    }
    
    .btn-arabic {
        border-width: 2px;
        font-weight: 700;
    }
}
