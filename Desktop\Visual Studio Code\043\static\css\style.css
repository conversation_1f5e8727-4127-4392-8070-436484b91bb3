/* Additional custom styles for the car dealership system */

/* Font Face Declarations */
@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Naskh Arabic';
    src: url('../fonts/NotoNaskhArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Arabic font improvements */
body, .form-control, .form-select, .btn, .card, .table {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Noto Naskh Arabic', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Specific font weights and styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
    line-height: 1.4;
}

.navbar-brand, .card-title {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 700;
}

/* Arabic text improvements */
.arabic-text {
    font-family: 'Noto Naskh Arabic', 'Cairo', 'Noto Sans Arabic', serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
}

/* Numbers in Arabic context */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
    font-variant-numeric: lining-nums;
}

/* Improved animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Button improvements */
.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Status badges */
.status-available { background-color: #28a745; }
.status-sold { background-color: #dc3545; }
.status-reserved { background-color: #ffc107; color: #000; }
.status-maintenance { background-color: #6c757d; }

.status-pending { background-color: #ffc107; color: #000; }
.status-paid { background-color: #28a745; }
.status-overdue { background-color: #dc3545; }
.status-partial { background-color: #17a2b8; }

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Image gallery */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-gallery img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-gallery img:hover {
    transform: scale(1.05);
}

/* Print styles */
@media print {
    .sidebar, .btn, .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        border-color: #404040;
    }
    
    .table {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control, .form-select {
        background-color: #404040;
        border-color: #555555;
        color: #ffffff;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
.btn:focus, .form-control:focus, .form-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000000;
        border-color: #000000;
    }

    .card {
        border: 2px solid #000000;
    }
}

/* Enhanced Arabic Typography */
.text-arabic {
    font-family: 'Noto Naskh Arabic', 'Cairo', serif;
    line-height: 1.8;
    word-spacing: 0.1em;
}

.text-arabic-modern {
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    line-height: 1.6;
}

/* Form improvements for Arabic */
.form-control, .form-select {
    font-size: 1rem;
    line-height: 1.6;
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Button text improvements */
.btn {
    font-weight: 500;
    letter-spacing: 0.02em;
    line-height: 1.5;
}

/* Table text improvements */
.table {
    font-size: 0.95rem;
    line-height: 1.6;
}

.table th {
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Card text improvements */
.card-body {
    line-height: 1.7;
}

.card-text {
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Badge text improvements */
.badge {
    font-weight: 500;
    letter-spacing: 0.02em;
    line-height: 1.4;
}

/* Alert text improvements */
.alert {
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Navigation text improvements */
.nav-link {
    font-weight: 500;
    line-height: 1.5;
}

/* Sidebar text improvements */
.sidebar .nav-link {
    font-size: 0.95rem;
    line-height: 1.6;
    padding: 0.75rem 1rem;
}

/* Print font improvements */
@media print {
    body, .card, .table {
        font-family: 'Cairo', 'Noto Sans Arabic', serif !important;
        font-size: 12pt !important;
        line-height: 1.6 !important;
    }

    h1, h2, h3, h4, h5, h6 {
        font-weight: 600 !important;
        line-height: 1.4 !important;
    }
}

/* Mobile font adjustments */
@media (max-width: 768px) {
    body {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    h1, h2, h3, h4, h5, h6 {
        line-height: 1.3;
    }

    .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .form-control, .form-select {
        font-size: 0.9rem;
    }
}

/* Font loading optimization */
.font-loading {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

.fonts-loaded body {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Noto Naskh Arabic', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}
