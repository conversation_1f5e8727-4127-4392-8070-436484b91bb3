{% extends "base.html" %}

{% block title %}مزايدات أرقام السيارات{% endblock %}

{% block extra_css %}
<style>
.auction-card {
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.auction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.auction-card.active {
    border-color: #28a745;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

.auction-card.ended {
    opacity: 0.7;
    border-color: #6c757d;
}

.plate-display {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 2rem;
    margin-bottom: 15px;
    position: relative;
    animation: pulse-glow 2s infinite;
}

.plate-display.ended {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    animation: none;
}

@keyframes pulse-glow {
    0% { box-shadow: 0 0 5px rgba(67, 233, 123, 0.5); }
    50% { box-shadow: 0 0 20px rgba(67, 233, 123, 0.8); }
    100% { box-shadow: 0 0 5px rgba(67, 233, 123, 0.5); }
}

.auction-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.auction-status.active {
    background: #28a745;
    color: white;
}

.auction-status.ending-soon {
    background: #ffc107;
    color: #212529;
    animation: blink 1s infinite;
}

.auction-status.ended {
    background: #6c757d;
    color: white;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

.countdown-timer {
    background: rgba(220, 53, 69, 0.1);
    border: 2px solid #dc3545;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin: 15px 0;
}

.countdown-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #dc3545;
    font-family: 'Courier New', monospace;
}

.bid-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.current-bid {
    font-size: 1.5rem;
    font-weight: bold;
    color: #28a745;
    text-align: center;
}

.bid-history {
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
}

.bid-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.bid-item:last-child {
    border-bottom: none;
}

.bid-item.winning {
    background: #d4edda;
    border-radius: 5px;
    padding: 8px;
    margin: 2px 0;
}

.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
}

.stat-item {
    text-align: center;
    padding: 15px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.filter-tabs {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-pills .nav-link {
    border-radius: 20px;
    padding: 10px 20px;
    margin: 0 5px;
    font-weight: 500;
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auction-actions {
    position: sticky;
    bottom: 20px;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-gavel me-2"></i>مزايدات أرقام السيارات</h2>
            <p class="text-muted">المزايدات النشطة والمنتهية لأرقام اللوحات المميزة</p>
        </div>
        <div>
            <a href="{{ url_for('plate_numbers.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة رقم للمزايدة
            </a>
            <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>جميع الأرقام
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-section">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">{{ active_auctions|length }}</span>
                    <span class="stat-label">مزايدات نشطة</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">{{ ended_auctions|length }}</span>
                    <span class="stat-label">مزايدات منتهية</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    {% set total_bids = active_auctions|sum(attribute='bids')|length + ended_auctions|sum(attribute='bids')|length %}
                    <span class="stat-number">{{ total_bids or 0 }}</span>
                    <span class="stat-label">إجمالي المزايدات</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    {% set highest_bid = 0 %}
                    {% for auction in active_auctions %}
                        {% if auction.current_bid and auction.current_bid > highest_bid %}
                            {% set highest_bid = auction.current_bid %}
                        {% endif %}
                    {% endfor %}
                    <span class="stat-number">{{ "{:,.0f}".format(highest_bid) }}</span>
                    <span class="stat-label">أعلى مزايدة (ريال)</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <ul class="nav nav-pills justify-content-center" id="auctionTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="active-tab" data-bs-toggle="pill" data-bs-target="#active-auctions" type="button" role="tab">
                    <i class="fas fa-play-circle me-2"></i>المزايدات النشطة ({{ active_auctions|length }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ended-tab" data-bs-toggle="pill" data-bs-target="#ended-auctions" type="button" role="tab">
                    <i class="fas fa-stop-circle me-2"></i>المزايدات المنتهية ({{ ended_auctions|length }})
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="auctionTabsContent">
        <!-- Active Auctions -->
        <div class="tab-pane fade show active" id="active-auctions" role="tabpanel">
            {% if active_auctions %}
            <div class="row">
                {% for auction in active_auctions %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card auction-card active h-100">
                        <div class="card-body">
                            <!-- Plate Display -->
                            <div class="plate-display">
                                {{ auction.number }}
                                <span class="auction-status active">نشطة</span>
                            </div>

                            <!-- Auction Info -->
                            <div class="text-center mb-3">
                                <h6 class="card-title">{{ auction.description or 'رقم لوحة مميز' }}</h6>
                                <div class="badge bg-{{ 'danger' if auction.category == 'vip' else 'info' if auction.category == 'special' else 'secondary' }}">
                                    {% if auction.category == 'vip' %}VIP
                                    {% elif auction.category == 'special' %}مميزة
                                    {% else %}عادية{% endif %}
                                </div>
                            </div>

                            <!-- Current Bid -->
                            <div class="bid-info">
                                <div class="current-bid">
                                    {{ "{:,.0f}".format(auction.current_bid or auction.starting_bid) }} ريال
                                </div>
                                <div class="text-center text-muted small">المزايدة الحالية</div>
                                
                                {% if auction.reserve_price %}
                                <div class="text-center mt-2">
                                    <small class="text-muted">السعر المحجوز: {{ "{:,.0f}".format(auction.reserve_price) }} ريال</small>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Countdown Timer -->
                            {% if auction.auction_end_date %}
                            <div class="countdown-timer">
                                <div class="countdown-number" data-end-date="{{ auction.auction_end_date.isoformat() }}">
                                    جاري الحساب...
                                </div>
                                <small class="text-muted">متبقي على انتهاء المزايدة</small>
                            </div>
                            {% endif %}

                            <!-- Recent Bids -->
                            {% if auction.bids %}
                            <div class="bid-history">
                                <h6 class="mb-2"><i class="fas fa-history me-2"></i>آخر المزايدات</h6>
                                {% for bid in auction.bids[:3] %}
                                <div class="bid-item {{ 'winning' if bid.is_winning }}">
                                    <div>
                                        <strong>{{ bid.customer.full_name }}</strong>
                                        <small class="d-block text-muted">{{ bid.bid_date.strftime('%H:%M') }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-bold">{{ "{:,.0f}".format(bid.bid_amount) }}</span>
                                        {% if bid.is_winning %}
                                        <span class="badge bg-success ms-1">الأعلى</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                                {% if auction.bids|length > 3 %}
                                <div class="text-center mt-2">
                                    <small class="text-muted">و {{ auction.bids|length - 3 }} مزايدات أخرى</small>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Actions -->
                        <div class="card-footer bg-transparent">
                            <div class="d-grid gap-2">
                                <button class="btn btn-success" onclick="showBidModal({{ auction.id }}, '{{ auction.number }}', {{ auction.current_bid or auction.starting_bid }})">
                                    <i class="fas fa-gavel me-2"></i>مزايدة
                                </button>
                                <a href="{{ url_for('plate_numbers.view', id=auction.id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-2"></i>التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مزايدات نشطة حالياً</h4>
                <p class="text-muted">يمكنك إضافة أرقام جديدة للمزايدة من قسم إدارة الأرقام</p>
                <a href="{{ url_for('plate_numbers.add') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة رقم للمزايدة
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Ended Auctions -->
        <div class="tab-pane fade" id="ended-auctions" role="tabpanel">
            {% if ended_auctions %}
            <div class="row">
                {% for auction in ended_auctions %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card auction-card ended h-100">
                        <div class="card-body">
                            <!-- Plate Display -->
                            <div class="plate-display ended">
                                {{ auction.number }}
                                <span class="auction-status ended">منتهية</span>
                            </div>

                            <!-- Auction Info -->
                            <div class="text-center mb-3">
                                <h6 class="card-title">{{ auction.description or 'رقم لوحة مميز' }}</h6>
                                <div class="badge bg-secondary">
                                    انتهت في: {{ auction.auction_end_date.strftime('%Y-%m-%d') }}
                                </div>
                            </div>

                            <!-- Final Bid -->
                            <div class="bid-info">
                                {% if auction.current_bid %}
                                <div class="current-bid text-success">
                                    {{ "{:,.0f}".format(auction.current_bid) }} ريال
                                </div>
                                <div class="text-center text-muted small">السعر النهائي</div>
                                {% else %}
                                <div class="current-bid text-muted">
                                    لم تتلق مزايدات
                                </div>
                                <div class="text-center text-muted small">السعر الابتدائي: {{ "{:,.0f}".format(auction.starting_bid) }} ريال</div>
                                {% endif %}
                            </div>

                            <!-- Winner Info -->
                            {% if auction.bids %}
                                {% set winning_bid = auction.bids|selectattr('is_winning')|first %}
                                {% if winning_bid %}
                                <div class="alert alert-success">
                                    <i class="fas fa-trophy me-2"></i>
                                    <strong>الفائز:</strong> {{ winning_bid.customer.full_name }}
                                </div>
                                {% endif %}
                            {% endif %}

                            <!-- Bid Summary -->
                            <div class="text-center">
                                <small class="text-muted">
                                    عدد المزايدات: {{ auction.bids|length }}
                                    {% if auction.bids|length > 0 %}
                                    | آخر مزايدة: {{ auction.bids[0].bid_date.strftime('%H:%M') }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="card-footer bg-transparent">
                            <div class="d-grid">
                                <a href="{{ url_for('plate_numbers.view', id=auction.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مزايدات منتهية</h4>
                <p class="text-muted">ستظهر هنا المزايدات التي انتهت مؤخراً</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Bid Modal -->
<div class="modal fade" id="bidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">مزايدة على رقم اللوحة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bidForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">رقم اللوحة</label>
                        <input type="text" class="form-control" id="plateNumber" readonly>
                        <input type="hidden" id="plateId">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المزايدة الحالية</label>
                        <input type="text" class="form-control" id="currentBid" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                        <select class="form-select" id="customer_id" name="customer_id" required>
                            <option value="">اختر العميل</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="bid_amount" class="form-label">مبلغ المزايدة <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="bid_amount" name="bid_amount" required min="0" step="100">
                        <div class="form-text">يجب أن يكون أعلى من المزايدة الحالية</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تأكيد المزايدة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Countdown timers
function updateCountdowns() {
    const countdownElements = document.querySelectorAll('.countdown-number[data-end-date]');
    
    countdownElements.forEach(element => {
        const endDate = new Date(element.dataset.endDate);
        const now = new Date();
        const diff = endDate - now;
        
        if (diff > 0) {
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            let timeString = '';
            if (days > 0) timeString += `${days} يوم `;
            if (hours > 0) timeString += `${hours} ساعة `;
            if (minutes > 0) timeString += `${minutes} دقيقة `;
            timeString += `${seconds} ثانية`;
            
            element.textContent = timeString;
            
            // Change color if ending soon
            if (diff < 3600000) { // Less than 1 hour
                element.parentElement.classList.add('ending-soon');
            }
        } else {
            element.textContent = 'انتهت المزايدة';
            element.parentElement.classList.remove('ending-soon');
            element.parentElement.classList.add('ended');
        }
    });
}

// Update countdowns every second
setInterval(updateCountdowns, 1000);
updateCountdowns();

// Bid modal functions
function showBidModal(plateId, plateNumber, currentBid) {
    $('#plateId').val(plateId);
    $('#plateNumber').val(plateNumber);
    $('#currentBid').val(currentBid.toLocaleString() + ' ريال');
    $('#bid_amount').attr('min', currentBid + 100);
    $('#bidModal').modal('show');
    
    // Load customers if not already loaded
    if ($('#customer_id option').length <= 1) {
        loadCustomers();
    }
}

function loadCustomers() {
    $.get('/customers/api/list', function(data) {
        const select = $('#customer_id');
        select.empty().append('<option value="">اختر العميل</option>');
        data.forEach(customer => {
            select.append(`<option value="${customer.id}">${customer.full_name} - ${customer.phone}</option>`);
        });
    }).fail(function() {
        showNotification('خطأ في تحميل قائمة العملاء', 'error');
    });
}

// Submit bid
$('#bidForm').on('submit', function(e) {
    e.preventDefault();
    
    const plateId = $('#plateId').val();
    const formData = new FormData(this);
    
    $.post(`/plate-numbers/${plateId}/bid`, formData)
        .done(function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                $('#bidModal').modal('hide');
                location.reload();
            } else {
                showNotification(response.message, 'error');
            }
        })
        .fail(function() {
            showNotification('حدث خطأ أثناء تسجيل المزايدة', 'error');
        });
});

// Auto refresh page every 30 seconds for active auctions
if (document.querySelector('#active-auctions.show')) {
    setInterval(function() {
        // Only refresh if user is still on active auctions tab
        if (document.querySelector('#active-tab.active')) {
            location.reload();
        }
    }, 30000);
}

// Sound notification for ending auctions
function checkEndingAuctions() {
    const endingSoon = document.querySelectorAll('.ending-soon');
    if (endingSoon.length > 0) {
        // Play notification sound (if available)
        if (typeof Audio !== 'undefined') {
            try {
                const audio = new Audio('/static/sounds/notification.mp3');
                audio.volume = 0.3;
                audio.play().catch(() => {}); // Ignore errors
            } catch (e) {}
        }
        
        // Show browser notification
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('تنبيه مزايدة', {
                body: `هناك ${endingSoon.length} مزايدة ستنتهي قريباً`,
                icon: '/static/images/logo.png'
            });
        }
    }
}

// Check for ending auctions every minute
setInterval(checkEndingAuctions, 60000);

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
</script>
{% endblock %}
