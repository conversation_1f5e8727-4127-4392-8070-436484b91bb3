{% extends "base.html" %}

{% block title %}قوالب الواتساب المحسنة - {{ company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fab fa-whatsapp me-2"></i>
                        قوالب رسائل الواتساب المحسنة
                    </h3>
                    <div>
                        <button class="btn btn-outline-info me-2" onclick="showVariablesHelp()">
                            <i class="fas fa-question-circle me-2"></i>
                            دليل المتغيرات
                        </button>
                        <button class="btn btn-outline-primary me-2" onclick="importTemplates()">
                            <i class="fas fa-download me-2"></i>
                            استيراد قوالب
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة قالب جديد
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    
                    <!-- Template Categories Tabs -->
                    <ul class="nav nav-tabs mb-4" id="templateTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                تذكيرات الدفع
                                <span class="badge bg-warning ms-2">3</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="welcome-tab" data-bs-toggle="tab" data-bs-target="#welcome" type="button" role="tab">
                                <i class="fas fa-handshake me-2"></i>
                                رسائل الترحيب
                                <span class="badge bg-success ms-2">2</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="promotion-tab" data-bs-toggle="tab" data-bs-target="#promotion" type="button" role="tab">
                                <i class="fas fa-tags me-2"></i>
                                عروض ترويجية
                                <span class="badge bg-info ms-2">1</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="followup-tab" data-bs-toggle="tab" data-bs-target="#followup" type="button" role="tab">
                                <i class="fas fa-phone me-2"></i>
                                متابعة العملاء
                                <span class="badge bg-secondary ms-2">1</span>
                            </button>
                        </li>
                    </ul>
                    
                    <!-- Template Content -->
                    <div class="tab-content" id="templateTabContent">
                        
                        <!-- Payment Reminders -->
                        <div class="tab-pane fade show active" id="payment" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card template-card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">تذكير بالقسط - قطر</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="useTemplate(1)">
                                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewTemplate(1)">
                                                        <i class="fas fa-eye me-2"></i>معاينة
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="editTemplate(1)">
                                                        <i class="fas fa-edit me-2"></i>تعديل
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item" href="#" onclick="duplicateTemplate(1)">
                                                        <i class="fas fa-copy me-2"></i>نسخ
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteTemplate(1)">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="template-preview mb-3">
                                                <div class="whatsapp-message">
                                                    <div class="message-bubble">
                                                        السلام عليكم {customer_name}<br>
                                                        تذكير بموعد استحقاق القسط:<br>
                                                        • رقم القسط: {installment_number}<br>
                                                        • المبلغ: {amount} ريال قطري<br>
                                                        <span class="text-muted">...</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="template-stats d-flex justify-content-between">
                                                <span class="badge bg-primary">دفع</span>
                                                <small class="text-muted">استخدم 45 مرة</small>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-success btn-sm w-100" onclick="useTemplate(1)">
                                                <i class="fab fa-whatsapp me-2"></i>
                                                استخدام الآن
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card template-card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">إشعار تأخير - عاجل</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="useTemplate(3)">
                                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewTemplate(3)">
                                                        <i class="fas fa-eye me-2"></i>معاينة
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="editTemplate(3)">
                                                        <i class="fas fa-edit me-2"></i>تعديل
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="template-preview mb-3">
                                                <div class="whatsapp-message">
                                                    <div class="message-bubble urgent">
                                                        ⚠️ إشعار مهم - تأخير في سداد القسط<br>
                                                        عدد أيام التأخير: {days_overdue} يوم<br>
                                                        المبلغ: {amount} ريال قطري<br>
                                                        <span class="text-muted">...</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="template-stats d-flex justify-content-between">
                                                <span class="badge bg-danger">عاجل</span>
                                                <small class="text-muted">استخدم 23 مرة</small>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-danger btn-sm w-100" onclick="useTemplate(3)">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                إرسال عاجل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Welcome Messages -->
                        <div class="tab-pane fade" id="welcome" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card template-card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">ترحيب بالعميل الجديد</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="useTemplate(2)">
                                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewTemplate(2)">
                                                        <i class="fas fa-eye me-2"></i>معاينة
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="template-preview mb-3">
                                                <div class="whatsapp-message">
                                                    <div class="message-bubble welcome">
                                                        أهلاً وسهلاً بك {customer_name} 🎉<br>
                                                        نرحب بك في عائلة معرض بوخليفة<br>
                                                        تفاصيل السيارة: {car_details}<br>
                                                        <span class="text-muted">...</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="template-stats d-flex justify-content-between">
                                                <span class="badge bg-success">ترحيب</span>
                                                <small class="text-muted">استخدم 67 مرة</small>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-success btn-sm w-100" onclick="useTemplate(2)">
                                                <i class="fas fa-handshake me-2"></i>
                                                إرسال ترحيب
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card template-card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">تهنئة بالشراء</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="useTemplate(6)">
                                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewTemplate(6)">
                                                        <i class="fas fa-eye me-2"></i>معاينة
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="template-preview mb-3">
                                                <div class="whatsapp-message">
                                                    <div class="message-bubble celebration">
                                                        مبروك الشراء الجديد! 🎊<br>
                                                        تهانينا بامتلاك سيارتكم الجديدة<br>
                                                        🎁 هدايا خاصة لك<br>
                                                        <span class="text-muted">...</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="template-stats d-flex justify-content-between">
                                                <span class="badge bg-warning">تهنئة</span>
                                                <small class="text-muted">استخدم 34 مرة</small>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-warning btn-sm w-100" onclick="useTemplate(6)">
                                                <i class="fas fa-gift me-2"></i>
                                                إرسال تهنئة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Promotions -->
                        <div class="tab-pane fade" id="promotion" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card template-card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">عرض ترويجي</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="useTemplate(4)">
                                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewTemplate(4)">
                                                        <i class="fas fa-eye me-2"></i>معاينة
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="template-preview mb-3">
                                                <div class="whatsapp-message">
                                                    <div class="message-bubble promotion">
                                                        🎉 عرض خاص من معرض بوخليفة! 🎉<br>
                                                        خصم يصل إلى 15% على السيارات<br>
                                                        تقسيط بدون فوائد لمدة 5 سنوات<br>
                                                        <span class="text-muted">...</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="template-stats d-flex justify-content-between">
                                                <span class="badge bg-info">عرض</span>
                                                <small class="text-muted">استخدم 12 مرة</small>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-info btn-sm w-100" onclick="useTemplate(4)">
                                                <i class="fas fa-tags me-2"></i>
                                                إرسال عرض
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Follow-up -->
                        <div class="tab-pane fade" id="followup" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card template-card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">تأكيد الموعد</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="useTemplate(5)">
                                                        <i class="fas fa-paper-plane me-2"></i>استخدام
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewTemplate(5)">
                                                        <i class="fas fa-eye me-2"></i>معاينة
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="template-preview mb-3">
                                                <div class="whatsapp-message">
                                                    <div class="message-bubble appointment">
                                                        ✅ تأكيد موعد زيارة المعرض<br>
                                                        التاريخ: {today}<br>
                                                        الوقت: {time}<br>
                                                        <span class="text-muted">...</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="template-stats d-flex justify-content-between">
                                                <span class="badge bg-secondary">موعد</span>
                                                <small class="text-muted">استخدم 28 مرة</small>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-secondary btn-sm w-100" onclick="useTemplate(5)">
                                                <i class="fas fa-calendar-check me-2"></i>
                                                تأكيد موعد
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" id="useTemplateFromPreview">
                    <i class="fab fa-whatsapp me-2"></i>
                    استخدام هذا القالب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Template Modal -->
<div class="modal fade" id="addTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قالب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTemplateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="templateName" class="form-label">اسم القالب</label>
                                <input type="text" class="form-control" id="templateName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="templateType" class="form-label">نوع القالب</label>
                                <select class="form-select" id="templateType" required>
                                    <option value="">اختر النوع</option>
                                    <option value="payment_reminder">تذكير بالدفع</option>
                                    <option value="welcome">ترحيب</option>
                                    <option value="promotion">عرض ترويجي</option>
                                    <option value="follow_up">متابعة</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="templateContent" class="form-label">محتوى القالب</label>
                        <textarea class="form-control" id="templateContent" rows="8" required
                                  placeholder="اكتب محتوى القالب هنا..."></textarea>
                        <div class="form-text">
                            يمكنك استخدام المتغيرات مثل {customer_name} و {amount} و {today}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="templateDescription" class="form-label">وصف القالب (اختياري)</label>
                        <input type="text" class="form-control" id="templateDescription"
                               placeholder="وصف مختصر للقالب">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveTemplate()">
                    <i class="fas fa-save me-2"></i>
                    حفظ القالب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Variables Help Modal -->
<div class="modal fade" id="variablesHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">دليل المتغيرات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات العميل</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{customer_name}</code>
                                <span>اسم العميل</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{phone}</code>
                                <span>رقم الهاتف</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{email}</code>
                                <span>البريد الإلكتروني</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات السيارة</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{car_details}</code>
                                <span>تفاصيل السيارة</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{chassis_number}</code>
                                <span>رقم الشاسيه</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{sale_price}</code>
                                <span>سعر البيع</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الأقساط</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{installment_number}</code>
                                <span>رقم القسط</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{due_date}</code>
                                <span>تاريخ الاستحقاق</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{amount}</code>
                                <span>مبلغ القسط</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{days_overdue}</code>
                                <span>أيام التأخير</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات النظام</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{company_name}</code>
                                <span>اسم الشركة</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{today}</code>
                                <span>تاريخ اليوم</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <code>{time}</code>
                                <span>الوقت الحالي</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_css %}
<style>
.template-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #e0e0e0;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.whatsapp-message {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
}

.message-bubble {
    background: #DCF8C6;
    padding: 10px 12px;
    border-radius: 15px 15px 3px 15px;
    color: #303030;
    font-size: 0.9rem;
    line-height: 1.4;
    position: relative;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-bubble.urgent {
    background: #FFE5E5;
    border-left: 4px solid #dc3545;
}

.message-bubble.welcome {
    background: #E8F5E8;
    border-left: 4px solid #28a745;
}

.message-bubble.celebration {
    background: #FFF3CD;
    border-left: 4px solid #ffc107;
}

.message-bubble.promotion {
    background: #D1ECF1;
    border-left: 4px solid #17a2b8;
}

.message-bubble.appointment {
    background: #E2E3E5;
    border-left: 4px solid #6c757d;
}

.message-bubble::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top-color: #DCF8C6;
    border-bottom: 0;
    margin-bottom: -6px;
}

.template-stats {
    font-size: 0.85rem;
}

.nav-tabs .nav-link {
    color: #495057;
    border: none;
    border-bottom: 2px solid transparent;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #007bff;
}

.badge {
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .template-card {
        margin-bottom: 1rem;
    }

    .whatsapp-message {
        padding: 10px;
    }

    .message-bubble {
        font-size: 0.8rem;
        padding: 8px 10px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentTemplateId = null;

// Use template function
function useTemplate(templateId) {
    // Redirect to send message page with template pre-selected
    window.location.href = `{{ url_for('whatsapp.send_message') }}?template_id=${templateId}`;
}

// Preview template function
function previewTemplate(templateId) {
    currentTemplateId = templateId;

    // Get template data (in real app, this would be an AJAX call)
    const templates = {
        1: {
            name: 'تذكير بالقسط - قطر',
            content: `السلام عليكم ورحمة الله وبركاته {customer_name}

تذكير بموعد استحقاق القسط:

📋 تفاصيل القسط:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• المبلغ المطلوب: {amount} ريال قطري

🚗 تفاصيل السيارة:
• {car_details}
• رقم الشاسيه: {chassis_number}

يرجى سداد المبلغ في الموعد المحدد لتجنب أي رسوم إضافية.

📞 للاستفسار: +974 4444 5555
🏢 معرض بوخليفة للسيارات - قطر`
        },
        2: {
            name: 'ترحيب بالعميل الجديد',
            content: `أهلاً وسهلاً بك {customer_name} 🎉

نرحب بك في عائلة معرض بوخليفة للسيارات!

تم إعداد عقد بيع السيارة بنجاح:

🚗 تفاصيل السيارة:
• {car_details}
• سعر البيع: {sale_price} ريال قطري
• نوع البيع: {sale_type}

📋 الخطوات التالية:
1. مراجعة المعرض لتوقيع العقد
2. إحضار الوثائق المطلوبة
3. استلام السيارة

📞 للتواصل: +974 4444 5555
📍 الموقع: الدوحة، قطر

شكراً لثقتكم بنا! 🙏`
        },
        3: {
            name: 'إشعار تأخير - عاجل',
            content: `السلام عليكم {customer_name}

⚠️ إشعار مهم - تأخير في سداد القسط

📋 تفاصيل القسط المتأخر:
• رقم القسط: {installment_number}
• تاريخ الاستحقاق: {due_date}
• عدد أيام التأخير: {days_overdue} يوم
• المبلغ المطلوب: {amount} ريال قطري

🚗 السيارة: {car_details}

⏰ يرجى سداد المبلغ المستحق في أقرب وقت ممكن لتجنب:
• رسوم التأخير
• الإجراءات القانونية
• تعليق الخدمات

💳 طرق الدفع المتاحة:
• نقداً في المعرض
• تحويل بنكي
• بطاقة ائتمانية

📞 للتواصل الفوري: +974 4444 5555
🏢 معرض بوخليفة للسيارات`
        },
        4: {
            name: 'عرض ترويجي',
            content: `🎉 عرض خاص من معرض بوخليفة للسيارات! 🎉

عزيزي {customer_name}

🚗 عروض حصرية على أفضل السيارات:
• خصم يصل إلى 15% على السيارات الجديدة
• تقسيط مريح بدون فوائد لمدة 5 سنوات
• ضمان شامل لمدة 3 سنوات
• صيانة مجانية للسنة الأولى

⏰ العرض ساري حتى نهاية الشهر!

📞 احجز موعدك الآن: +974 4444 5555
📍 زورونا في الدوحة، قطر

لا تفوت الفرصة! 🔥`
        },
        5: {
            name: 'تأكيد الموعد',
            content: `السلام عليكم {customer_name}

✅ تأكيد موعد زيارة المعرض

📅 تفاصيل الموعد:
• التاريخ: {today}
• الوقت: {time}
• الغرض: مراجعة عقد السيارة

📍 عنوان المعرض:
معرض بوخليفة للسيارات
الدوحة، قطر

📋 يرجى إحضار:
• بطاقة الهوية القطرية
• رخصة القيادة
• كشف حساب بنكي (للتقسيط)

📞 للاستفسار أو تغيير الموعد: +974 4444 5555

نتطلع لرؤيتكم! 🤝`
        },
        6: {
            name: 'تهنئة بالشراء',
            content: `مبروك الشراء الجديد! 🎊 {customer_name}

🚗 تهانينا بامتلاك سيارتكم الجديدة:
{car_details}

🎁 هدايا خاصة لك:
• بطاقة صيانة مجانية لسنة كاملة
• تأمين شامل مخفض
• خدمة غسيل مجانية شهرياً
• خصم 20% على قطع الغيار

📱 تطبيق خدمة العملاء:
حمّل تطبيقنا لمتابعة صيانة سيارتك وحجز المواعيد

📞 خدمة العملاء 24/7: +974 4444 5555

شكراً لثقتكم الغالية!
معرض بوخليفة للسيارات - قطر 🇶🇦`
        }
    };

    const template = templates[templateId];
    if (template) {
        const previewContent = `
            <div class="mb-3">
                <h6>${template.name}</h6>
            </div>
            <div class="whatsapp-message">
                <div class="message-bubble">
                    ${template.content.replace(/\n/g, '<br>')}
                </div>
            </div>
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                المتغيرات (مثل {customer_name}) ستُستبدل بالقيم الفعلية عند الإرسال
            </div>
        `;

        document.getElementById('templatePreviewContent').innerHTML = previewContent;
        document.getElementById('useTemplateFromPreview').onclick = () => useTemplate(templateId);

        const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
        modal.show();
    }
}

// Edit template function
function editTemplate(templateId) {
    alert('ميزة التعديل ستكون متاحة قريباً');
}

// Delete template function
function deleteTemplate(templateId) {
    if (confirm('هل أنت متأكد من حذف هذا القالب؟')) {
        alert('تم حذف القالب بنجاح');
        // In real app, make AJAX call to delete
    }
}

// Duplicate template function
function duplicateTemplate(templateId) {
    alert('تم نسخ القالب بنجاح');
    // In real app, make AJAX call to duplicate
}

// Show variables help
function showVariablesHelp() {
    const modal = new bootstrap.Modal(document.getElementById('variablesHelpModal'));
    modal.show();
}

// Import templates function
function importTemplates() {
    alert('ميزة استيراد القوالب ستكون متاحة قريباً');
}

// Save new template
function saveTemplate() {
    const name = document.getElementById('templateName').value;
    const type = document.getElementById('templateType').value;
    const content = document.getElementById('templateContent').value;
    const description = document.getElementById('templateDescription').value;

    if (!name || !type || !content) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // In real app, make AJAX call to save template
    alert('تم حفظ القالب بنجاح!');

    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('addTemplateModal'));
    modal.hide();
    document.getElementById('addTemplateForm').reset();
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
