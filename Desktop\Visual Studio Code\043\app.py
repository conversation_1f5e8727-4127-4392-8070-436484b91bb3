from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import Login<PERSON>anager, login_required, current_user
from config import Config
from models import db, User, Car, Customer, Sale, Installment, Payment, Notification
import os
from datetime import datetime, date, timedelta

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # Create upload directories
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'cars'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'customers'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'contracts'), exist_ok=True)
    
    # Register blueprints
    from auth import auth_bp
    from dashboard import dashboard_bp
    from cars import cars_bp
    from customers import customers_bp
    from sales import sales_bp
    from contracts import contracts_bp
    from reports import reports_bp
    from whatsapp import whatsapp_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(cars_bp, url_prefix='/cars')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(sales_bp, url_prefix='/sales')
    app.register_blueprint(contracts_bp, url_prefix='/contracts')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(whatsapp_bp, url_prefix='/whatsapp')
    
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))

    @app.route('/font-test')
    @login_required
    def font_test():
        """Test page for Arabic fonts"""
        return render_template('font-test.html')
    
    @app.context_processor
    def inject_global_vars():
        """Inject global variables into all templates"""
        unread_notifications = 0
        if current_user.is_authenticated:
            unread_notifications = Notification.query.filter_by(
                user_id=current_user.id, 
                is_read=False
            ).count()
        
        return {
            'company_name': app.config['COMPANY_NAME'],
            'company_name_en': app.config['COMPANY_NAME_EN'],
            'unread_notifications': unread_notifications,
            'current_date': date.today(),
            'current_datetime': datetime.now()
        }
    
    @app.template_filter('currency')
    def currency_filter(amount):
        """Format currency in Arabic style"""
        if amount is None:
            return "0 ريال"
        return f"{amount:,.0f} ريال"
    
    @app.template_filter('arabic_date')
    def arabic_date_filter(date_obj):
        """Format date in Arabic"""
        if not date_obj:
            return ""

        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }

        if isinstance(date_obj, str):
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()

        return f"{date_obj.day} {months[date_obj.month]} {date_obj.year}"

    @app.template_filter('from_json')
    def from_json_filter(json_str):
        """Parse JSON string"""
        if not json_str:
            return []
        try:
            import json
            return json.loads(json_str)
        except:
            return []

    @app.template_filter('number_format')
    def number_format_filter(number):
        """Format number with commas"""
        if number is None:
            return "0"
        return f"{number:,}"
    
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='manager',
                full_name='مدير النظام',
                phone='+966501234567'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Default admin user created: admin/admin123")
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
