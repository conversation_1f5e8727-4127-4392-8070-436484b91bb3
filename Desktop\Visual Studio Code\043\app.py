from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import Login<PERSON>anager, login_required, current_user
from config import Config
from models import db, User, Car, Customer, Sale, Installment, Payment, Notification
import os
from datetime import datetime, date, timedelta

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # Create upload directories
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'cars'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'customers'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'contracts'), exist_ok=True)
    
    # Register blueprints
    from auth import auth_bp
    from dashboard import dashboard_bp
    from cars import cars_bp
    from customers import customers_bp
    from sales import sales_bp
    from contracts import contracts_bp
    from reports import reports_bp
    from whatsapp import whatsapp_bp
    from notifications import notifications_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(cars_bp, url_prefix='/cars')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(sales_bp, url_prefix='/sales')
    app.register_blueprint(contracts_bp, url_prefix='/contracts')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(whatsapp_bp, url_prefix='/whatsapp')
    app.register_blueprint(notifications_bp, url_prefix='/notifications')
    
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))

    @app.route('/font-test')
    @login_required
    def font_test():
        """Test page for Arabic fonts"""
        return render_template('font-test.html')

    @app.route('/number-test')
    @login_required
    def number_test():
        """Test page for number formatting"""
        return render_template('number-test.html')
    
    @app.context_processor
    def inject_global_vars():
        """Inject global variables into all templates"""
        unread_notifications = 0
        if current_user.is_authenticated:
            unread_notifications = Notification.query.filter_by(
                user_id=current_user.id, 
                is_read=False
            ).count()
        
        return {
            'company_name': app.config['COMPANY_NAME'],
            'company_name_en': app.config['COMPANY_NAME_EN'],
            'unread_notifications': unread_notifications,
            'current_date': date.today(),
            'current_datetime': datetime.now()
        }
    
    @app.template_filter('currency')
    def currency_filter(amount):
        """Format currency in Arabic style"""
        if amount is None or amount == 0:
            return "0 ريال قطري"

        # Handle float precision issues
        if isinstance(amount, float):
            # Round to 2 decimal places to avoid precision issues
            amount = round(amount, 2)

            # If it's a whole number, display as integer
            if amount == int(amount):
                amount = int(amount)

        try:
            # Format with commas and handle large numbers
            if amount >= 1000000:
                formatted = f"{amount:,.0f}"
            elif amount >= 1000:
                formatted = f"{amount:,.0f}"
            else:
                formatted = f"{amount:.0f}"

            return f"{formatted} ريال قطري"
        except (ValueError, TypeError):
            return "0 ريال قطري"
    
    @app.template_filter('arabic_date')
    def arabic_date_filter(date_obj):
        """Format date in Arabic"""
        if not date_obj:
            return ""

        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }

        if isinstance(date_obj, str):
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()

        return f"{date_obj.day} {months[date_obj.month]} {date_obj.year}"

    @app.template_filter('from_json')
    def from_json_filter(json_str):
        """Parse JSON string"""
        if not json_str:
            return []
        try:
            import json
            return json.loads(json_str)
        except:
            return []

    @app.template_filter('number_format')
    def number_format_filter(number):
        """Format number with commas"""
        if number is None or number == 0:
            return "0"

        # Handle float precision issues
        if isinstance(number, float):
            # Round to 2 decimal places to avoid precision issues
            number = round(number, 2)

            # If it's a whole number, display as integer
            if number == int(number):
                number = int(number)

        try:
            # Format with commas
            if isinstance(number, float) and number != int(number):
                return f"{number:,.2f}"
            else:
                return f"{number:,}"
        except (ValueError, TypeError):
            return "0"

    @app.template_filter('arabic_number')
    def arabic_number_filter(number):
        """Convert number to Arabic numerals"""
        if number is None:
            return "٠"

        # Handle float precision issues
        if isinstance(number, float):
            number = round(number, 2)
            if number == int(number):
                number = int(number)

        # Convert to string and replace digits
        number_str = str(number)
        arabic_digits = {
            '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
            '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
        }

        for english, arabic in arabic_digits.items():
            number_str = number_str.replace(english, arabic)

        return number_str

    @app.template_filter('qatar_currency')
    def qatar_currency_filter(amount):
        """Format currency specifically for Qatar"""
        if amount is None or amount == 0:
            return "٠ ر.ق"

        # Handle float precision issues
        if isinstance(amount, float):
            amount = round(amount, 2)
            if amount == int(amount):
                amount = int(amount)

        try:
            # Format with commas
            formatted = f"{amount:,.0f}"

            # Convert to Arabic numerals
            arabic_digits = {
                '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
                '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
            }

            for english, arabic in arabic_digits.items():
                formatted = formatted.replace(english, arabic)

            return f"{formatted} ر.ق"
        except (ValueError, TypeError):
            return "٠ ر.ق"

    @app.template_filter('safe_number')
    def safe_number_filter(number):
        """Safely format number avoiding precision issues"""
        if number is None:
            return 0

        # Handle string numbers
        if isinstance(number, str):
            try:
                number = float(number)
            except ValueError:
                return 0

        # Handle float precision issues
        if isinstance(number, float):
            # Check for repeating decimals (precision issues)
            if abs(number - round(number)) < 0.0001:
                return int(round(number))
            else:
                return round(number, 2)

        return number

    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='manager',
                full_name='مدير النظام',
                phone='+966501234567'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Default admin user created: admin/admin123")
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
