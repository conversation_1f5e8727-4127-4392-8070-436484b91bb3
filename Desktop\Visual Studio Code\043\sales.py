from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Sale, Car, Customer, Installment, Payment
from datetime import datetime, date, timedelta
try:
    from dateutil.relativedelta import relativedelta
except ImportError:
    # Fallback if dateutil is not available
    def relativedelta(months=0):
        return timedelta(days=months * 30)  # Approximate

# Import notification manager
try:
    from notifications import NotificationManager
except ImportError:
    NotificationManager = None

sales_bp = Blueprint('sales', __name__)

@sales_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    sale_type = request.args.get('sale_type', '')
    
    query = Sale.query.join(Customer).join(Car)
    
    if search:
        query = query.filter(
            Customer.full_name.contains(search) |
            Customer.national_id.contains(search) |
            Car.name.contains(search) |
            Car.brand.contains(search) |
            Car.chassis_number.contains(search)
        )
    
    if status:
        query = query.filter(Sale.status == status)
    
    if sale_type:
        query = query.filter(Sale.sale_type == sale_type)
    
    sales = query.order_by(Sale.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )
    
    return render_template('sales/index.html', 
                         sales=sales, 
                         search=search, 
                         status=status,
                         sale_type=sale_type)

@sales_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if not current_user.has_permission('create_sales'):
        flash('ليس لديك صلاحية لإنشاء عمليات البيع', 'error')
        return redirect(url_for('sales.index'))
    
    if request.method == 'POST':
        # Get form data
        car_id = request.form.get('car_id', type=int)
        customer_id = request.form.get('customer_id', type=int)
        sale_type = request.form.get('sale_type')
        sale_price = request.form.get('sale_price', type=float)
        down_payment = request.form.get('down_payment', type=float, default=0)
        installment_count = request.form.get('installment_count', type=int, default=0)
        installment_frequency = request.form.get('installment_frequency', 'monthly')
        first_installment_date = request.form.get('first_installment_date')
        notes = request.form.get('notes', '').strip()
        
        # Validation
        if not all([car_id, customer_id, sale_type, sale_price]):
            flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
            return render_template('sales/add.html')
        
        # Validate car and customer
        car = Car.query.get(car_id)
        customer = Customer.query.get(customer_id)
        
        if not car:
            flash('السيارة المحددة غير موجودة', 'error')
            return render_template('sales/add.html')
        
        if not customer:
            flash('العميل المحدد غير موجود', 'error')
            return render_template('sales/add.html')
        
        if car.status != 'available':
            flash('السيارة غير متاحة للبيع', 'error')
            return render_template('sales/add.html')
        
        if sale_price <= 0:
            flash('سعر البيع يجب أن يكون أكبر من صفر', 'error')
            return render_template('sales/add.html')
        
        # Validate installment data
        installment_amount = 0
        if sale_type == 'installment':
            if not installment_count or installment_count <= 0:
                flash('عدد الأقساط يجب أن يكون أكبر من صفر', 'error')
                return render_template('sales/add.html')
            
            if not first_installment_date:
                flash('تاريخ أول قسط مطلوب للبيع بالتقسيط', 'error')
                return render_template('sales/add.html')
            
            try:
                first_installment_date = datetime.strptime(first_installment_date, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ أول قسط غير صحيح', 'error')
                return render_template('sales/add.html')
            
            remaining_amount = sale_price - down_payment
            if remaining_amount <= 0:
                flash('المبلغ المتبقي للتقسيط يجب أن يكون أكبر من صفر', 'error')
                return render_template('sales/add.html')
            
            installment_amount = remaining_amount / installment_count
        
        # Create sale
        sale = Sale(
            car_id=car_id,
            customer_id=customer_id,
            sale_type=sale_type,
            sale_price=sale_price,
            down_payment=down_payment,
            installment_amount=installment_amount,
            installment_count=installment_count,
            installment_frequency=installment_frequency,
            first_installment_date=first_installment_date if sale_type == 'installment' else None,
            notes=notes,
            created_by=current_user.id
        )
        
        try:
            db.session.add(sale)
            db.session.flush()  # Get the sale ID
            
            # Update car status
            car.status = 'sold'
            
            # Create installments if installment sale
            if sale_type == 'installment' and installment_count > 0:
                create_installments(sale, first_installment_date, installment_count,
                                  installment_amount, installment_frequency)

            db.session.commit()

            # Create notification for new sale
            if NotificationManager:
                NotificationManager.create_sale_notification(sale)

            flash(f'تم إنشاء عملية البيع بنجاح', 'success')
            return redirect(url_for('sales.view', sale_id=sale.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء عملية البيع', 'error')
    
    # Get available cars and customers for the form
    available_cars = Car.query.filter_by(status='available').all()
    customers = Customer.query.all()

    return render_template('sales/add.html',
                         cars=available_cars,
                         customers=customers,
                         current_datetime=datetime.now())

def create_installments(sale, start_date, count, amount, frequency):
    """Create installment records for a sale"""
    current_date = start_date
    
    for i in range(count):
        installment = Installment(
            sale_id=sale.id,
            installment_number=i + 1,
            due_date=current_date,
            amount=amount
        )
        db.session.add(installment)
        
        # Calculate next installment date
        if frequency == 'monthly':
            current_date = current_date + relativedelta(months=1)
        elif frequency == 'weekly':
            current_date = current_date + timedelta(weeks=1)

@sales_bp.route('/<int:sale_id>')
@login_required
def view(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    # Get installments
    installments = Installment.query.filter_by(sale_id=sale_id).order_by(Installment.installment_number).all()
    
    # Get payments
    payments = Payment.query.filter_by(sale_id=sale_id).order_by(Payment.created_at.desc()).all()
    
    # Calculate payment summary
    total_paid = sum(payment.amount for payment in payments)
    remaining_amount = sale.sale_price - total_paid
    
    return render_template('sales/view.html', 
                         sale=sale, 
                         installments=installments, 
                         payments=payments,
                         total_paid=total_paid,
                         remaining_amount=remaining_amount)

@sales_bp.route('/<int:sale_id>/installments')
@login_required
def installments(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    
    query = Installment.query.filter_by(sale_id=sale_id)
    
    if status:
        query = query.filter(Installment.status == status)
    
    installments = query.order_by(Installment.installment_number).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('sales/installments.html',
                         sale=sale,
                         installments=installments,
                         status=status,
                         current_date=date.today())

@sales_bp.route('/<int:sale_id>/payments/add', methods=['GET', 'POST'])
@login_required
def add_payment(sale_id):
    if not current_user.has_permission('payments'):
        flash('ليس لديك صلاحية لإضافة المدفوعات', 'error')
        return redirect(url_for('sales.view', sale_id=sale_id))
    
    sale = Sale.query.get_or_404(sale_id)
    
    if request.method == 'POST':
        amount = request.form.get('amount', type=float)
        payment_method = request.form.get('payment_method')
        payment_date = request.form.get('payment_date')
        reference_number = request.form.get('reference_number', '').strip()
        notes = request.form.get('notes', '').strip()
        installment_id = request.form.get('installment_id', type=int)
        
        # Validation
        if not all([amount, payment_method, payment_date]):
            flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
            return render_template('sales/add_payment.html', sale=sale)
        
        if amount <= 0:
            flash('مبلغ الدفع يجب أن يكون أكبر من صفر', 'error')
            return render_template('sales/add_payment.html', sale=sale)
        
        try:
            payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
        except ValueError:
            flash('تاريخ الدفع غير صحيح', 'error')
            return render_template('sales/add_payment.html', sale=sale)
        
        # Create payment
        payment = Payment(
            sale_id=sale_id,
            installment_id=installment_id if installment_id else None,
            amount=amount,
            payment_method=payment_method,
            payment_date=payment_date,
            reference_number=reference_number,
            notes=notes,
            created_by=current_user.id
        )
        
        try:
            db.session.add(payment)
            
            # Update installment status if payment is for specific installment
            if installment_id:
                installment = Installment.query.get(installment_id)
                if installment:
                    installment.paid_amount += amount
                    installment.paid_date = payment_date
                    
                    if installment.paid_amount >= installment.amount:
                        installment.status = 'paid'
                    else:
                        installment.status = 'partial'
            
            db.session.commit()
            flash(f'تم إضافة الدفعة بمبلغ {amount:,.0f} ريال بنجاح', 'success')
            return redirect(url_for('sales.view', sale_id=sale_id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة الدفعة', 'error')
    
    # Get pending installments for this sale
    pending_installments = Installment.query.filter_by(
        sale_id=sale_id,
        status='pending'
    ).order_by(Installment.installment_number).all()

    # Calculate remaining amount
    payments = Payment.query.filter_by(sale_id=sale_id).all()
    total_paid = sum(payment.amount for payment in payments) + (sale.down_payment or 0)
    remaining_amount = sale.sale_price - total_paid

    return render_template('sales/add_payment.html',
                         sale=sale,
                         pending_installments=pending_installments,
                         remaining_amount=remaining_amount,
                         total_paid=total_paid,
                         current_date=date.today())

@sales_bp.route('/installments/<int:installment_id>/pay', methods=['POST'])
@login_required
def pay_installment(installment_id):
    if not current_user.has_permission('payments'):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية'})
    
    installment = Installment.query.get_or_404(installment_id)
    
    data = request.get_json()
    amount = data.get('amount', type=float)
    payment_method = data.get('payment_method')
    reference_number = data.get('reference_number', '')
    
    if not amount or amount <= 0:
        return jsonify({'success': False, 'message': 'مبلغ الدفع غير صحيح'})
    
    if not payment_method:
        return jsonify({'success': False, 'message': 'طريقة الدفع مطلوبة'})
    
    try:
        # Create payment
        payment = Payment(
            sale_id=installment.sale_id,
            installment_id=installment_id,
            amount=amount,
            payment_method=payment_method,
            payment_date=date.today(),
            reference_number=reference_number,
            created_by=current_user.id
        )
        
        db.session.add(payment)
        
        # Update installment
        installment.paid_amount += amount
        installment.paid_date = date.today()
        
        if installment.paid_amount >= installment.amount:
            installment.status = 'paid'
        else:
            installment.status = 'partial'
        
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'تم دفع {amount:,.0f} ريال بنجاح',
            'new_status': installment.status,
            'paid_amount': installment.paid_amount
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء الدفع'})

@sales_bp.route('/<int:sale_id>/cancel', methods=['POST'])
@login_required
def cancel_sale(sale_id):
    if not current_user.has_permission('edit_all'):
        flash('ليس لديك صلاحية لإلغاء عمليات البيع', 'error')
        return redirect(url_for('sales.view', sale_id=sale_id))
    
    sale = Sale.query.get_or_404(sale_id)
    
    if sale.status == 'cancelled':
        flash('عملية البيع ملغاة بالفعل', 'warning')
        return redirect(url_for('sales.view', sale_id=sale_id))
    
    try:
        # Cancel sale
        sale.status = 'cancelled'
        
        # Make car available again
        car = Car.query.get(sale.car_id)
        if car:
            car.status = 'available'
        
        # Cancel pending installments
        Installment.query.filter_by(
            sale_id=sale_id, 
            status='pending'
        ).update({'status': 'cancelled'})
        
        db.session.commit()
        flash('تم إلغاء عملية البيع بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إلغاء عملية البيع', 'error')
    
    return redirect(url_for('sales.view', sale_id=sale_id))

@sales_bp.route('/overdue')
@login_required
def overdue_installments():
    """View overdue installments"""
    page = request.args.get('page', 1, type=int)
    
    today = date.today()
    overdue_installments = Installment.query.join(Sale).join(Customer).filter(
        Installment.due_date < today,
        Installment.status == 'pending'
    ).order_by(Installment.due_date.asc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('sales/overdue.html', installments=overdue_installments)
