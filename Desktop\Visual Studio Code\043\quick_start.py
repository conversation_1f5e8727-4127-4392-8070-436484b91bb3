import os
import sys

# Set environment variables
os.environ['WHATSAPP_DEMO_MODE'] = 'true'

try:
    print("🚀 بدء تشغيل نظام معرض السيارات...")
    print("=" * 50)

    from app import create_app
    app = create_app()

    print("✅ تم إنشاء التطبيق بنجاح")
    print("🎉 النظام جاهز للاستخدام!")
    print("=" * 50)
    print("🌐 الروابط المهمة:")
    print("   • الصفحة الرئيسية: http://localhost:5000")
    print("   • لوحة التحكم: http://localhost:5000/dashboard")
    print("   • السيارات: http://localhost:5000/cars")
    print("   • أرقام السيارات: http://localhost:5000/plate-numbers")
    print("   • العملاء: http://localhost:5000/customers")
    print("   • المبيعات: http://localhost:5000/sales")
    print("   • العقود: http://localhost:5000/contracts")
    print("   • التقارير: http://localhost:5000/reports")
    print("   • واتساب: http://localhost:5000/whatsapp")
    print("=" * 50)
    print("👤 بيانات تسجيل الدخول:")
    print("   • اسم المستخدم: admin")
    print("   • كلمة المرور: admin123")
    print("=" * 50)
    print("🆕 الميزات الجديدة:")
    print("   • قسم أرقام السيارات القطرية")
    print("   • نظام مزايدات للأرقام المميزة")
    print("   • تحليل تلقائي للأرقام الخاصة")
    print("   • إدارة شاملة للمبيعات والعقود")
    print("=" * 50)
    print("📱 واتساب: وضع التجريب مفعل")
    print("💰 إدخال الأسعار: محسن ومطور")
    print("🎨 الألوان: قائمة شاملة مضافة")
    print("\n🛑 اضغط Ctrl+C للإيقاف")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
