#!/usr/bin/env python3
"""
تحديث النظام لدعم الأرقام العربية والكتابة العشوائية
Update system to support Arabic numbers and random typing
"""

import os
import sys
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_system():
    """Update system with Arabic numbers support"""
    print("🔄 تحديث النظام لدعم الأرقام العربية...")
    print("=" * 60)
    
    try:
        # Check if required files exist
        required_files = [
            'static/js/arabic-numbers.js',
            'static/css/arabic-numbers.css',
            'templates/arabic-numbers-test.html'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ الملفات التالية مفقودة:")
            for file in missing_files:
                print(f"   • {file}")
            return False
        
        print("✅ جميع الملفات المطلوبة موجودة")
        
        # Test Arabic number conversion
        test_arabic_conversion()
        
        # Test file permissions
        test_file_permissions()
        
        # Create backup
        create_backup()
        
        print("\n🎉 تم تحديث النظام بنجاح!")
        print("=" * 60)
        print("🌟 الميزات الجديدة:")
        print("   • تحويل تلقائي للأرقام إلى العربية")
        print("   • تأثيرات الكتابة العشوائية")
        print("   • تحريك الأرقام والإحصائيات")
        print("   • تنسيق محسن للعملة القطرية")
        print("   • أرقام متوهجة ونابضة")
        print("   • أرقام متحركة في الخلفية")
        print("=" * 60)
        print("🔗 الروابط الجديدة:")
        print("   • صفحة الاختبار: http://localhost:5000/arabic-numbers-test")
        print("   • أرقام السيارات: http://localhost:5000/plate-numbers")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحديث: {str(e)}")
        return False

def test_arabic_conversion():
    """Test Arabic number conversion functions"""
    print("\n📝 اختبار تحويل الأرقام...")
    
    # Test data
    test_numbers = [
        ("123456", "١٢٣٤٥٦"),
        ("0", "٠"),
        ("987.65", "٩٨٧٫٦٥"),
        ("1,234,567", "١،٢٣٤،٥٦٧")
    ]
    
    # Arabic digits mapping
    arabic_digits = {
        '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
        '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩',
        '.': '٫', ',': '،'
    }
    
    def to_arabic(text):
        for english, arabic in arabic_digits.items():
            text = text.replace(english, arabic)
        return text
    
    all_passed = True
    for english, expected_arabic in test_numbers:
        result = to_arabic(english)
        if result == expected_arabic:
            print(f"   ✅ {english} → {result}")
        else:
            print(f"   ❌ {english} → {result} (متوقع: {expected_arabic})")
            all_passed = False
    
    if all_passed:
        print("   🎉 جميع اختبارات التحويل نجحت!")
    else:
        print("   ⚠️ بعض اختبارات التحويل فشلت")
    
    return all_passed

def test_file_permissions():
    """Test file permissions"""
    print("\n🔒 فحص صلاحيات الملفات...")
    
    files_to_check = [
        'static/js/arabic-numbers.js',
        'static/css/arabic-numbers.css',
        'templates/arabic-numbers-test.html',
        'templates/base.html'
    ]
    
    all_readable = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            if os.access(file_path, os.R_OK):
                print(f"   ✅ {file_path} - قابل للقراءة")
            else:
                print(f"   ❌ {file_path} - غير قابل للقراءة")
                all_readable = False
        else:
            print(f"   ⚠️ {file_path} - غير موجود")
            all_readable = False
    
    return all_readable

def create_backup():
    """Create backup of important files"""
    print("\n💾 إنشاء نسخة احتياطية...")
    
    import shutil
    from datetime import datetime
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # Files to backup
        backup_files = [
            'app.py',
            'templates/base.html',
            'models.py'
        ]
        
        for file_path in backup_files:
            if os.path.exists(file_path):
                shutil.copy2(file_path, backup_dir)
                print(f"   ✅ تم نسخ {file_path}")
        
        print(f"   💾 النسخة الاحتياطية في: {backup_dir}")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في النسخ الاحتياطي: {str(e)}")
        return False

def show_usage_examples():
    """Show usage examples"""
    print("\n📚 أمثلة الاستخدام:")
    print("=" * 40)
    
    print("🔢 تحويل الأرقام في JavaScript:")
    print("```javascript")
    print("// تحويل إلى العربية")
    print("ArabicNumbers.toArabic('123456'); // ١٢٣٤٥٦")
    print("")
    print("// تحويل إلى الإنجليزية") 
    print("ArabicNumbers.toEnglish('١٢٣٤٥٦'); // 123456")
    print("")
    print("// تنسيق العملة")
    print("ArabicNumbers.formatCurrency(123456); // ١٢٣،٤٥٦ ر.ق")
    print("```")
    
    print("\n✨ الكتابة العشوائية:")
    print("```javascript")
    print("// إنشاء تأثير كتابة")
    print("new ArabicNumbers.RandomTyping(element, 'النص المراد كتابته', {")
    print("    speed: 100,")
    print("    randomDelay: 50,")
    print("    showCursor: true")
    print("}).start();")
    print("```")
    
    print("\n🎨 في HTML:")
    print("```html")
    print("<!-- أرقام بالعربية -->")
    print("<span class='arabic-number'>{{ price|arabic_number }}</span>")
    print("")
    print("<!-- كتابة عشوائية -->")
    print("<h1 class='random-typing'>العنوان</h1>")
    print("")
    print("<!-- أرقام متحركة -->")
    print("<div class='animate-number'>123456</div>")
    print("```")

def check_browser_compatibility():
    """Check browser compatibility"""
    print("\n🌐 متطلبات المتصفح:")
    print("=" * 30)
    print("✅ Chrome 60+")
    print("✅ Firefox 55+") 
    print("✅ Safari 12+")
    print("✅ Edge 79+")
    print("⚠️ Internet Explorer غير مدعوم")
    
    print("\n📱 الأجهزة المحمولة:")
    print("✅ iOS Safari 12+")
    print("✅ Chrome Mobile 60+")
    print("✅ Samsung Internet 8+")

def performance_tips():
    """Show performance tips"""
    print("\n⚡ نصائح الأداء:")
    print("=" * 25)
    print("• استخدم فئة 'no-arabic-convert' لتجاهل التحويل")
    print("• قم بتعطيل التحريك للأجهزة البطيئة")
    print("• استخدم 'english-only' للنصوص الإنجليزية")
    print("• تجنب الكثير من الأرقام المتحركة في نفس الوقت")

def troubleshooting():
    """Show troubleshooting guide"""
    print("\n🔧 حل المشاكل الشائعة:")
    print("=" * 35)
    print("❓ الأرقام لا تتحول للعربية:")
    print("   • تأكد من تحميل arabic-numbers.js")
    print("   • تحقق من وجود أخطاء في وحدة التحكم")
    print("   • تأكد من عدم وجود فئة 'no-arabic-convert'")
    print("")
    print("❓ الكتابة العشوائية لا تعمل:")
    print("   • تأكد من استدعاء الدالة بعد تحميل الصفحة")
    print("   • تحقق من وجود العنصر في DOM")
    print("   • تأكد من صحة النص المرسل")
    print("")
    print("❓ التحريك بطيء:")
    print("   • قلل عدد العناصر المتحركة")
    print("   • استخدم CSS transforms بدلاً من تغيير الخصائص")
    print("   • فعل GPU acceleration")

if __name__ == '__main__':
    print("🚀 بدء تحديث نظام الأرقام العربية...")
    print("=" * 60)
    
    if update_system():
        show_usage_examples()
        check_browser_compatibility()
        performance_tips()
        troubleshooting()
        
        print("\n" + "=" * 60)
        print("🎉 التحديث مكتمل! النظام جاهز للاستخدام")
        print("🌐 للاختبار: http://localhost:5000/arabic-numbers-test")
        print("=" * 60)
    else:
        print("\n❌ فشل في التحديث. يرجى مراجعة الأخطاء أعلاه.")
        sys.exit(1)
