/**
 * Simple Price Handler - Direct solution for price input issues
 * Works independently of InputValidator
 */

class SimplePriceHandler {
    constructor() {
        this.init();
    }
    
    init() {
        console.log('🔧 Simple Price Handler initializing...');
        this.setupPriceInputs();
        console.log('✅ Simple Price Handler ready');
    }
    
    setupPriceInputs() {
        // Find all price/currency inputs
        const selectors = [
            '.currency-input',
            'input[data-format="currency"]',
            '#price',
            '#cost_price', 
            '#sale_price',
            '#down_payment',
            '#installment_amount'
        ];
        
        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(input => {
                this.setupSingleInput(input);
            });
        });
    }
    
    setupSingleInput(input) {
        // Ensure input type is text for proper handling
        input.type = 'text';
        
        // Remove any existing event listeners by cloning
        const newInput = input.cloneNode(true);
        input.parentNode.replaceChild(newInput, input);
        
        // Setup new event listeners
        this.addEventListeners(newInput);
        
        console.log(`✅ Setup price input: ${newInput.id || newInput.name || 'unnamed'}`);
    }
    
    addEventListeners(input) {
        // Input event - clean as user types
        input.addEventListener('input', (e) => {
            const cleaned = this.cleanPriceInput(e.target.value);
            if (cleaned !== e.target.value) {
                const cursorPos = e.target.selectionStart;
                e.target.value = cleaned;
                // Restore cursor position
                const newPos = Math.min(cursorPos, cleaned.length);
                e.target.setSelectionRange(newPos, newPos);
            }
            this.clearError(input);
        });
        
        // Focus event - remove formatting for editing
        input.addEventListener('focus', (e) => {
            const value = e.target.value.replace(/[^\d.]/g, '');
            e.target.value = value;
            this.clearError(input);
        });
        
        // Blur event - format for display and validate
        input.addEventListener('blur', (e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value) && value > 0) {
                e.target.value = this.formatPrice(value);
            }
            this.validateInput(input);
        });
        
        // Paste event - handle pasted content
        input.addEventListener('paste', (e) => {
            e.preventDefault();
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');
            const cleaned = this.cleanPriceInput(pastedText);
            input.value = cleaned;
            input.dispatchEvent(new Event('input'));
        });
    }
    
    cleanPriceInput(value) {
        if (!value) return '';
        
        // Convert Arabic digits to English
        let cleaned = String(value);
        const arabicDigits = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        };
        
        for (let arabic in arabicDigits) {
            cleaned = cleaned.replace(new RegExp(arabic, 'g'), arabicDigits[arabic]);
        }
        
        // Remove all non-numeric characters except decimal point
        cleaned = cleaned.replace(/[^\d.]/g, '');
        
        // Handle multiple decimal points
        const parts = cleaned.split('.');
        if (parts.length > 2) {
            cleaned = parts[0] + '.' + parts.slice(1).join('');
        }
        
        // Limit decimal places to 2
        if (parts.length === 2 && parts[1].length > 2) {
            cleaned = parts[0] + '.' + parts[1].substring(0, 2);
        }
        
        return cleaned;
    }
    
    formatPrice(amount) {
        if (!amount || isNaN(amount)) return '';
        
        // Format with commas
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    }
    
    validateInput(input) {
        const value = parseFloat(input.value.replace(/[^\d.]/g, ''));
        
        if (input.required && (!input.value || isNaN(value) || value <= 0)) {
            this.showError(input, 'يجب إدخال مبلغ صحيح أكبر من صفر');
            return false;
        }
        
        this.clearError(input);
        return true;
    }
    
    showError(input, message) {
        this.clearError(input);
        
        input.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback simple-price-error';
        errorDiv.textContent = message;
        
        const inputGroup = input.closest('.input-group');
        const target = inputGroup || input;
        target.parentNode.insertBefore(errorDiv, target.nextSibling);
    }
    
    clearError(input) {
        input.classList.remove('is-invalid');
        
        const errorDiv = input.parentNode.querySelector('.simple-price-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    // Public method to get numeric value
    getNumericValue(input) {
        const value = input.value.replace(/[^\d.]/g, '');
        return parseFloat(value) || 0;
    }
    
    // Public method to set value
    setNumericValue(input, value) {
        if (!isNaN(value) && value >= 0) {
            input.value = this.formatPrice(value);
        }
    }
    
    // Public method to refresh all inputs
    refresh() {
        this.setupPriceInputs();
    }
}

// Initialize immediately when script loads
document.addEventListener('DOMContentLoaded', () => {
    window.simplePriceHandler = new SimplePriceHandler();
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading
} else {
    // DOM is already loaded
    window.simplePriceHandler = new SimplePriceHandler();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimplePriceHandler;
}
