#!/usr/bin/env python3
"""
Number Formatting Fix Script for Qatar Car Dealership System
Fixes precision issues and improves number display
"""

import os
import sys
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Sale, Car, Customer, Installment, Payment

class NumberFixer:
    def __init__(self):
        self.app = create_app()
        
    def fix_precision_issues(self):
        """Fix floating point precision issues in database"""
        with self.app.app_context():
            try:
                print("🔧 إصلاح مشاكل دقة الأرقام...")
                
                # Fix sale prices
                sales = Sale.query.all()
                fixed_sales = 0
                
                for sale in sales:
                    original_price = sale.sale_price
                    
                    # Round to 2 decimal places
                    if isinstance(original_price, float):
                        # Use Decimal for precise rounding
                        decimal_price = Decimal(str(original_price))
                        rounded_price = decimal_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        # If it's essentially a whole number, make it an integer
                        if rounded_price % 1 == 0:
                            sale.sale_price = int(rounded_price)
                        else:
                            sale.sale_price = float(rounded_price)
                        
                        if sale.sale_price != original_price:
                            fixed_sales += 1
                            print(f"   📊 Sale {sale.id}: {original_price} → {sale.sale_price}")
                    
                    # Fix down payment
                    if sale.down_payment and isinstance(sale.down_payment, float):
                        decimal_payment = Decimal(str(sale.down_payment))
                        rounded_payment = decimal_payment.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        if rounded_payment % 1 == 0:
                            sale.down_payment = int(rounded_payment)
                        else:
                            sale.down_payment = float(rounded_payment)
                    
                    # Fix installment amount
                    if sale.installment_amount and isinstance(sale.installment_amount, float):
                        decimal_installment = Decimal(str(sale.installment_amount))
                        rounded_installment = decimal_installment.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        if rounded_installment % 1 == 0:
                            sale.installment_amount = int(rounded_installment)
                        else:
                            sale.installment_amount = float(rounded_installment)
                
                # Fix car prices
                cars = Car.query.all()
                fixed_cars = 0
                
                for car in cars:
                    if car.price and isinstance(car.price, float):
                        original_price = car.price
                        decimal_price = Decimal(str(original_price))
                        rounded_price = decimal_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        if rounded_price % 1 == 0:
                            car.price = int(rounded_price)
                        else:
                            car.price = float(rounded_price)
                        
                        if car.price != original_price:
                            fixed_cars += 1
                            print(f"   🚗 Car {car.id}: {original_price} → {car.price}")
                
                # Fix installment amounts
                installments = Installment.query.all()
                fixed_installments = 0
                
                for installment in installments:
                    # Fix amount
                    if installment.amount and isinstance(installment.amount, float):
                        original_amount = installment.amount
                        decimal_amount = Decimal(str(original_amount))
                        rounded_amount = decimal_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        if rounded_amount % 1 == 0:
                            installment.amount = int(rounded_amount)
                        else:
                            installment.amount = float(rounded_amount)
                        
                        if installment.amount != original_amount:
                            fixed_installments += 1
                            print(f"   💰 Installment {installment.id}: {original_amount} → {installment.amount}")
                    
                    # Fix paid amount
                    if installment.paid_amount and isinstance(installment.paid_amount, float):
                        decimal_paid = Decimal(str(installment.paid_amount))
                        rounded_paid = decimal_paid.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        if rounded_paid % 1 == 0:
                            installment.paid_amount = int(rounded_paid)
                        else:
                            installment.paid_amount = float(rounded_paid)
                
                # Fix payment amounts
                payments = Payment.query.all()
                fixed_payments = 0
                
                for payment in payments:
                    if payment.amount and isinstance(payment.amount, float):
                        original_amount = payment.amount
                        decimal_amount = Decimal(str(original_amount))
                        rounded_amount = decimal_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                        
                        if rounded_amount % 1 == 0:
                            payment.amount = int(rounded_amount)
                        else:
                            payment.amount = float(rounded_amount)
                        
                        if payment.amount != original_amount:
                            fixed_payments += 1
                            print(f"   💳 Payment {payment.id}: {original_amount} → {payment.amount}")
                
                # Commit changes
                db.session.commit()
                
                print(f"\n✅ تم إصلاح:")
                print(f"   📊 {fixed_sales} عملية بيع")
                print(f"   🚗 {fixed_cars} سيارة")
                print(f"   💰 {fixed_installments} قسط")
                print(f"   💳 {fixed_payments} دفعة")
                
                return True
                
            except Exception as e:
                db.session.rollback()
                print(f"❌ خطأ في إصلاح الأرقام: {e}")
                return False
    
    def validate_numbers(self):
        """Validate all numbers in the database"""
        with self.app.app_context():
            try:
                print("🔍 فحص صحة الأرقام...")
                
                issues = []
                
                # Check sales
                sales = Sale.query.all()
                for sale in sales:
                    if sale.sale_price and (sale.sale_price <= 0 or sale.sale_price > 10000000):
                        issues.append(f"Sale {sale.id}: سعر غير منطقي {sale.sale_price}")
                    
                    if sale.down_payment and sale.down_payment < 0:
                        issues.append(f"Sale {sale.id}: دفعة مقدمة سالبة {sale.down_payment}")
                    
                    if sale.installment_amount and sale.installment_amount <= 0:
                        issues.append(f"Sale {sale.id}: قسط غير صحيح {sale.installment_amount}")
                
                # Check cars
                cars = Car.query.all()
                for car in cars:
                    if car.price and (car.price <= 0 or car.price > 10000000):
                        issues.append(f"Car {car.id}: سعر غير منطقي {car.price}")
                
                # Check installments
                installments = Installment.query.all()
                for installment in installments:
                    if installment.amount and installment.amount <= 0:
                        issues.append(f"Installment {installment.id}: مبلغ غير صحيح {installment.amount}")
                    
                    if installment.paid_amount and installment.paid_amount < 0:
                        issues.append(f"Installment {installment.id}: مبلغ مدفوع سالب {installment.paid_amount}")
                
                # Check payments
                payments = Payment.query.all()
                for payment in payments:
                    if payment.amount and payment.amount <= 0:
                        issues.append(f"Payment {payment.id}: مبلغ غير صحيح {payment.amount}")
                
                if issues:
                    print("⚠️ مشاكل تم العثور عليها:")
                    for issue in issues:
                        print(f"   {issue}")
                else:
                    print("✅ جميع الأرقام صحيحة")
                
                return len(issues) == 0
                
            except Exception as e:
                print(f"❌ خطأ في فحص الأرقام: {e}")
                return False
    
    def generate_test_data(self):
        """Generate test data with proper number formatting"""
        with self.app.app_context():
            try:
                print("📊 إنشاء بيانات اختبار...")
                
                # Create test customer
                test_customer = Customer.query.filter_by(national_id='12345678901').first()
                if not test_customer:
                    test_customer = Customer(
                        full_name='عميل اختبار الأرقام',
                        national_id='12345678901',
                        phone='55123456',
                        email='<EMAIL>',
                        address='الدوحة، قطر'
                    )
                    db.session.add(test_customer)
                    db.session.flush()
                
                # Create test car
                test_car = Car.query.filter_by(chassis_number='TEST123456').first()
                if not test_car:
                    test_car = Car(
                        name='سيارة اختبار',
                        brand='تويوتا',
                        model='كامري',
                        year=2023,
                        chassis_number='TEST123456',
                        price=75000,  # Clean integer
                        color='أبيض',
                        status='available'
                    )
                    db.session.add(test_car)
                    db.session.flush()
                
                # Create test sale with clean numbers
                test_sale = Sale(
                    car_id=test_car.id,
                    customer_id=test_customer.id,
                    sale_type='installment',
                    sale_price=75000,  # Clean integer
                    down_payment=15000,  # Clean integer
                    installment_amount=2500,  # Clean integer
                    installment_count=24,
                    installment_frequency='monthly',
                    first_installment_date=datetime.now().date(),
                    created_by=1
                )
                
                db.session.add(test_sale)
                db.session.commit()
                
                print("✅ تم إنشاء بيانات اختبار بأرقام صحيحة")
                return True
                
            except Exception as e:
                db.session.rollback()
                print(f"❌ خطأ في إنشاء بيانات الاختبار: {e}")
                return False
    
    def show_number_examples(self):
        """Show examples of number formatting"""
        print("📋 أمثلة على تنسيق الأرقام:")
        print("=" * 40)
        
        test_numbers = [
            1666.6666666666667,  # The problematic number from the image
            50000,
            125000.50,
            1500.00,
            999.99,
            0,
            -500
        ]
        
        for num in test_numbers:
            # Apply the same logic as our filters
            if isinstance(num, float):
                rounded = round(num, 2)
                if rounded == int(rounded):
                    clean_num = int(rounded)
                else:
                    clean_num = rounded
            else:
                clean_num = num
            
            print(f"الأصلي: {num}")
            print(f"المُصحح: {clean_num}")
            print(f"العملة: {clean_num:,.0f} ريال قطري")
            print("-" * 20)

def main():
    """Main function"""
    print("🔧 إصلاح مشاكل الأرقام - معرض بوخليفة للسيارات")
    print("=" * 55)
    
    fixer = NumberFixer()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'fix':
            fixer.fix_precision_issues()
        elif command == 'validate':
            fixer.validate_numbers()
        elif command == 'test':
            fixer.generate_test_data()
        elif command == 'examples':
            fixer.show_number_examples()
        elif command == 'all':
            print("🚀 تشغيل جميع العمليات...")
            fixer.show_number_examples()
            fixer.fix_precision_issues()
            fixer.validate_numbers()
            fixer.generate_test_data()
            print("\n🎉 تم إكمال جميع العمليات!")
        else:
            print("❌ أمر غير معروف")
            print_usage()
    else:
        print_usage()

def print_usage():
    """Print usage instructions"""
    print("\nالاستخدام:")
    print("  python fix_numbers.py fix       - إصلاح مشاكل الدقة")
    print("  python fix_numbers.py validate  - فحص صحة الأرقام")
    print("  python fix_numbers.py test      - إنشاء بيانات اختبار")
    print("  python fix_numbers.py examples  - عرض أمثلة التنسيق")
    print("  python fix_numbers.py all       - تشغيل جميع العمليات")
    print("\nأمثلة:")
    print("  python fix_numbers.py fix")
    print("  python fix_numbers.py validate")

if __name__ == "__main__":
    main()
