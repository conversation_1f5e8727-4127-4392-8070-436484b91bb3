{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إضافة مستخدم جديد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">بيانات المستخدم</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="userForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control phone-input" id="phone" name="phone">
                                <div class="form-text">مثال: +966501234567</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">اختر الدور</option>
                                    <option value="manager">مدير</option>
                                    <option value="sales">موظف مبيعات</option>
                                    <option value="accountant">محاسب</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Role Permissions Preview -->
                    <div class="row mt-4" id="rolePermissions" style="display: none;">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>صلاحيات هذا الدور:</h6>
                                <ul id="permissionsList" class="mb-0"></ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المستخدم
                        </button>
                        <a href="{{ url_for('auth.users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Panel -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">نصائح مهمة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                    <ul class="mb-0">
                        <li>اسم المستخدم يجب أن يكون فريداً</li>
                        <li>كلمة المرور يجب أن تكون قوية</li>
                        <li>اختر الدور المناسب للمستخدم</li>
                        <li>تأكد من صحة البريد الإلكتروني</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                    <p class="mb-0">الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة.</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الأدوار والصلاحيات</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-danger">المدير</h6>
                    <small class="text-muted">جميع الصلاحيات + إدارة المستخدمين</small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-success">موظف المبيعات</h6>
                    <small class="text-muted">إدارة السيارات والعملاء والمبيعات</small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-info">المحاسب</h6>
                    <small class="text-muted">المدفوعات والتقارير المالية</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#togglePassword').on('click', function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Show role permissions
    $('#role').on('change', function() {
        const role = $(this).val();
        const permissionsDiv = $('#rolePermissions');
        const permissionsList = $('#permissionsList');
        
        if (role) {
            const permissions = getRolePermissions(role);
            permissionsList.html(permissions.map(p => `<li>${p}</li>`).join(''));
            permissionsDiv.show();
        } else {
            permissionsDiv.hide();
        }
    });
    
    // Form validation
    $('#userForm').on('submit', function(e) {
        if (!validateUserForm()) {
            e.preventDefault();
        }
    });
    
    // Username validation
    $('#username').on('input', function() {
        let value = $(this).val().replace(/[^a-zA-Z0-9_]/g, '');
        $(this).val(value);
    });
});

function getRolePermissions(role) {
    const permissions = {
        'manager': [
            'جميع الصلاحيات',
            'إدارة المستخدمين',
            'الإعدادات العامة',
            'حذف البيانات'
        ],
        'sales': [
            'عرض وتعديل السيارات',
            'عرض وتعديل العملاء',
            'إنشاء المبيعات',
            'عرض التقارير'
        ],
        'accountant': [
            'عرض جميع البيانات',
            'إدارة المدفوعات',
            'التقارير المالية',
            'متابعة الأقساط'
        ]
    };
    
    return permissions[role] || [];
}

function validateUserForm() {
    let isValid = true;
    
    // Validate username
    const username = $('#username').val().trim();
    if (username.length < 3) {
        showFieldError($('#username'), 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
        isValid = false;
    }
    
    // Validate password
    const password = $('#password').val();
    if (password.length < 6) {
        showFieldError($('#password'), 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        isValid = false;
    }
    
    // Validate email
    const email = $('#email').val().trim();
    if (!isValidEmail(email)) {
        showFieldError($('#email'), 'البريد الإلكتروني غير صحيح');
        isValid = false;
    }
    
    // Validate phone if provided
    const phone = $('#phone').val().trim();
    if (phone && !isValidPhone(phone)) {
        showFieldError($('#phone'), 'رقم الهاتف غير صحيح');
        isValid = false;
    }
    
    return isValid;
}
</script>
{% endblock %}
