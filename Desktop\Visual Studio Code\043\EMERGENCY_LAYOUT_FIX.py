#!/usr/bin/env python3
"""
🚨 إصلاح طارئ فوري للتخطيط والنصوص
Emergency Layout and Text Fix
"""

import os
import sys
import webbrowser
import time
import threading
from datetime import datetime

def print_emergency_banner():
    """Print emergency fix banner"""
    print("🚨" + "=" * 60 + "🚨")
    print("🔧      إصلاح طارئ فوري - نظام معرض السيارات      🔧")
    print("⚡      Emergency Fix - Car Dealership System      ⚡")
    print("🚨" + "=" * 60 + "🚨")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🚨 تم اكتشاف مشكلة في العرض!")
    print("⚡ جاري تطبيق الإصلاح الطارئ...")
    print()
    print("🔧 الإصلاحات المطبقة:")
    print("   ✅ إصلاح التخطيط RTL")
    print("   ✅ إصلاح الشريط الجانبي")
    print("   ✅ إصلاح النصوص والخطوط")
    print("   ✅ إصلاح الأيقونات")
    print("   ✅ إصلاح الألوان")
    print("🚨" + "=" * 60 + "🚨")

def apply_emergency_fix():
    """Apply emergency CSS fix"""
    print("🔧 تطبيق الإصلاح الطارئ...")
    
    # Create emergency CSS
    emergency_css = """
/* EMERGENCY LAYOUT FIX */
* {
    box-sizing: border-box !important;
}

html, body {
    direction: rtl !important;
    text-align: right !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    background-color: #f8f9fa !important;
}

/* FORCE SIDEBAR */
.sidebar {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    width: 250px !important;
    height: 100vh !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    z-index: 1000 !important;
    overflow-y: auto !important;
    padding: 20px 0 !important;
}

.main-content {
    margin-right: 250px !important;
    padding: 20px !important;
    min-height: 100vh !important;
    background-color: #f8f9fa !important;
}

/* FORCE SIDEBAR CONTENT */
.sidebar * {
    color: white !important;
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
}

.sidebar .nav-link {
    display: block !important;
    padding: 12px 20px !important;
    color: rgba(255,255,255,0.9) !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    margin: 2px 0 !important;
    font-size: 16px !important;
    direction: rtl !important;
    text-align: right !important;
}

.sidebar .nav-link:hover {
    background-color: rgba(255,255,255,0.15) !important;
    color: white !important;
}

.sidebar .nav-link i {
    display: inline-block !important;
    width: 20px !important;
    margin-left: 10px !important;
    text-align: center !important;
}

/* EMOJI ICONS */
.fa-home::before { content: "🏠" !important; font-family: Arial !important; }
.fa-chart-bar::before { content: "📈" !important; font-family: Arial !important; }
.fa-car::before { content: "🚗" !important; font-family: Arial !important; }
.fa-id-card::before { content: "🔢" !important; font-family: Arial !important; }
.fa-users::before { content: "👥" !important; font-family: Arial !important; }
.fa-handshake::before { content: "🤝" !important; font-family: Arial !important; }
.fa-file-contract::before { content: "📋" !important; font-family: Arial !important; }
.fa-whatsapp::before { content: "💬" !important; font-family: Arial !important; }
.fa-bell::before { content: "🔔" !important; font-family: Arial !important; }
.fa-user::before { content: "👤" !important; font-family: Arial !important; }
.fa-cog::before { content: "⚙️" !important; font-family: Arial !important; }
.fa-sign-out-alt::before { content: "🚪" !important; font-family: Arial !important; }

/* SECTION HEADINGS */
.sidebar-heading {
    font-size: 12px !important;
    color: rgba(255,255,255,0.6) !important;
    text-transform: uppercase !important;
    margin: 20px 10px 10px 10px !important;
    padding: 0 10px !important;
    font-weight: 600 !important;
}

.sidebar-section {
    margin-bottom: 20px !important;
}

/* MAIN CONTENT */
.main-content * {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

.main-content h1, .main-content h2, .main-content h3 {
    color: #212529 !important;
    margin-bottom: 20px !important;
}

/* BUTTONS AND FORMS */
.btn {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
}

.form-control {
    direction: rtl !important;
    text-align: right !important;
}

/* TABLES */
.table {
    direction: rtl !important;
}

.table th, .table td {
    text-align: right !important;
}
"""
    
    # Write emergency CSS
    css_dir = 'static/css'
    os.makedirs(css_dir, exist_ok=True)
    
    with open(os.path.join(css_dir, 'emergency-fix.css'), 'w', encoding='utf-8') as f:
        f.write(emergency_css)
    
    print("   ✅ تم إنشاء ملف CSS الطارئ")

def open_browser_delayed():
    """Open browser after delay"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000?emergency=fixed')
        print("🌐 تم فتح المتصفح مع الإصلاح الطارئ!")
    except:
        print("📱 افتح المتصفح يدوياً: http://localhost:5000")

def main():
    """Main function"""
    print_emergency_banner()
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ خطأ: يجب تشغيل هذا السكريبت من مجلد المشروع")
        print("💡 تأكد من أنك في المجلد: Desktop\\Visual Studio Code\\043")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # Apply emergency fix
    apply_emergency_fix()
    
    print("\n🚀 بدء تشغيل النظام مع الإصلاح الطارئ...")
    
    # Set environment variables
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'true'
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    try:
        # Import and create app
        from app import create_app
        app = create_app()
        
        print("✅ تم تحميل النظام مع الإصلاح الطارئ!")
        print("\n🌐 الروابط:")
        print("   🏠 الصفحة الرئيسية: http://localhost:5000")
        print("   📊 لوحة التحكم: http://localhost:5000/dashboard")
        print("   🚗 السيارات: http://localhost:5000/cars")
        print("   👥 العملاء: http://localhost:5000/customers")
        
        print("\n🔐 بيانات الدخول:")
        print("   👤 المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        
        print("\n⚡ الإصلاحات الطارئة المطبقة:")
        print("   ✅ تخطيط RTL مُصحح")
        print("   ✅ شريط جانبي ثابت ومرئي")
        print("   ✅ نصوص واضحة ومقروءة")
        print("   ✅ أيقونات رموز تعبيرية")
        print("   ✅ ألوان متناسقة")
        
        print("\n🛑 للإيقاف: اضغط Ctrl+C")
        print("🚨" + "=" * 60 + "🚨")
        
        # Start browser in background
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask app
        print("⚡ النظام يعمل الآن مع الإصلاح الطارئ...")
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
        print("💾 الإصلاح الطارئ محفوظ")
        print("🔄 لإعادة التشغيل: python EMERGENCY_LAYOUT_FIX.py")
        print("🎉 شكراً لاستخدام النظام!")
        
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد الوحدات: {str(e)}")
        print("\n💡 جرب تثبيت المتطلبات:")
        print("   pip install flask flask-sqlalchemy flask-login")
        input("\nاضغط Enter للخروج...")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n💡 الحلول المقترحة:")
        print("   1. أعد تشغيل الكمبيوتر")
        print("   2. تأكد من أن المنفذ 5000 غير مستخدم")
        print("   3. جرب منفذ مختلف")
        input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        print("🆘 اتصل بالدعم الفني")
        input("اضغط Enter للخروج...")
