{% extends "base.html" %}

{% block title %}قوالب الواتساب - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">قوالب الواتساب</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                <i class="fas fa-plus me-2"></i>
                إضافة قالب جديد
            </button>
        </div>
        <a href="{{ url_for('whatsapp.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للواتساب
        </a>
    </div>
</div>

<!-- Templates Grid -->
<div class="row">
    {% for template in templates %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">{{ template.name }}</h6>
                <span class="badge bg-{% if template.type == 'payment_reminder' %}warning{% elif template.type == 'welcome' %}success{% elif template.type == 'promotion' %}info{% else %}secondary{% endif %}">
                    {% if template.type == 'payment_reminder' %}تذكير دفع
                    {% elif template.type == 'welcome' %}ترحيب
                    {% elif template.type == 'promotion' %}ترويجي
                    {% elif template.type == 'follow_up' %}متابعة
                    {% else %}عام{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="template-preview mb-3">
                    <div class="whatsapp-bubble">
                        <div class="message-text">
                            {{ template.content[:100] }}{% if template.content|length > 100 %}...{% endif %}
                        </div>
                        <div class="message-time">
                            <small class="text-muted">{{ template.created_at.strftime('%H:%M') }}</small>
                        </div>
                    </div>
                </div>
                
                <div class="template-stats">
                    <small class="text-muted">
                        <i class="fas fa-paper-plane me-1"></i>
                        استُخدم {{ template.usage_count or 0 }} مرة
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" 
                            onclick="useTemplate({{ template.id }})">
                        <i class="fas fa-paper-plane me-1"></i>
                        استخدام
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" 
                            onclick="previewTemplate({{ template.id }})">
                        <i class="fas fa-eye me-1"></i>
                        معاينة
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" 
                            onclick="editTemplate({{ template.id }})">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" 
                            onclick="deleteTemplate({{ template.id }})">
                        <i class="fas fa-trash me-1"></i>
                        حذف
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h5>لا توجد قوالب</h5>
            <p class="text-muted">لم يتم إنشاء أي قوالب بعد. ابدأ بإنشاء قالبك الأول!</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                <i class="fas fa-plus me-2"></i>
                إنشاء قالب جديد
            </button>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Add Template Modal -->
<div class="modal fade" id="addTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قالب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('whatsapp.templates') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_name" class="form-label">اسم القالب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="template_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_type" class="form-label">نوع القالب <span class="text-danger">*</span></label>
                                <select class="form-select" id="template_type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="payment_reminder">تذكير بالدفع</option>
                                    <option value="welcome">رسالة ترحيب</option>
                                    <option value="follow_up">متابعة</option>
                                    <option value="promotion">عرض ترويجي</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="template_content" class="form-label">محتوى القالب <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="template_content" name="content" rows="6" required
                                          placeholder="اكتب محتوى القالب هنا..."></textarea>
                                <div class="form-text">
                                    يمكنك استخدام المتغيرات: {customer_name}, {company_name}, {today}, {time}, {amount}
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="template_description" class="form-label">وصف القالب</label>
                                <input type="text" class="form-control" id="template_description" name="description"
                                       placeholder="وصف مختصر للقالب...">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Preview -->
                    <div class="mt-4">
                        <h6>معاينة القالب:</h6>
                        <div class="whatsapp-preview-modal">
                            <div class="chat-bubble-modal">
                                <div class="message-content-modal" id="templatePreview">
                                    اكتب محتوى القالب لرؤية المعاينة...
                                </div>
                                <div class="message-time-modal">
                                    <small class="text-muted">الآن</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ القالب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('whatsapp.templates') }}">
                <input type="hidden" id="edit_template_id" name="template_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_template_name" class="form-label">اسم القالب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_template_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_template_type" class="form-label">نوع القالب <span class="text-danger">*</span></label>
                                <select class="form-select" id="edit_template_type" name="type" required>
                                    <option value="payment_reminder">تذكير بالدفع</option>
                                    <option value="welcome">رسالة ترحيب</option>
                                    <option value="follow_up">متابعة</option>
                                    <option value="promotion">عرض ترويجي</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit_template_content" class="form-label">محتوى القالب <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="edit_template_content" name="content" rows="6" required></textarea>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit_template_description" class="form-label">وصف القالب</label>
                                <input type="text" class="form-control" id="edit_template_description" name="description">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Preview Template Modal -->
<div class="modal fade" id="previewTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="whatsapp-preview-full">
                    <div class="chat-bubble-full">
                        <div class="message-content-full" id="fullTemplatePreview">
                            <!-- Template content will be loaded here -->
                        </div>
                        <div class="message-time-full">
                            <small class="text-muted">الآن</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="usePreviewedTemplate">
                    <i class="fas fa-paper-plane me-2"></i>
                    استخدام هذا القالب
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.whatsapp-bubble {
    background: #DCF8C6;
    padding: 8px 12px;
    border-radius: 12px 12px 2px 12px;
    max-width: 100%;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: relative;
}

.whatsapp-bubble::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top-color: #DCF8C6;
    border-bottom: 0;
    margin-left: -6px;
    margin-bottom: -6px;
}

.message-text {
    color: #303030;
    line-height: 1.3;
    font-size: 0.9rem;
    word-wrap: break-word;
}

.message-time {
    text-align: right;
    margin-top: 3px;
}

.template-preview {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    padding: 15px;
    border-radius: 8px;
}

.whatsapp-preview-modal {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    padding: 20px;
    border-radius: 10px;
    min-height: 150px;
}

.chat-bubble-modal {
    background: #DCF8C6;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 85%;
    margin-left: auto;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: relative;
}

.chat-bubble-modal::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: #DCF8C6;
    border-bottom: 0;
    margin-left: -8px;
    margin-bottom: -8px;
}

.message-content-modal {
    color: #303030;
    line-height: 1.4;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message-time-modal {
    text-align: right;
    margin-top: 5px;
}

.whatsapp-preview-full {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    padding: 30px;
    border-radius: 15px;
    min-height: 200px;
}

.chat-bubble-full {
    background: #DCF8C6;
    padding: 15px 20px;
    border-radius: 20px 20px 5px 20px;
    max-width: 90%;
    margin-left: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.chat-bubble-full::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -10px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-top-color: #DCF8C6;
    border-bottom: 0;
    margin-left: -10px;
    margin-bottom: -10px;
}

.message-content-full {
    color: #303030;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message-time-full {
    text-align: right;
    margin-top: 8px;
}

.template-stats {
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
    margin-top: 10px;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 2px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update preview when typing in add modal
    $('#template_content').on('input', function() {
        updateTemplatePreview();
    });
    
    // Update preview when typing in edit modal
    $('#edit_template_content').on('input', function() {
        updateEditTemplatePreview();
    });
});

function updateTemplatePreview() {
    let content = $('#template_content').val();
    
    // Replace variables with sample data
    content = content.replace(/{customer_name}/g, 'أحمد محمد');
    content = content.replace(/{company_name}/g, '{{ company_name }}');
    content = content.replace(/{today}/g, new Date().toLocaleDateString('ar-SA'));
    content = content.replace(/{time}/g, new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
    content = content.replace(/{amount}/g, '1,500');
    
    $('#templatePreview').text(content || 'اكتب محتوى القالب لرؤية المعاينة...');
}

function updateEditTemplatePreview() {
    let content = $('#edit_template_content').val();
    
    // Replace variables with sample data
    content = content.replace(/{customer_name}/g, 'أحمد محمد');
    content = content.replace(/{company_name}/g, '{{ company_name }}');
    content = content.replace(/{today}/g, new Date().toLocaleDateString('ar-SA'));
    content = content.replace(/{time}/g, new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
    content = content.replace(/{amount}/g, '1,500');
    
    $('#editTemplatePreview').text(content || 'اكتب محتوى القالب لرؤية المعاينة...');
}

function useTemplate(templateId) {
    // Redirect to send message page with template pre-selected
    window.location.href = `{{ url_for('whatsapp.send_message') }}?template_id=${templateId}`;
}

function previewTemplate(templateId) {
    // Load template data via AJAX (simplified for demo)
    fetch(`/whatsapp/templates/${templateId}`)
        .then(response => response.json())
        .then(data => {
            let content = data.content;
            
            // Replace variables with sample data
            content = content.replace(/{customer_name}/g, 'أحمد محمد');
            content = content.replace(/{company_name}/g, '{{ company_name }}');
            content = content.replace(/{today}/g, new Date().toLocaleDateString('ar-SA'));
            content = content.replace(/{time}/g, new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
            content = content.replace(/{amount}/g, '1,500');
            
            $('#fullTemplatePreview').text(content);
            $('#usePreviewedTemplate').data('template-id', templateId);
            $('#previewTemplateModal').modal('show');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ أثناء تحميل القالب', 'danger');
        });
}

function editTemplate(templateId) {
    // Load template data via AJAX (simplified for demo)
    fetch(`/whatsapp/templates/${templateId}`)
        .then(response => response.json())
        .then(data => {
            $('#edit_template_id').val(data.id);
            $('#edit_template_name').val(data.name);
            $('#edit_template_type').val(data.type);
            $('#edit_template_content').val(data.content);
            $('#edit_template_description').val(data.description);
            $('#editTemplateModal').modal('show');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ أثناء تحميل القالب', 'danger');
        });
}

function deleteTemplate(templateId) {
    if (!confirm('هل أنت متأكد من حذف هذا القالب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    fetch(`/whatsapp/templates/${templateId}/delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showAlert('حدث خطأ أثناء حذف القالب', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء حذف القالب', 'danger');
    });
}

// Use previewed template
$('#usePreviewedTemplate').on('click', function() {
    const templateId = $(this).data('template-id');
    useTemplate(templateId);
});

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}
</script>
{% endblock %}
