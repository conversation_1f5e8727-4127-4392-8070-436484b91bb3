{% extends "base.html" %}

{% block title %}جدول الأقساط - مبيعة #{{ sale.id }} - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">جدول الأقساط - مبيعة #{{ sale.id }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('sales.add_payment', sale_id=sale.id) }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                إضافة دفعة
            </a>
        </div>
        <a href="{{ url_for('sales.view', sale_id=sale.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمبيعة
        </a>
    </div>
</div>

<!-- Sale Summary -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات المبيعة</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>العميل:</strong></td>
                                <td>{{ sale.customer.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>السيارة:</strong></td>
                                <td>{{ sale.car.brand }} {{ sale.car.model }} {{ sale.car.year }}</td>
                            </tr>
                            <tr>
                                <td><strong>سعر البيع:</strong></td>
                                <td><strong class="text-primary">{{ sale.sale_price|currency }}</strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>تفاصيل التقسيط</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>الدفعة المقدمة:</strong></td>
                                <td>{{ sale.down_payment|currency if sale.down_payment else 'لا توجد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الأقساط:</strong></td>
                                <td>{{ sale.installment_count }} قسط</td>
                            </tr>
                            <tr>
                                <td><strong>قيمة القسط:</strong></td>
                                <td><strong class="text-info">{{ sale.installment_amount|currency }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">حالة المدفوعات</h6>
            </div>
            <div class="card-body text-center">
                {% set total_paid = (sale.down_payment or 0) %}
                {% for installment in installments.items %}
                    {% set total_paid = total_paid + (installment.paid_amount or 0) %}
                {% endfor %}
                {% set remaining = sale.sale_price - total_paid %}
                
                <div class="row">
                    <div class="col-6">
                        <h6>المدفوع</h6>
                        <h5 class="text-success">{{ total_paid|currency }}</h5>
                    </div>
                    <div class="col-6">
                        <h6>المتبقي</h6>
                        <h5 class="text-{{ 'success' if remaining <= 0 else 'warning' }}">
                            {{ remaining|currency }}
                        </h5>
                    </div>
                </div>
                
                <div class="progress mt-3">
                    {% set progress = (total_paid / sale.sale_price * 100) if sale.sale_price > 0 else 0 %}
                    <div class="progress-bar" role="progressbar" style="width: {{ progress }}%">
                        {{ "%.1f"|format(progress) }}%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">فلترة الأقساط</h6>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" class="form-select" id="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {{ 'selected' if status == 'pending' }}>معلق</option>
                    <option value="partial" {{ 'selected' if status == 'partial' }}>مدفوع جزئياً</option>
                    <option value="paid" {{ 'selected' if status == 'paid' }}>مدفوع</option>
                    <option value="overdue" {{ 'selected' if status == 'overdue' }}>متأخر</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        فلترة
                    </button>
                </div>
            </div>
            
            {% if status %}
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('sales.installments', sale_id=sale.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء الفلترة
                    </a>
                </div>
            </div>
            {% endif %}
        </form>
    </div>
</div>

<!-- Installments Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">جدول الأقساط</h6>
        <span class="badge bg-primary">{{ installments.total if installments else 0 }} قسط</span>
    </div>
    <div class="card-body">
        {% if installments and installments.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم القسط</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>المبلغ المطلوب</th>
                        <th>المبلغ المدفوع</th>
                        <th>المتبقي</th>
                        <th>الحالة</th>
                        <th>تاريخ الدفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for installment in installments.items %}
                    {% set remaining_amount = installment.amount - (installment.paid_amount or 0) %}
                    {% set is_overdue = installment.due_date < current_date and installment.status == 'pending' %}
                    
                    <tr class="{% if is_overdue %}table-danger{% elif installment.status == 'paid' %}table-success{% elif installment.status == 'partial' %}table-warning{% endif %}">
                        <td>
                            <strong>#{{ installment.installment_number }}</strong>
                        </td>
                        <td>
                            {{ installment.due_date|arabic_date }}
                            {% if is_overdue %}
                            <br><small class="text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                متأخر {{ (current_date - installment.due_date).days }} يوم
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ installment.amount|currency }}</strong>
                        </td>
                        <td>
                            {% if installment.paid_amount %}
                            <span class="text-success">{{ installment.paid_amount|currency }}</span>
                            {% else %}
                            <span class="text-muted">لم يدفع</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if remaining_amount > 0 %}
                            <span class="text-warning">{{ remaining_amount|currency }}</span>
                            {% else %}
                            <span class="text-success">مكتمل</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if installment.status == 'paid' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    مدفوع
                                </span>
                            {% elif installment.status == 'partial' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>
                                    جزئي
                                </span>
                            {% elif is_overdue %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    متأخر
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-hourglass-half me-1"></i>
                                    معلق
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if installment.paid_date %}
                            {{ installment.paid_date|arabic_date }}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                {% if installment.status != 'paid' %}
                                <button type="button" class="btn btn-sm btn-success" 
                                        onclick="quickPay({{ installment.id }}, {{ remaining_amount }})"
                                        title="دفع سريع">
                                    <i class="fas fa-credit-card"></i>
                                </button>
                                {% endif %}
                                
                                <a href="{{ url_for('sales.add_payment', sale_id=sale.id) }}?installment_id={{ installment.id }}" 
                                   class="btn btn-sm btn-outline-primary" title="إضافة دفعة">
                                    <i class="fas fa-plus"></i>
                                </a>
                                
                                {% if installment.status != 'pending' %}
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        onclick="viewPayments({{ installment.id }})" title="عرض المدفوعات">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if installments.pages > 1 %}
        <nav aria-label="تنقل الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if installments.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('sales.installments', sale_id=sale.id, page=installments.prev_num, status=status) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in installments.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != installments.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('sales.installments', sale_id=sale.id, page=page_num, status=status) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if installments.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('sales.installments', sale_id=sale.id, page=installments.next_num, status=status) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5>لا توجد أقساط</h5>
            {% if status %}
            <p class="text-muted">لم يتم العثور على أقساط تطابق المعايير المحددة.</p>
            <a href="{{ url_for('sales.installments', sale_id=sale.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-times me-2"></i>
                إلغاء الفلترة
            </a>
            {% else %}
            <p class="text-muted">لم يتم إنشاء جدول أقساط لهذه المبيعة.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function quickPay(installmentId, amount) {
    if (!confirm(`هل تريد دفع مبلغ ${amount.toLocaleString('ar-SA')} ريال لهذا القسط؟`)) {
        return;
    }
    
    const paymentData = {
        amount: amount,
        payment_method: 'cash',
        reference_number: ''
    };
    
    fetch(`/sales/installments/${installmentId}/pay`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(paymentData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            location.reload();
        } else {
            showAlert(data.message || 'حدث خطأ أثناء الدفع', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء الدفع', 'danger');
    });
}

function viewPayments(installmentId) {
    // This would open a modal or redirect to payments view
    // For now, just show an alert
    showAlert('ميزة عرض المدفوعات ستكون متاحة قريباً', 'info');
}

function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}
</script>
{% endblock %}
