/**
 * نظام تحويل الأرقام إلى العربية والكتابة العشوائية
 * Arabic Numbers System with Random Writing
 */

// خريطة تحويل الأرقام من الإنجليزية إلى العربية
const ARABIC_DIGITS = {
    '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
    '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩',
    '.': '٫', ',': '،'
};

// خريطة تحويل الأرقام من العربية إلى الإنجليزية
const ENGLISH_DIGITS = {
    '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
    '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
    '٫': '.', '،': ','
};

/**
 * تحويل الأرقام الإنجليزية إلى العربية
 * @param {string|number} input - النص أو الرقم المراد تحويله
 * @returns {string} النص مع الأرقام العربية
 */
function toArabicNumbers(input) {
    if (input === null || input === undefined) return '';
    
    let str = String(input);
    for (const [english, arabic] of Object.entries(ARABIC_DIGITS)) {
        str = str.replace(new RegExp(english, 'g'), arabic);
    }
    return str;
}

/**
 * تحويل الأرقام العربية إلى الإنجليزية
 * @param {string} input - النص المراد تحويله
 * @returns {string} النص مع الأرقام الإنجليزية
 */
function toEnglishNumbers(input) {
    if (input === null || input === undefined) return '';
    
    let str = String(input);
    for (const [arabic, english] of Object.entries(ENGLISH_DIGITS)) {
        str = str.replace(new RegExp(arabic, 'g'), english);
    }
    return str;
}

/**
 * تنسيق الرقم مع الفواصل والأرقام العربية
 * @param {number} number - الرقم المراد تنسيقه
 * @param {number} decimals - عدد الخانات العشرية (افتراضي: 0)
 * @returns {string} الرقم منسق بالأرقام العربية
 */
function formatArabicNumber(number, decimals = 0) {
    if (number === null || number === undefined || isNaN(number)) {
        return '٠';
    }
    
    // تنسيق الرقم بالفواصل
    const formatted = Number(number).toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
    
    // تحويل إلى الأرقام العربية
    return toArabicNumbers(formatted);
}

/**
 * تنسيق العملة بالأرقام العربية
 * @param {number} amount - المبلغ
 * @param {string} currency - العملة (افتراضي: ر.ق)
 * @returns {string} المبلغ منسق بالعملة والأرقام العربية
 */
function formatArabicCurrency(amount, currency = 'ر.ق') {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return `٠ ${currency}`;
    }
    
    const formatted = formatArabicNumber(amount);
    return `${formatted} ${currency}`;
}

/**
 * تحويل جميع الأرقام في عنصر HTML إلى العربية
 * @param {HTMLElement} element - العنصر المراد تحويل أرقامه
 */
function convertElementToArabicNumbers(element) {
    if (!element) return;
    
    // تحويل النص المباشر
    if (element.nodeType === Node.TEXT_NODE) {
        element.textContent = toArabicNumbers(element.textContent);
        return;
    }
    
    // تحويل النصوص في العقد الفرعية
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
        textNode.textContent = toArabicNumbers(textNode.textContent);
    });
}

/**
 * تحويل جميع الأرقام في الصفحة إلى العربية
 */
function convertPageToArabicNumbers() {
    // تحويل النصوص العامة
    convertElementToArabicNumbers(document.body);
    
    // تحويل قيم الحقول
    const inputs = document.querySelectorAll('input[type="number"], input[readonly], .number-display, .price-display, .currency-display');
    inputs.forEach(input => {
        if (input.value && !input.classList.contains('no-arabic-convert')) {
            input.value = toArabicNumbers(input.value);
        }
        if (input.textContent && !input.classList.contains('no-arabic-convert')) {
            input.textContent = toArabicNumbers(input.textContent);
        }
    });
    
    // تحويل الجداول
    const tables = document.querySelectorAll('table td, table th');
    tables.forEach(cell => {
        if (!cell.classList.contains('no-arabic-convert')) {
            convertElementToArabicNumbers(cell);
        }
    });
    
    // تحويل البطاقات والعناصر المخصصة
    const cards = document.querySelectorAll('.card-body, .info-value, .summary-row, .stats-card');
    cards.forEach(card => {
        if (!card.classList.contains('no-arabic-convert')) {
            convertElementToArabicNumbers(card);
        }
    });
}

/**
 * إعداد تحويل تلقائي للأرقام في حقول الإدخال
 */
function setupAutoArabicConversion() {
    // تحويل الأرقام عند الكتابة في الحقول
    document.addEventListener('input', function(e) {
        const target = e.target;
        
        // تجاهل الحقول المستثناة
        if (target.classList.contains('no-arabic-convert') || 
            target.classList.contains('english-only') ||
            target.type === 'email' ||
            target.type === 'url') {
            return;
        }
        
        // تحويل الأرقام في حقول النص
        if (target.type === 'text' || target.type === 'search') {
            const cursorPos = target.selectionStart;
            const oldValue = target.value;
            const newValue = toArabicNumbers(oldValue);
            
            if (oldValue !== newValue) {
                target.value = newValue;
                target.setSelectionRange(cursorPos, cursorPos);
            }
        }
    });
    
    // تحويل الأرقام عند فقدان التركيز
    document.addEventListener('blur', function(e) {
        const target = e.target;
        
        if (target.classList.contains('no-arabic-convert') || 
            target.classList.contains('english-only')) {
            return;
        }
        
        if (target.type === 'text' || target.type === 'search' || target.tagName === 'TEXTAREA') {
            target.value = toArabicNumbers(target.value);
        }
    }, true);
}

/**
 * نظام الكتابة العشوائية
 */
class RandomTypingEffect {
    constructor(element, text, options = {}) {
        this.element = element;
        this.text = text;
        this.options = {
            speed: 100,           // سرعة الكتابة (مللي ثانية)
            randomDelay: 50,      // تأخير عشوائي إضافي
            showCursor: true,     // إظهار المؤشر
            cursorChar: '|',      // شكل المؤشر
            loop: false,          // تكرار الكتابة
            ...options
        };
        this.currentIndex = 0;
        this.isTyping = false;
    }
    
    /**
     * بدء تأثير الكتابة العشوائية
     */
    start() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        this.currentIndex = 0;
        this.element.textContent = '';
        
        if (this.options.showCursor) {
            this.element.textContent = this.options.cursorChar;
        }
        
        this.typeNextChar();
    }
    
    /**
     * كتابة الحرف التالي
     */
    typeNextChar() {
        if (this.currentIndex >= this.text.length) {
            this.isTyping = false;
            
            if (this.options.showCursor) {
                // إزالة المؤشر بعد انتهاء الكتابة
                setTimeout(() => {
                    this.element.textContent = this.text;
                }, 500);
            }
            
            if (this.options.loop) {
                setTimeout(() => {
                    this.start();
                }, 2000);
            }
            
            return;
        }
        
        const char = this.text[this.currentIndex];
        const displayText = this.text.substring(0, this.currentIndex + 1);
        
        if (this.options.showCursor) {
            this.element.textContent = displayText + this.options.cursorChar;
        } else {
            this.element.textContent = displayText;
        }
        
        this.currentIndex++;
        
        // تأخير عشوائي
        const delay = this.options.speed + Math.random() * this.options.randomDelay;
        setTimeout(() => this.typeNextChar(), delay);
    }
    
    /**
     * إيقاف تأثير الكتابة
     */
    stop() {
        this.isTyping = false;
        this.element.textContent = this.text;
    }
}

/**
 * إنشاء تأثير كتابة عشوائية لعنصر
 * @param {string} selector - محدد العنصر
 * @param {string} text - النص المراد كتابته
 * @param {object} options - خيارات التأثير
 */
function createRandomTyping(selector, text, options = {}) {
    const element = document.querySelector(selector);
    if (!element) return null;
    
    return new RandomTypingEffect(element, text, options);
}

/**
 * تطبيق تأثير الكتابة العشوائية على عناصر متعددة
 */
function applyRandomTypingEffects() {
    // تأثير على العناوين الرئيسية
    const mainTitles = document.querySelectorAll('.random-typing');
    mainTitles.forEach((title, index) => {
        const originalText = title.textContent;
        const delay = index * 1000; // تأخير متدرج
        
        setTimeout(() => {
            new RandomTypingEffect(title, originalText, {
                speed: 80,
                randomDelay: 40,
                showCursor: true
            }).start();
        }, delay);
    });
    
    // تأثير على الأرقام المهمة
    const importantNumbers = document.querySelectorAll('.animate-number');
    importantNumbers.forEach((element, index) => {
        const targetNumber = element.textContent;
        const delay = index * 500;
        
        setTimeout(() => {
            animateNumber(element, 0, parseFloat(toEnglishNumbers(targetNumber)), 2000);
        }, delay);
    });
}

/**
 * تحريك الأرقام من 0 إلى القيمة المطلوبة
 * @param {HTMLElement} element - العنصر
 * @param {number} start - القيمة الابتدائية
 * @param {number} end - القيمة النهائية
 * @param {number} duration - مدة التحريك
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const isInteger = Number.isInteger(end);
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // استخدام easing function للحصول على تأثير أكثر سلاسة
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        
        const current = start + (end - start) * easeProgress;
        const displayValue = isInteger ? Math.round(current) : current.toFixed(2);
        
        element.textContent = toArabicNumbers(displayValue);
        
        if (progress < 1) {
            requestAnimationFrame(update);
        } else {
            element.textContent = toArabicNumbers(end);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * تحديث الأرقام في الوقت الفعلي
 */
function setupRealTimeNumberUpdates() {
    // مراقبة التغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        convertElementToArabicNumbers(node);
                    }
                });
            } else if (mutation.type === 'characterData') {
                if (!mutation.target.parentElement.classList.contains('no-arabic-convert')) {
                    mutation.target.textContent = toArabicNumbers(mutation.target.textContent);
                }
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
}

/**
 * تهيئة النظام عند تحميل الصفحة
 */
function initializeArabicNumberSystem() {
    // تحويل الأرقام الموجودة
    convertPageToArabicNumbers();
    
    // إعداد التحويل التلقائي
    setupAutoArabicConversion();
    
    // إعداد التحديث في الوقت الفعلي
    setupRealTimeNumberUpdates();
    
    // تطبيق تأثيرات الكتابة العشوائية
    applyRandomTypingEffects();
    
    console.log('✅ تم تفعيل نظام الأرقام العربية والكتابة العشوائية');
}

// تصدير الدوال للاستخدام العام
window.ArabicNumbers = {
    toArabic: toArabicNumbers,
    toEnglish: toEnglishNumbers,
    formatNumber: formatArabicNumber,
    formatCurrency: formatArabicCurrency,
    convertElement: convertElementToArabicNumbers,
    convertPage: convertPageToArabicNumbers,
    RandomTyping: RandomTypingEffect,
    createRandomTyping: createRandomTyping,
    animateNumber: animateNumber,
    initialize: initializeArabicNumberSystem
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeArabicNumberSystem);

// تحديث عند تحميل محتوى جديد عبر AJAX
document.addEventListener('ajaxComplete', function() {
    setTimeout(convertPageToArabicNumbers, 100);
});

// تحديث عند تغيير التاريخ/الوقت
setInterval(function() {
    const timeElements = document.querySelectorAll('.current-time, .live-time');
    timeElements.forEach(element => {
        if (element.textContent && !element.classList.contains('no-arabic-convert')) {
            convertElementToArabicNumbers(element);
        }
    });
}, 1000);
