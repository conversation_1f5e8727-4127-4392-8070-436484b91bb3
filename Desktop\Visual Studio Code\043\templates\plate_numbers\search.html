{% extends "base.html" %}

{% block title %}البحث المتقدم - أرقام السيارات{% endblock %}

{% block extra_css %}
<style>
.search-form {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.search-section {
    border-left: 4px solid #007bff;
    padding-left: 20px;
    margin-bottom: 25px;
}

.search-section h5 {
    color: #007bff;
    margin-bottom: 15px;
}

.quick-search-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.quick-search-btn {
    padding: 8px 16px;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    background: white;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quick-search-btn:hover {
    border-color: #007bff;
    background: #007bff;
    color: white;
    text-decoration: none;
}

.quick-search-btn.active {
    border-color: #007bff;
    background: #007bff;
    color: white;
}

.result-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.plate-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.plate-preview.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.plate-preview.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.search-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.filter-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.filter-tag {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 2px;
}

.filter-tag .remove {
    margin-left: 8px;
    cursor: pointer;
    opacity: 0.8;
}

.filter-tag .remove:hover {
    opacity: 1;
}

.advanced-options {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.price-range-slider {
    margin: 20px 0;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-search me-2"></i>البحث المتقدم</h2>
            <p class="text-muted">ابحث عن أرقام اللوحات بمعايير متقدمة ومتعددة</p>
        </div>
        <div>
            <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>جميع الأرقام
            </a>
            <a href="{{ url_for('plate_numbers.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة رقم جديد
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Search Form -->
        <div class="col-lg-4">
            <div class="search-form">
                <form method="GET" id="searchForm">
                    <!-- Basic Search -->
                    <div class="search-section">
                        <h5><i class="fas fa-search me-2"></i>البحث الأساسي</h5>
                        <div class="mb-3">
                            <label for="q" class="form-label">رقم اللوحة أو الوصف</label>
                            <input type="text" class="form-control" id="q" name="q" value="{{ query }}" 
                                   placeholder="مثال: 1234، A-123، رقم مميز">
                        </div>
                        
                        <!-- Quick Search Buttons -->
                        <div class="quick-search-buttons">
                            <a href="#" class="quick-search-btn" data-search="1">رقم 1</a>
                            <a href="#" class="quick-search-btn" data-search="7777">7777</a>
                            <a href="#" class="quick-search-btn" data-search="1234">1234</a>
                            <a href="#" class="quick-search-btn" data-search="888">888</a>
                            <a href="#" class="quick-search-btn" data-search="2000">2000</a>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="search-section">
                        <h5><i class="fas fa-tags me-2"></i>الفئة</h5>
                        <select class="form-select" name="category">
                            <option value="">جميع الفئات</option>
                            <option value="vip" {{ 'selected' if category == 'vip' }}>VIP - مميزة جداً</option>
                            <option value="special" {{ 'selected' if category == 'special' }}>مميزة</option>
                            <option value="regular" {{ 'selected' if category == 'regular' }}>عادية</option>
                            <option value="custom" {{ 'selected' if category == 'custom' }}>مخصصة</option>
                        </select>
                    </div>

                    <!-- Price Range -->
                    <div class="search-section">
                        <h5><i class="fas fa-money-bill-wave me-2"></i>نطاق السعر</h5>
                        <div class="row">
                            <div class="col-6">
                                <label for="min_price" class="form-label">من</label>
                                <input type="number" class="form-control" id="min_price" name="min_price" 
                                       value="{{ min_price }}" placeholder="0" min="0" step="1000">
                            </div>
                            <div class="col-6">
                                <label for="max_price" class="form-label">إلى</label>
                                <input type="number" class="form-control" id="max_price" name="max_price" 
                                       value="{{ max_price }}" placeholder="1000000" min="0" step="1000">
                            </div>
                        </div>
                        
                        <!-- Quick Price Buttons -->
                        <div class="quick-search-buttons">
                            <a href="#" class="quick-price-btn" data-min="0" data-max="10000">أقل من 10 آلاف</a>
                            <a href="#" class="quick-price-btn" data-min="10000" data-max="50000">10-50 ألف</a>
                            <a href="#" class="quick-price-btn" data-min="50000" data-max="100000">50-100 ألف</a>
                            <a href="#" class="quick-price-btn" data-min="100000" data-max="">أكثر من 100 ألف</a>
                        </div>
                    </div>

                    <!-- Number of Digits -->
                    <div class="search-section">
                        <h5><i class="fas fa-sort-numeric-up me-2"></i>عدد الأرقام</h5>
                        <select class="form-select" name="digits">
                            <option value="">أي عدد</option>
                            <option value="1" {{ 'selected' if digits == 1 }}>رقم واحد</option>
                            <option value="2" {{ 'selected' if digits == 2 }}>رقمان</option>
                            <option value="3" {{ 'selected' if digits == 3 }}>3 أرقام</option>
                            <option value="4" {{ 'selected' if digits == 4 }}>4 أرقام</option>
                            <option value="5" {{ 'selected' if digits == 5 }}>5 أرقام</option>
                            <option value="6" {{ 'selected' if digits == 6 }}>6 أرقام أو أكثر</option>
                        </select>
                    </div>

                    <!-- Advanced Options -->
                    <div class="advanced-options">
                        <h6><i class="fas fa-cog me-2"></i>خيارات متقدمة</h6>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="sequential" id="sequential" 
                                   {{ 'checked' if request.args.get('sequential') }}>
                            <label class="form-check-label" for="sequential">
                                أرقام متسلسلة فقط
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="repeated" id="repeated"
                                   {{ 'checked' if request.args.get('repeated') }}>
                            <label class="form-check-label" for="repeated">
                                أرقام مكررة فقط
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="mirror" id="mirror"
                                   {{ 'checked' if request.args.get('mirror') }}>
                            <label class="form-check-label" for="mirror">
                                أرقام مرآة فقط
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="lucky" id="lucky"
                                   {{ 'checked' if request.args.get('lucky') }}>
                            <label class="form-check-label" for="lucky">
                                أرقام محظوظة فقط
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="auction_only" id="auction_only"
                                   {{ 'checked' if request.args.get('auction_only') }}>
                            <label class="form-check-label" for="auction_only">
                                في المزايدة فقط
                            </label>
                        </div>
                    </div>

                    <!-- Search Buttons -->
                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="fas fa-times me-2"></i>مسح الفلاتر
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search Results -->
        <div class="col-lg-8">
            {% if request.args %}
            <!-- Search Stats -->
            <div class="search-stats">
                <h4>{{ results|length }} نتيجة</h4>
                <p class="mb-0">تم العثور على {{ results|length }} رقم لوحة مطابق لمعايير البحث</p>
            </div>

            <!-- Active Filters -->
            {% if query or category or min_price or max_price or digits %}
            <div class="filter-summary">
                <h6><i class="fas fa-filter me-2"></i>الفلاتر النشطة:</h6>
                {% if query %}
                <span class="filter-tag">
                    البحث: {{ query }}
                    <span class="remove" onclick="removeFilter('q')">&times;</span>
                </span>
                {% endif %}
                {% if category %}
                <span class="filter-tag">
                    الفئة: 
                    {% if category == 'vip' %}VIP
                    {% elif category == 'special' %}مميزة
                    {% elif category == 'regular' %}عادية
                    {% else %}مخصصة{% endif %}
                    <span class="remove" onclick="removeFilter('category')">&times;</span>
                </span>
                {% endif %}
                {% if min_price %}
                <span class="filter-tag">
                    من: {{ "{:,.0f}".format(min_price) }} ريال
                    <span class="remove" onclick="removeFilter('min_price')">&times;</span>
                </span>
                {% endif %}
                {% if max_price %}
                <span class="filter-tag">
                    إلى: {{ "{:,.0f}".format(max_price) }} ريال
                    <span class="remove" onclick="removeFilter('max_price')">&times;</span>
                </span>
                {% endif %}
                {% if digits %}
                <span class="filter-tag">
                    {{ digits }} أرقام
                    <span class="remove" onclick="removeFilter('digits')">&times;</span>
                </span>
                {% endif %}
            </div>
            {% endif %}

            <!-- Results -->
            {% if results %}
            <div class="row">
                {% for plate in results %}
                <div class="col-md-6 mb-3">
                    <div class="card result-card h-100">
                        <div class="card-body">
                            <!-- Plate Preview -->
                            <div class="plate-preview {{ plate.category }}">
                                {{ plate.number }}
                            </div>

                            <!-- Details -->
                            <div class="mb-2">
                                <span class="badge bg-{{ 'danger' if plate.category == 'vip' else 'info' if plate.category == 'special' else 'secondary' }}">
                                    {% if plate.category == 'vip' %}VIP
                                    {% elif plate.category == 'special' %}مميزة
                                    {% elif plate.category == 'regular' %}عادية
                                    {% else %}مخصصة{% endif %}
                                </span>
                                
                                <span class="badge bg-{{ 'success' if plate.status == 'available' else 'danger' if plate.status == 'sold' else 'warning' }}">
                                    {% if plate.status == 'available' %}متاحة
                                    {% elif plate.status == 'sold' %}مباعة
                                    {% elif plate.status == 'reserved' %}محجوزة
                                    {% else %}محظورة{% endif %}
                                </span>
                            </div>

                            <!-- Special Features -->
                            {% if plate.is_sequential or plate.is_repeated or plate.is_mirror or plate.is_lucky %}
                            <div class="mb-2">
                                {% if plate.is_sequential %}<span class="badge bg-success me-1">متسلسل</span>{% endif %}
                                {% if plate.is_repeated %}<span class="badge bg-warning me-1">مكرر</span>{% endif %}
                                {% if plate.is_mirror %}<span class="badge bg-info me-1">مرآة</span>{% endif %}
                                {% if plate.is_lucky %}<span class="badge bg-primary me-1">محظوظ</span>{% endif %}
                            </div>
                            {% endif %}

                            <!-- Price -->
                            <div class="mb-2">
                                {% if plate.is_auction and plate.status == 'available' %}
                                    <strong class="text-success">مزايدة: {{ "{:,.0f}".format(plate.current_bid or plate.starting_bid) }} ريال</strong>
                                {% else %}
                                    <strong class="text-primary">{{ "{:,.0f}".format(plate.price) }} ريال</strong>
                                {% endif %}
                            </div>

                            <!-- Description -->
                            {% if plate.description %}
                            <p class="text-muted small mb-2">{{ plate.description[:80] }}{% if plate.description|length > 80 %}...{% endif %}</p>
                            {% endif %}

                            <!-- Digits Count -->
                            <small class="text-muted">عدد الأرقام: {{ plate.digits_count }}</small>
                        </div>

                        <!-- Actions -->
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ url_for('plate_numbers.view', id=plate.id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                {% if plate.status == 'available' %}
                                    {% if plate.is_auction %}
                                    <button class="btn btn-outline-success btn-sm" onclick="showBidModal({{ plate.id }}, '{{ plate.number }}', {{ plate.current_bid or plate.starting_bid }})">
                                        <i class="fas fa-gavel"></i> مزايدة
                                    </button>
                                    {% else %}
                                    <a href="{{ url_for('plate_numbers.sell', id=plate.id) }}" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-handshake"></i> بيع
                                    </a>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h4>لم يتم العثور على نتائج</h4>
                <p>لم يتم العثور على أرقام لوحات تطابق معايير البحث المحددة.</p>
                <p>جرب تعديل معايير البحث أو إزالة بعض الفلاتر.</p>
                <button class="btn btn-outline-primary" onclick="clearSearch()">
                    <i class="fas fa-times me-2"></i>مسح جميع الفلاتر
                </button>
            </div>
            {% endif %}
            {% else %}
            <!-- Welcome Message -->
            <div class="text-center py-5">
                <i class="fas fa-search fa-4x text-muted mb-4"></i>
                <h3 class="text-muted">البحث المتقدم عن أرقام اللوحات</h3>
                <p class="text-muted mb-4">استخدم النموذج على اليسار للبحث عن أرقام اللوحات بمعايير متقدمة</p>
                
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-lightbulb me-2"></i>نصائح للبحث:</h5>
                            <ul class="text-start mb-0">
                                <li>ابحث برقم اللوحة المباشر مثل "1234" أو "A-123"</li>
                                <li>استخدم الفلاتر لتضييق نطاق البحث</li>
                                <li>اختر نطاق سعر محدد للعثور على أرقام في ميزانيتك</li>
                                <li>استخدم الخيارات المتقدمة للبحث عن أرقام بمميزات خاصة</li>
                                <li>جرب الأزرار السريعة للبحث عن أرقام شائعة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Quick search buttons
$('.quick-search-btn').on('click', function(e) {
    e.preventDefault();
    const searchTerm = $(this).data('search');
    $('#q').val(searchTerm);
    $('.quick-search-btn').removeClass('active');
    $(this).addClass('active');
});

// Quick price buttons
$('.quick-price-btn').on('click', function(e) {
    e.preventDefault();
    const minPrice = $(this).data('min');
    const maxPrice = $(this).data('max');
    
    $('#min_price').val(minPrice);
    $('#max_price').val(maxPrice || '');
    
    $('.quick-price-btn').removeClass('active');
    $(this).addClass('active');
});

// Remove filter function
function removeFilter(filterName) {
    const url = new URL(window.location);
    url.searchParams.delete(filterName);
    window.location.href = url.toString();
}

// Clear all filters
function clearSearch() {
    window.location.href = '{{ url_for("plate_numbers.search") }}';
}

// Auto-submit form on filter change
$('select[name="category"], select[name="digits"]').on('change', function() {
    $('#searchForm').submit();
});

// Auto-submit on checkbox change
$('input[type="checkbox"]').on('change', function() {
    $('#searchForm').submit();
});

// Bid modal (reuse from main page)
function showBidModal(plateId, plateNumber, currentBid) {
    // Implementation would be similar to the main page
    alert(`مزايدة على رقم ${plateNumber} - المزايدة الحالية: ${currentBid.toLocaleString()} ريال`);
}

// Highlight search terms in results
function highlightSearchTerms() {
    const searchTerm = $('#q').val();
    if (searchTerm) {
        $('.result-card').each(function() {
            const content = $(this).html();
            const highlighted = content.replace(
                new RegExp(searchTerm, 'gi'),
                `<mark>${searchTerm}</mark>`
            );
            $(this).html(highlighted);
        });
    }
}

// Apply highlighting on page load
$(document).ready(function() {
    highlightSearchTerms();
});

// Save search preferences
function saveSearchPreferences() {
    const preferences = {
        category: $('select[name="category"]').val(),
        min_price: $('#min_price').val(),
        max_price: $('#max_price').val(),
        digits: $('select[name="digits"]').val()
    };
    
    localStorage.setItem('plateSearchPreferences', JSON.stringify(preferences));
}

// Load search preferences
function loadSearchPreferences() {
    const saved = localStorage.getItem('plateSearchPreferences');
    if (saved) {
        const preferences = JSON.parse(saved);
        
        if (preferences.category) $('select[name="category"]').val(preferences.category);
        if (preferences.min_price) $('#min_price').val(preferences.min_price);
        if (preferences.max_price) $('#max_price').val(preferences.max_price);
        if (preferences.digits) $('select[name="digits"]').val(preferences.digits);
    }
}

// Save preferences on form submit
$('#searchForm').on('submit', function() {
    saveSearchPreferences();
});

// Load preferences on page load
$(document).ready(function() {
    if (!{{ 'true' if request.args else 'false' }}) {
        loadSearchPreferences();
    }
});
</script>
{% endblock %}
