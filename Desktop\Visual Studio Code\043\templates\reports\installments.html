{% extends "base.html" %}

{% block title %}تقرير الأقساط - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تقرير الأقساط</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>
                تصدير Excel
            </button>
        </div>
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للتقارير
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">فلترة البيانات</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">حالة القسط</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {{ 'selected' if status == 'pending' }}>مستحق</option>
                    <option value="paid" {{ 'selected' if status == 'paid' }}>مدفوع</option>
                    <option value="overdue" {{ 'selected' if status == 'overdue' }}>متأخر</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="month" class="form-label">الشهر</label>
                <select class="form-select" id="month" name="month">
                    <option value="">جميع الأشهر</option>
                    {% for i in range(1, 13) %}
                    <option value="{{ i }}" {{ 'selected' if month == i|string }}>
                        {{ ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'][i-1] }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="year" class="form-label">السنة</label>
                <select class="form-select" id="year" name="year">
                    <option value="">جميع السنوات</option>
                    {% for y in range(current_year - 2, current_year + 3) %}
                    <option value="{{ y }}" {{ 'selected' if year == y|string }}>{{ y }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="customer" class="form-label">العميل</label>
                <input type="text" class="form-control" id="customer" name="customer" 
                       value="{{ customer }}" placeholder="اسم العميل...">
            </div>
            
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>
                    تطبيق الفلتر
                </button>
                <a href="{{ url_for('reports.installments_report') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء الفلتر
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">إجمالي الأقساط</h5>
                <h2 class="text-primary">{{ total_installments }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">المدفوعة</h5>
                <h2 class="text-success">{{ paid_installments }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">المستحقة</h5>
                <h2 class="text-warning">{{ pending_installments }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">المتأخرة</h5>
                <h2 class="text-danger">{{ overdue_installments }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- Installments Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تفاصيل الأقساط</h5>
    </div>
    <div class="card-body">
        {% if installments %}
        <div class="table-responsive">
            <table class="table table-hover" id="installmentsTable">
                <thead>
                    <tr>
                        <th>العميل</th>
                        <th>السيارة</th>
                        <th>رقم المبيعة</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>مبلغ القسط</th>
                        <th>المبلغ المدفوع</th>
                        <th>المتبقي</th>
                        <th>الحالة</th>
                        <th>الأيام المتأخرة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for installment in installments %}
                    <tr class="{% if installment.status == 'overdue' %}table-danger{% elif installment.status == 'paid' %}table-success{% endif %}">
                        <td>
                            <div class="d-flex align-items-center">
                                {% if installment.sale.customer.photo_path %}
                                <img src="{{ url_for('static', filename=installment.sale.customer.photo_path) }}" 
                                     class="rounded-circle me-2" width="30" height="30" 
                                     style="object-fit: cover;" alt="صورة العميل">
                                {% else %}
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" 
                                     style="width: 30px; height: 30px; font-size: 0.7rem;">
                                    {{ installment.sale.customer.full_name[:2].upper() }}
                                </div>
                                {% endif %}
                                <div>
                                    <a href="{{ url_for('customers.view', customer_id=installment.sale.customer.id) }}" 
                                       class="text-decoration-none">
                                        {{ installment.sale.customer.full_name }}
                                    </a>
                                    <br><small class="text-muted">{{ installment.sale.customer.phone }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="{{ url_for('cars.view', car_id=installment.sale.car.id) }}" 
                               class="text-decoration-none">
                                {{ installment.sale.car.brand }} {{ installment.sale.car.model }}
                                <br><small class="text-muted">{{ installment.sale.car.year }}</small>
                            </a>
                        </td>
                        <td>
                            <a href="{{ url_for('sales.view', sale_id=installment.sale.id) }}" 
                               class="text-decoration-none">
                                #{{ installment.sale.id }}
                            </a>
                        </td>
                        <td>
                            {{ installment.due_date.strftime('%Y/%m/%d') }}
                            <br><small class="text-muted">{{ installment.due_date.strftime('%A') }}</small>
                        </td>
                        <td>{{ installment.amount|currency }}</td>
                        <td>{{ installment.paid_amount|currency if installment.paid_amount else '-' }}</td>
                        <td>
                            {% set remaining = installment.amount - (installment.paid_amount or 0) %}
                            <span class="text-{{ 'success' if remaining <= 0 else 'warning' }}">
                                {{ remaining|currency }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{% if installment.status == 'paid' %}success{% elif installment.status == 'overdue' %}danger{% else %}warning{% endif %}">
                                {% if installment.status == 'paid' %}مدفوع
                                {% elif installment.status == 'overdue' %}متأخر
                                {% else %}مستحق{% endif %}
                            </span>
                        </td>
                        <td>
                            {% if installment.status == 'overdue' %}
                                {% set days_overdue = (current_date - installment.due_date).days %}
                                <span class="text-danger">{{ days_overdue }} يوم</span>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                {% if installment.status != 'paid' %}
                                <a href="{{ url_for('sales.pay_installment', installment_id=installment.id) }}" 
                                   class="btn btn-sm btn-success" title="دفع القسط">
                                    <i class="fas fa-money-bill-wave"></i>
                                </a>
                                {% endif %}
                                
                                <a href="{{ url_for('sales.view', sale_id=installment.sale.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="عرض المبيعة">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                {% if installment.sale.customer.whatsapp_number or installment.sale.customer.phone %}
                                <a href="{{ url_for('whatsapp.send_reminder', installment_id=installment.id) }}" 
                                   class="btn btn-sm btn-outline-success" title="إرسال تذكير">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th colspan="4">الإجمالي</th>
                        <th>{{ installments|sum(attribute='amount')|currency }}</th>
                        <th>{{ installments|sum(attribute='paid_amount')|currency }}</th>
                        <th>{{ (installments|sum(attribute='amount') - installments|sum(attribute='paid_amount'))|currency }}</th>
                        <th colspan="3"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5>لا توجد أقساط</h5>
            {% if status or month or year or customer %}
            <p class="text-muted">لم يتم العثور على أقساط تطابق المعايير المحددة.</p>
            <a href="{{ url_for('reports.installments_report') }}" class="btn btn-outline-primary">
                <i class="fas fa-times me-2"></i>
                إلغاء الفلترة
            </a>
            {% else %}
            <p class="text-muted">لا توجد مبيعات بالتقسيط بعد.</p>
            <a href="{{ url_for('sales.add') }}" class="btn btn-primary">
                <i class="fas fa-handshake me-2"></i>
                إضافة مبيعة جديدة
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Charts -->
{% if installments %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">توزيع الأقساط حسب الحالة</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الأقساط المستحقة خلال الشهر</h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#installmentsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 3, "asc" ]], // Sort by due date
        "pageLength": 25,
        "responsive": true
    });
    
    // Status Chart
    {% if installments %}
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['مدفوع', 'مستحق', 'متأخر'],
            datasets: [{
                data: [{{ paid_installments }}, {{ pending_installments }}, {{ overdue_installments }}],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Monthly Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    // This would need monthly data from the backend
    new Chart(monthlyCtx, {
        type: 'bar',
        data: {
            labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
            datasets: [{
                label: 'الأقساط المستحقة',
                data: [12, 19, 3, 5], // This should come from backend
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}
});

function exportToExcel() {
    // Simple Excel export
    const table = document.getElementById('installmentsTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "الأقساط"});
    XLSX.writeFile(wb, 'تقرير_الأقساط.xlsx');
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn-toolbar,
    .card-header .btn,
    .btn-group,
    .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}

.printing .sidebar,
.printing .navbar,
.printing .btn-toolbar {
    display: none !important;
}
</style>
{% endblock %}
