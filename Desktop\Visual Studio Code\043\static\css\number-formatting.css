/* Number Formatting Styles for Qatar Car Dealership System */

/* Number Display Classes */
.number {
    font-family: 'Cairo', 'Noto Sans Arabic', monospace;
    font-weight: 500;
    letter-spacing: 0.5px;
    direction: ltr;
    text-align: right;
}

.currency {
    font-family: 'Cairo', 'Noto Sans Arabic', monospace;
    font-weight: 600;
    color: #2d5a27;
    letter-spacing: 0.5px;
    direction: rtl;
    text-align: right;
}

.percentage {
    font-family: 'Cairo', 'Noto Sans Arabic', monospace;
    font-weight: 500;
    color: #1e40af;
    letter-spacing: 0.5px;
}

/* Arabic Digits Styling */
.arabic-digits {
    font-family: 'Noto Naskh Arabic', 'Amiri', serif;
    font-size: 1.1em;
    direction: rtl;
}

/* Size Variants */
.number-sm {
    font-size: 0.875rem;
}

.number-lg {
    font-size: 1.25rem;
}

.number-xl {
    font-size: 1.5rem;
    font-weight: 700;
}

/* Currency Variants */
.currency-primary {
    color: #059669;
    background-color: #ecfdf5;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid #a7f3d0;
}

.currency-secondary {
    color: #7c3aed;
    background-color: #f3e8ff;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid #c4b5fd;
}

.currency-warning {
    color: #d97706;
    background-color: #fffbeb;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid #fed7aa;
}

.currency-danger {
    color: #dc2626;
    background-color: #fef2f2;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid #fecaca;
}

/* Input Formatting */
input[data-format="currency"],
input[data-format="number"] {
    font-family: 'Cairo', 'Noto Sans Arabic', monospace;
    text-align: right;
    direction: ltr;
    font-weight: 500;
}

input[data-format="currency"]:focus,
input[data-format="number"]:focus {
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

input[data-format="phone"],
input[data-format="qatar-id"] {
    font-family: 'Cairo', monospace;
    text-align: left;
    direction: ltr;
    font-weight: 500;
    letter-spacing: 1px;
}

/* Table Number Formatting */
.table .number,
.table .currency {
    white-space: nowrap;
    text-align: right;
}

.table .currency {
    font-weight: 600;
}

/* Card Number Display */
.card .number-display {
    background-color: #f8fafc;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    margin: 0.5rem 0;
}

.card .currency-display {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

/* Loading States */
.number-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 0.25rem;
    height: 1.2em;
    width: 100px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Error States */
.number-error {
    color: #dc2626;
    background-color: #fef2f2;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #fecaca;
}

.number-error::before {
    content: "⚠️ ";
}

/* Responsive Design */
@media (max-width: 768px) {
    .number,
    .currency {
        font-size: 0.9rem;
    }
    
    .number-xl {
        font-size: 1.25rem;
    }
    
    .currency-display {
        font-size: 1.1rem;
        padding: 0.75rem;
    }
    
    .table .number,
    .table .currency {
        font-size: 0.85rem;
    }
}

/* Print Styles */
@media print {
    .number,
    .currency,
    .percentage {
        color: #000 !important;
        background: transparent !important;
        border: none !important;
        font-weight: 600;
    }
    
    .currency-display {
        background: transparent !important;
        color: #000 !important;
        border: 2px solid #000 !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .number,
    .currency {
        font-weight: 700;
        border: 1px solid currentColor;
    }
    
    .currency-primary {
        background-color: #000;
        color: #fff;
    }
    
    .currency-display {
        border: 3px solid #000;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .number-display {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .currency-display {
        background: linear-gradient(135deg, #065f46 0%, #064e3b 100%);
    }
    
    .currency-primary {
        background-color: #064e3b;
        border-color: #059669;
    }
    
    .currency-secondary {
        background-color: #581c87;
        border-color: #7c3aed;
    }
    
    .currency-warning {
        background-color: #92400e;
        border-color: #d97706;
    }
    
    .currency-danger {
        background-color: #991b1b;
        border-color: #dc2626;
    }
}

/* Animation Classes */
.number-highlight {
    animation: highlight 0.5s ease-in-out;
}

@keyframes highlight {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: #fef3c7;
    }
    100% {
        background-color: transparent;
    }
}

.currency-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Utility Classes */
.text-monospace {
    font-family: 'Courier New', monospace;
}

.text-tabular {
    font-variant-numeric: tabular-nums;
}

.text-ltr {
    direction: ltr;
    text-align: left;
}

.text-rtl {
    direction: rtl;
    text-align: right;
}

/* Form Validation Styles */
.form-control.is-invalid[data-format] {
    border-color: #dc2626;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc2626'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6.4.4.4-.4M6 7v.01'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid[data-format] {
    border-color: #059669;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5 6.47 3.56 4.26 5.78l-.94-.94-.94.94z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Tooltip for Number Formatting */
.number-tooltip {
    position: relative;
    cursor: help;
}

.number-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 1000;
}

.number-tooltip:hover::after {
    opacity: 1;
}
