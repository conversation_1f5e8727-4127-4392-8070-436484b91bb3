from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='sales')  # manager, sales, accountant
    full_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        permissions = {
            'manager': ['view_all', 'edit_all', 'delete_all', 'reports', 'settings',
                       'view_cars', 'edit_cars', 'view_customers', 'edit_customers',
                       'create_sales', 'payments', 'installments'],
            'sales': ['view_cars', 'edit_cars', 'view_customers', 'edit_customers', 'create_sales'],
            'accountant': ['view_all', 'payments', 'reports', 'installments', 'view_cars', 'view_customers']
        }

        # Manager has all permissions
        if self.role == 'manager':
            return True

        return permission in permissions.get(self.role, [])

class Car(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    brand = db.Column(db.String(50), nullable=False)
    model = db.Column(db.String(50), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    chassis_number = db.Column(db.String(50), unique=True, nullable=False)
    plate_number = db.Column(db.String(20), unique=True)
    color = db.Column(db.String(30))
    engine_size = db.Column(db.String(20))
    fuel_type = db.Column(db.String(20))
    transmission = db.Column(db.String(20))
    mileage = db.Column(db.Integer)
    price = db.Column(db.Float, nullable=False)
    cost_price = db.Column(db.Float)
    status = db.Column(db.String(20), default='available')  # available, sold, reserved, maintenance
    description = db.Column(db.Text)
    images = db.Column(db.Text)  # JSON string of image paths

    # Car condition fields
    car_type = db.Column(db.String(20), default='used')  # new, used
    condition_rating = db.Column(db.String(20))  # excellent, very_good, good, fair, poor
    condition_notes = db.Column(db.Text)  # Additional notes about condition

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    sales = db.relationship('Sale', backref='car', lazy=True)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(20), unique=True, nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    date_of_birth = db.Column(db.Date)
    occupation = db.Column(db.String(100))
    monthly_income = db.Column(db.Float)
    id_copy_path = db.Column(db.String(200))
    photo_path = db.Column(db.String(200))
    
    # Guarantor information
    guarantor_name = db.Column(db.String(100))
    guarantor_id = db.Column(db.String(20))
    guarantor_phone = db.Column(db.String(20))
    guarantor_address = db.Column(db.Text)
    guarantor_id_copy_path = db.Column(db.String(200))
    
    whatsapp_number = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    sales = db.relationship('Sale', backref='customer', lazy=True)

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    car_id = db.Column(db.Integer, db.ForeignKey('car.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    sale_type = db.Column(db.String(20), nullable=False)  # cash, installment
    sale_price = db.Column(db.Float, nullable=False)
    down_payment = db.Column(db.Float, default=0)
    installment_amount = db.Column(db.Float, default=0)
    installment_count = db.Column(db.Integer, default=0)
    installment_frequency = db.Column(db.String(20), default='monthly')  # monthly, weekly
    first_installment_date = db.Column(db.Date)
    contract_path = db.Column(db.String(200))
    status = db.Column(db.String(20), default='active')  # active, completed, cancelled
    notes = db.Column(db.Text)
    sale_date = db.Column(db.Date, default=date.today)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    installments = db.relationship('Installment', backref='sale', lazy=True, cascade='all, delete-orphan')
    payments = db.relationship('Payment', backref='sale', lazy=True)

class Installment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sale.id'), nullable=False)
    installment_number = db.Column(db.Integer, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue
    paid_amount = db.Column(db.Float, default=0)
    paid_date = db.Column(db.Date)
    late_fee = db.Column(db.Float, default=0)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    payments = db.relationship('Payment', backref='installment', lazy=True)

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sale.id'), nullable=False)
    installment_id = db.Column(db.Integer, db.ForeignKey('installment.id'))
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, bank_transfer, check
    payment_date = db.Column(db.Date, default=date.today)
    reference_number = db.Column(db.String(50))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), default='info')  # info, warning, error, success
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
class WhatsAppMessage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    phone_number = db.Column(db.String(20), nullable=False)
    message = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(50))  # reminder, contract, delay_notice
    status = db.Column(db.String(20), default='pending')  # pending, sent, failed
    sent_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

class PlateNumber(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.String(20), unique=True, nullable=False)
    category = db.Column(db.String(30), nullable=False)  # special, vip, regular, custom
    type = db.Column(db.String(20), nullable=False)  # private, taxi, transport, government
    digits_count = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    cost_price = db.Column(db.Float)
    status = db.Column(db.String(20), default='available')  # available, sold, reserved, blocked
    description = db.Column(db.Text)
    special_features = db.Column(db.Text)  # JSON string for special features
    images = db.Column(db.Text)  # JSON string of image paths

    # Qatar specific fields
    code_letter = db.Column(db.String(5))  # A, B, C, etc.
    series = db.Column(db.String(10))  # Series number
    is_sequential = db.Column(db.Boolean, default=False)  # 1234, 2345, etc.
    is_repeated = db.Column(db.Boolean, default=False)  # 1111, 2222, etc.
    is_mirror = db.Column(db.Boolean, default=False)  # 1221, 3443, etc.
    is_birthday = db.Column(db.Boolean, default=False)  # Birth dates
    is_lucky = db.Column(db.Boolean, default=False)  # Lucky numbers

    # Auction and bidding
    is_auction = db.Column(db.Boolean, default=False)
    auction_start_date = db.Column(db.DateTime)
    auction_end_date = db.Column(db.DateTime)
    starting_bid = db.Column(db.Float)
    current_bid = db.Column(db.Float)
    reserve_price = db.Column(db.Float)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    sales = db.relationship('PlateNumberSale', backref='plate_number', lazy=True)
    bids = db.relationship('PlateNumberBid', backref='plate_number', lazy=True)

class PlateNumberSale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    plate_number_id = db.Column(db.Integer, db.ForeignKey('plate_number.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    sale_price = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, bank_transfer, installment
    down_payment = db.Column(db.Float, default=0)
    installment_amount = db.Column(db.Float, default=0)
    installment_count = db.Column(db.Integer, default=0)
    contract_path = db.Column(db.String(200))
    status = db.Column(db.String(20), default='active')  # active, completed, cancelled
    transfer_status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    transfer_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    sale_date = db.Column(db.Date, default=date.today)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    payments = db.relationship('PlateNumberPayment', backref='sale', lazy=True)

class PlateNumberBid(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    plate_number_id = db.Column(db.Integer, db.ForeignKey('plate_number.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    bid_amount = db.Column(db.Float, nullable=False)
    is_winning = db.Column(db.Boolean, default=False)
    status = db.Column(db.String(20), default='active')  # active, outbid, winning, cancelled
    notes = db.Column(db.Text)
    bid_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

class PlateNumberPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('plate_number_sale.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, bank_transfer, check
    payment_date = db.Column(db.Date, default=date.today)
    reference_number = db.Column(db.String(50))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
