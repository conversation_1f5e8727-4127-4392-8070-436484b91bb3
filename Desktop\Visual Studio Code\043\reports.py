from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, jsonify
from flask_login import login_required, current_user
from models import db, Sale, Car, Customer, Installment, Payment
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, or_
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
import tempfile
import os

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
def index():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    return render_template('reports/index.html')

@reports_bp.route('/sales')
@login_required
def sales_report():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get date range from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    sale_type = request.args.get('sale_type', '')
    
    # Default to current month if no dates provided
    if not start_date or not end_date:
        today = date.today()
        start_date = today.replace(day=1)
        if today.month == 12:
            end_date = date(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(today.year, today.month + 1, 1) - timedelta(days=1)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Build query
    query = Sale.query.join(Customer).join(Car).filter(
        and_(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date
        )
    )
    
    if sale_type:
        query = query.filter(Sale.sale_type == sale_type)
    
    sales = query.order_by(Sale.sale_date.desc()).all()
    
    # Calculate summary
    total_sales = len(sales)
    total_revenue = sum(sale.sale_price for sale in sales)
    cash_sales = [sale for sale in sales if sale.sale_type == 'cash']
    installment_sales = [sale for sale in sales if sale.sale_type == 'installment']
    
    cash_revenue = sum(sale.sale_price for sale in cash_sales)
    installment_revenue = sum(sale.sale_price for sale in installment_sales)
    down_payments = sum(sale.down_payment for sale in installment_sales)
    
    summary = {
        'total_sales': total_sales,
        'total_revenue': total_revenue,
        'cash_sales_count': len(cash_sales),
        'cash_revenue': cash_revenue,
        'installment_sales_count': len(installment_sales),
        'installment_revenue': installment_revenue,
        'down_payments': down_payments
    }
    
    return render_template('reports/sales.html', 
                         sales=sales, 
                         summary=summary,
                         start_date=start_date,
                         end_date=end_date,
                         sale_type=sale_type)

@reports_bp.route('/installments')
@login_required
def installments_report():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get filters
    status = request.args.get('status', '')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Default to current month if no dates provided
    if not start_date or not end_date:
        today = date.today()
        start_date = today.replace(day=1)
        if today.month == 12:
            end_date = date(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(today.year, today.month + 1, 1) - timedelta(days=1)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Build query
    query = Installment.query.join(Sale).join(Customer).filter(
        and_(
            Installment.due_date >= start_date,
            Installment.due_date <= end_date
        )
    )
    
    if status:
        query = query.filter(Installment.status == status)
    
    installments = query.order_by(Installment.due_date.asc()).all()
    
    # Calculate summary
    total_installments = len(installments)
    total_amount = sum(inst.amount for inst in installments)
    paid_installments = [inst for inst in installments if inst.status == 'paid']
    pending_installments = [inst for inst in installments if inst.status == 'pending']
    overdue_installments = [inst for inst in installments if inst.status == 'pending' and inst.due_date < date.today()]
    
    paid_amount = sum(inst.paid_amount for inst in paid_installments)
    pending_amount = sum(inst.amount for inst in pending_installments)
    overdue_amount = sum(inst.amount for inst in overdue_installments)
    
    summary = {
        'total_installments': total_installments,
        'total_amount': total_amount,
        'paid_count': len(paid_installments),
        'paid_amount': paid_amount,
        'pending_count': len(pending_installments),
        'pending_amount': pending_amount,
        'overdue_count': len(overdue_installments),
        'overdue_amount': overdue_amount
    }
    
    return render_template('reports/installments.html', 
                         installments=installments, 
                         summary=summary,
                         start_date=start_date,
                         end_date=end_date,
                         status=status)

@reports_bp.route('/cars')
@login_required
def cars_report():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get filters
    status = request.args.get('status', '')
    brand = request.args.get('brand', '')
    
    # Build query
    query = Car.query
    
    if status:
        query = query.filter(Car.status == status)
    
    if brand:
        query = query.filter(Car.brand == brand)
    
    cars = query.order_by(Car.created_at.desc()).all()
    
    # Get unique brands
    brands = db.session.query(Car.brand).distinct().all()
    brands = [brand[0] for brand in brands if brand[0]]
    
    # Calculate summary
    total_cars = len(cars)
    available_cars = len([car for car in cars if car.status == 'available'])
    sold_cars = len([car for car in cars if car.status == 'sold'])
    reserved_cars = len([car for car in cars if car.status == 'reserved'])
    
    total_value = sum(car.price for car in cars)
    available_value = sum(car.price for car in cars if car.status == 'available')
    
    summary = {
        'total_cars': total_cars,
        'available_cars': available_cars,
        'sold_cars': sold_cars,
        'reserved_cars': reserved_cars,
        'total_value': total_value,
        'available_value': available_value
    }
    
    return render_template('reports/cars.html', 
                         cars=cars, 
                         summary=summary,
                         brands=brands,
                         status=status,
                         brand=brand)

@reports_bp.route('/financial')
@login_required
def financial_report():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get date range
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Default to current month if no dates provided
    if not start_date or not end_date:
        today = date.today()
        start_date = today.replace(day=1)
        if today.month == 12:
            end_date = date(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(today.year, today.month + 1, 1) - timedelta(days=1)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Sales revenue
    sales_revenue = db.session.query(func.sum(Sale.sale_price)).filter(
        and_(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date,
            Sale.sale_type == 'cash'
        )
    ).scalar() or 0
    
    # Down payments
    down_payments = db.session.query(func.sum(Sale.down_payment)).filter(
        and_(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date,
            Sale.sale_type == 'installment'
        )
    ).scalar() or 0
    
    # Installment payments
    installment_payments = db.session.query(func.sum(Payment.amount)).filter(
        and_(
            Payment.payment_date >= start_date,
            Payment.payment_date <= end_date
        )
    ).scalar() or 0
    
    # Total income
    total_income = sales_revenue + down_payments + installment_payments
    
    # Get payment details
    payments = Payment.query.filter(
        and_(
            Payment.payment_date >= start_date,
            Payment.payment_date <= end_date
        )
    ).order_by(Payment.payment_date.desc()).all()
    
    # Group payments by method
    payment_methods = {}
    for payment in payments:
        method = payment.payment_method
        if method not in payment_methods:
            payment_methods[method] = {'count': 0, 'amount': 0}
        payment_methods[method]['count'] += 1
        payment_methods[method]['amount'] += payment.amount
    
    summary = {
        'sales_revenue': sales_revenue,
        'down_payments': down_payments,
        'installment_payments': installment_payments,
        'total_income': total_income,
        'payment_methods': payment_methods
    }
    
    return render_template('reports/financial.html', 
                         summary=summary,
                         payments=payments,
                         start_date=start_date,
                         end_date=end_date)

@reports_bp.route('/export/sales')
@login_required
def export_sales():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    sale_type = request.args.get('sale_type', '')
    
    if not start_date or not end_date:
        flash('تواريخ البداية والنهاية مطلوبة', 'error')
        return redirect(url_for('reports.sales_report'))
    
    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Get sales data
    query = Sale.query.join(Customer).join(Car).filter(
        and_(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date
        )
    )
    
    if sale_type:
        query = query.filter(Sale.sale_type == sale_type)
    
    sales = query.order_by(Sale.sale_date.desc()).all()
    
    # Create Excel file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    temp_path = temp_file.name
    temp_file.close()
    
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير المبيعات"
        
        # Headers
        headers = [
            'رقم العملية', 'تاريخ البيع', 'اسم العميل', 'رقم الهوية', 
            'السيارة', 'رقم الشاسيه', 'نوع البيع', 'سعر البيع', 
            'الدفعة المقدمة', 'قيمة القسط', 'عدد الأقساط', 'الحالة'
        ]
        
        # Style headers
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # Data rows
        for row, sale in enumerate(sales, 2):
            ws.cell(row=row, column=1, value=sale.id)
            ws.cell(row=row, column=2, value=sale.sale_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=3, value=sale.customer.full_name)
            ws.cell(row=row, column=4, value=sale.customer.national_id)
            ws.cell(row=row, column=5, value=f"{sale.car.brand} {sale.car.model} {sale.car.year}")
            ws.cell(row=row, column=6, value=sale.car.chassis_number)
            ws.cell(row=row, column=7, value="نقدي" if sale.sale_type == 'cash' else "تقسيط")
            ws.cell(row=row, column=8, value=sale.sale_price)
            ws.cell(row=row, column=9, value=sale.down_payment or 0)
            ws.cell(row=row, column=10, value=sale.installment_amount or 0)
            ws.cell(row=row, column=11, value=sale.installment_count or 0)
            ws.cell(row=row, column=12, value=sale.status)
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        wb.save(temp_path)
        
        filename = f"sales_report_{start_date}_{end_date}.xlsx"
        return send_file(temp_path, 
                        as_attachment=True, 
                        download_name=filename,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('reports.sales_report'))
    
    finally:
        try:
            os.unlink(temp_path)
        except:
            pass

@reports_bp.route('/export/installments')
@login_required
def export_installments():
    if not current_user.has_permission('reports'):
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    status = request.args.get('status', '')
    
    if not start_date or not end_date:
        flash('تواريخ البداية والنهاية مطلوبة', 'error')
        return redirect(url_for('reports.installments_report'))
    
    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Get installments data
    query = Installment.query.join(Sale).join(Customer).filter(
        and_(
            Installment.due_date >= start_date,
            Installment.due_date <= end_date
        )
    )
    
    if status:
        query = query.filter(Installment.status == status)
    
    installments = query.order_by(Installment.due_date.asc()).all()
    
    # Create Excel file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    temp_path = temp_file.name
    temp_file.close()
    
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير الأقساط"
        
        # Headers
        headers = [
            'رقم القسط', 'رقم العملية', 'اسم العميل', 'رقم الهوية',
            'السيارة', 'رقم القسط', 'تاريخ الاستحقاق', 'المبلغ المطلوب',
            'المبلغ المدفوع', 'تاريخ الدفع', 'الحالة', 'ملاحظات'
        ]
        
        # Style headers
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # Data rows
        for row, installment in enumerate(installments, 2):
            ws.cell(row=row, column=1, value=installment.id)
            ws.cell(row=row, column=2, value=installment.sale_id)
            ws.cell(row=row, column=3, value=installment.sale.customer.full_name)
            ws.cell(row=row, column=4, value=installment.sale.customer.national_id)
            ws.cell(row=row, column=5, value=f"{installment.sale.car.brand} {installment.sale.car.model}")
            ws.cell(row=row, column=6, value=installment.installment_number)
            ws.cell(row=row, column=7, value=installment.due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=8, value=installment.amount)
            ws.cell(row=row, column=9, value=installment.paid_amount)
            ws.cell(row=row, column=10, value=installment.paid_date.strftime('%Y-%m-%d') if installment.paid_date else '')
            ws.cell(row=row, column=11, value=installment.status)
            ws.cell(row=row, column=12, value=installment.notes or '')
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        wb.save(temp_path)
        
        filename = f"installments_report_{start_date}_{end_date}.xlsx"
        return send_file(temp_path, 
                        as_attachment=True, 
                        download_name=filename,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('reports.installments_report'))
    
    finally:
        try:
            os.unlink(temp_path)
        except:
            pass
