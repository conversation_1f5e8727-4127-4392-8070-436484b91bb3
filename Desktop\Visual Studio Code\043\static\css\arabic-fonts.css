/* Arabic Fonts Configuration for Car Dealership System */

/* Font Face Declarations for Local Fonts */
@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo[slnt,wght].ttf') format('truetype-variations'),
         url('../fonts/Cairo-Regular.ttf') format('truetype');
    font-weight: 200 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Arabic';
    src: url('../fonts/NotoSansArabic[wdth,wght].ttf') format('truetype-variations');
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Naskh Arabic';
    src: url('../fonts/NotoNaskhArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Amiri';
    src: url('../fonts/Amiri-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Font Variables for Easy Customization */
:root {
    --font-primary: 'Cairo', 'Noto Sans Arabic', sans-serif;
    --font-secondary: 'Noto Naskh Arabic', 'Amiri', serif;
    --font-fallback: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    
    /* Font sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Line heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    --line-height-loose: 2;
}

/* Base Typography */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-feature-settings: "liga" 1, "kern" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-variant-ligatures: common-ligatures;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: var(--line-height-tight);
    margin-bottom: 0.5em;
    color: #2d3748;
}

h1 { font-size: var(--font-size-3xl); font-weight: 700; }
h2 { font-size: var(--font-size-2xl); font-weight: 600; }
h3 { font-size: var(--font-size-xl); font-weight: 600; }
h4 { font-size: var(--font-size-lg); font-weight: 600; }
h5 { font-size: var(--font-size-base); font-weight: 600; }
h6 { font-size: var(--font-size-sm); font-weight: 600; }

/* Paragraph and Text */
p {
    margin-bottom: 1em;
    line-height: var(--line-height-relaxed);
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: 400;
    line-height: var(--line-height-relaxed);
}

/* Arabic Text Styles */
.text-arabic {
    font-family: var(--font-secondary);
    line-height: var(--line-height-loose);
    word-spacing: 0.1em;
    text-align: justify;
}

.text-arabic-modern {
    font-family: var(--font-primary);
    line-height: var(--line-height-relaxed);
}

.text-arabic-title {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: var(--line-height-tight);
}

/* Form Elements */
.form-control, .form-select, .form-check-label {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

.form-text {
    font-size: var(--font-size-sm);
    color: #6b7280;
    line-height: var(--line-height-normal);
}

/* Buttons */
.btn {
    font-family: var(--font-primary);
    font-weight: 500;
    line-height: var(--line-height-normal);
    letter-spacing: 0.025em;
}

.btn-sm {
    font-size: var(--font-size-sm);
}

.btn-lg {
    font-size: var(--font-size-lg);
}

/* Navigation */
.navbar-brand {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: var(--font-size-xl);
}

.nav-link {
    font-family: var(--font-primary);
    font-weight: 500;
    font-size: var(--font-size-base);
}

/* Cards */
.card-title {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: var(--line-height-tight);
}

.card-text {
    line-height: var(--line-height-relaxed);
}

.card-header {
    font-weight: 600;
}

/* Tables */
.table {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.table th {
    font-weight: 600;
    font-size: var(--font-size-sm);
    letter-spacing: 0.025em;
}

.table td {
    line-height: var(--line-height-relaxed);
}

/* Badges and Labels */
.badge {
    font-family: var(--font-primary);
    font-weight: 500;
    font-size: var(--font-size-xs);
    letter-spacing: 0.025em;
}

/* Alerts */
.alert {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

/* Breadcrumbs */
.breadcrumb-item {
    font-size: var(--font-size-sm);
}

/* Sidebar */
.sidebar .nav-link {
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: var(--line-height-normal);
}

/* Numbers and Currency */
.number, .currency {
    font-feature-settings: "tnum" 1, "lnum" 1;
    font-variant-numeric: tabular-nums lining-nums;
}

.arabic-numerals {
    font-feature-settings: "anum" 1;
}

/* Print Styles */
@media print {
    body {
        font-family: var(--font-primary) !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
    }
    
    h1, h2, h3, h4, h5, h6 {
        font-weight: 600 !important;
        color: #000 !important;
        page-break-after: avoid;
    }
    
    .card, .table {
        font-size: 11pt !important;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    :root {
        --font-size-base: 0.9rem;
        --font-size-sm: 0.8rem;
        --font-size-lg: 1rem;
        --font-size-xl: 1.1rem;
        --font-size-2xl: 1.3rem;
        --font-size-3xl: 1.6rem;
    }
    
    body {
        line-height: var(--line-height-relaxed);
    }
    
    h1, h2, h3, h4, h5, h6 {
        line-height: var(--line-height-normal);
    }
}

/* Font Loading States */
.font-loading {
    font-family: var(--font-fallback);
    visibility: hidden;
}

.fonts-loaded {
    visibility: visible;
    font-family: var(--font-primary);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    body {
        font-weight: 600;
    }
    
    .form-control, .btn {
        border-width: 2px;
    }
}

/* Font size preferences */
@media (prefers-reduced-data: reduce) {
    /* Use system fonts when data usage is a concern */
    body {
        font-family: var(--font-fallback);
    }
}
