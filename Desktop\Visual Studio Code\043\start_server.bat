@echo off
echo Starting Car Dealership System...
echo.

REM Set environment variables
set FLASK_APP=app.py
set FLASK_ENV=development
set FLASK_DEBUG=1

echo Environment variables set:
echo FLASK_APP=%FLASK_APP%
echo FLASK_ENV=%FLASK_ENV%
echo FLASK_DEBUG=%FLASK_DEBUG%
echo.

echo Starting Flask server...
echo Server will be available at: http://localhost:5000
echo.
echo Press Ctrl+C to stop the server
echo.

REM Try different Python commands
python -c "from app import create_app; app = create_app(); app.run(debug=True, host='0.0.0.0', port=5000)"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to start with python command, trying py...
    py -c "from app import create_app; app = create_app(); app.run(debug=True, host='0.0.0.0', port=5000)"
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to start with py command, trying flask run...
    flask run --host=0.0.0.0 --port=5000 --debug
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo All startup methods failed. Please check:
    echo 1. Python is installed and in PATH
    echo 2. Required packages are installed: pip install -r requirements.txt
    echo 3. No syntax errors in the code
    echo.
    pause
)
