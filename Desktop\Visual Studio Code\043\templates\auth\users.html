{% extends "base.html" %}

{% block title %}إدارة المستخدمين - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة المستخدمين</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.add_user') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المستخدمين</h5>
    </div>
    <div class="card-body">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم الكامل</th>
                        <th>اسم المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>آخر دخول</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" 
                                     style="width: 35px; height: 35px; font-size: 0.8rem;">
                                    {{ user.full_name[:2].upper() }}
                                </div>
                                <div>
                                    <strong>{{ user.full_name }}</strong>
                                    {% if user.phone %}
                                    <br><small class="text-muted">{{ user.phone }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>{{ user.username }}</code>
                            {% if user.id == current_user.id %}
                            <span class="badge bg-info ms-1">أنت</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="mailto:{{ user.email }}" class="text-decoration-none">
                                {{ user.email }}
                            </a>
                        </td>
                        <td>
                            {% if user.role == 'manager' %}
                                <span class="badge bg-danger">مدير</span>
                            {% elif user.role == 'sales' %}
                                <span class="badge bg-success">موظف مبيعات</span>
                            {% elif user.role == 'accountant' %}
                                <span class="badge bg-info">محاسب</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ user.role }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times"></i>
                                    معطل
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.last_login %}
                                {{ user.last_login.strftime('%Y/%m/%d') }}
                                <br><small class="text-muted">{{ user.last_login.strftime('%H:%M') }}</small>
                            {% else %}
                                <span class="text-muted">لم يسجل دخول</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ user.created_at.strftime('%Y/%m/%d') }}
                            <br><small class="text-muted">{{ user.created_at.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('auth.edit_user', user_id=user.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                {% if user.id != current_user.id %}
                                <form method="POST" action="{{ url_for('auth.delete_user', user_id=user.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if users.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('auth.users', page=users.prev_num) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in users.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != users.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('auth.users', page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('auth.users', page=users.next_num) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5>لا يوجد مستخدمين</h5>
            <p class="text-muted">لم يتم إضافة أي مستخدمين بعد.</p>
            <a href="{{ url_for('auth.add_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                إضافة أول مستخدم
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي المستخدمين</h5>
                <h2 class="text-primary">{{ users.total }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">المديرين</h5>
                <h2 class="text-danger">
                    {{ users.items|selectattr('role', 'equalto', 'manager')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">موظفي المبيعات</h5>
                <h2 class="text-success">
                    {{ users.items|selectattr('role', 'equalto', 'sales')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">المحاسبين</h5>
                <h2 class="text-info">
                    {{ users.items|selectattr('role', 'equalto', 'accountant')|list|length }}
                </h2>
            </div>
        </div>
    </div>
</div>

<!-- Roles Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات الأدوار والصلاحيات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">المدير</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>جميع الصلاحيات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إدارة المستخدمين</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الإعدادات العامة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>حذف البيانات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">موظف المبيعات</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>إدارة السيارات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إدارة العملاء</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إنشاء المبيعات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>عرض التقارير</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">المحاسب</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>عرض جميع البيانات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إدارة المدفوعات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>التقارير المالية</li>
                                    <li><i class="fas fa-check text-success me-2"></i>متابعة الأقساط</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add tooltips
    $('[title]').tooltip();
    
    // Animate cards
    $('.card').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
    
    // Confirm delete
    $('.delete-btn').on('click', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
