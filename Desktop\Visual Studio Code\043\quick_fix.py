#!/usr/bin/env python3
"""
إصلاح سريع لمشاكل النظام
Quick fix for system issues
"""

import os
import sys
import subprocess

def main():
    """Main quick fix function"""
    print("🚀 الإصلاح السريع لمشاكل النظام")
    print("=" * 40)
    
    # Step 1: Fix layout issues
    print("1️⃣ إصلاح مشاكل التخطيط...")
    try:
        subprocess.run([sys.executable, 'fix_layout_issues.py'], check=True)
        print("   ✅ تم إصلاح مشاكل التخطيط")
    except subprocess.CalledProcessError:
        print("   ⚠️ خطأ في إصلاح التخطيط")
    except FileNotFoundError:
        print("   ⚠️ ملف الإصلاح غير موجود")
    
    # Step 2: Update database
    print("\n2️⃣ تحديث قاعدة البيانات...")
    try:
        subprocess.run([sys.executable, 'update_database.py'], check=True)
        print("   ✅ تم تحديث قاعدة البيانات")
    except subprocess.CalledProcessError:
        print("   ⚠️ خطأ في تحديث قاعدة البيانات")
    except FileNotFoundError:
        print("   ⚠️ ملف التحديث غير موجود")
    
    # Step 3: Update Arabic numbers
    print("\n3️⃣ تحديث نظام الأرقام العربية...")
    try:
        subprocess.run([sys.executable, 'update_arabic_numbers.py'], check=True)
        print("   ✅ تم تحديث الأرقام العربية")
    except subprocess.CalledProcessError:
        print("   ⚠️ خطأ في تحديث الأرقام العربية")
    except FileNotFoundError:
        print("   ⚠️ ملف تحديث الأرقام غير موجود")
    
    # Step 4: Start system
    print("\n4️⃣ تشغيل النظام...")
    print("🌐 الروابط المهمة:")
    print("   • الصفحة الرئيسية: http://localhost:5000")
    print("   • صفحة التشخيص: http://localhost:5000/debug-layout")
    print("   • اختبار الأرقام العربية: http://localhost:5000/arabic-numbers-test")
    print("   • أرقام السيارات: http://localhost:5000/plate-numbers")
    
    print("\n👤 بيانات الدخول:")
    print("   • المستخدم: admin")
    print("   • كلمة المرور: admin123")
    
    print("\n🛑 اضغط Ctrl+C للإيقاف")
    print("=" * 40)
    
    try:
        from app import create_app
        app = create_app()
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n💡 جرب:")
        print("   python fix_layout_issues.py")
        print("   python quick_start.py")

if __name__ == '__main__':
    main()
