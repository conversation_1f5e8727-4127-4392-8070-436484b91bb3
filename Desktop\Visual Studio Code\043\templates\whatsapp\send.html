{% extends "base.html" %}

{% block title %}إرسال رسالة واتساب - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إرسال رسالة واتساب</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('whatsapp.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة لقائمة الرسائل
        </a>
    </div>
</div>

<form method="POST" id="whatsappForm">
    <div class="row">
        <!-- Message Form -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل الرسالة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Recipient Selection -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">المستقبل <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}"
                                            {{ 'selected' if (selected_customer and selected_customer.id == customer.id) or request.args.get('customer_id') == customer.id|string }}
                                            data-phone="{{ customer.phone }}"
                                            data-whatsapp="{{ customer.whatsapp_number or customer.phone }}"
                                            data-name="{{ customer.full_name }}">
                                        {{ customer.full_name }} - {{ customer.whatsapp_number or customer.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <i class="fab fa-whatsapp text-success me-1"></i>
                                    سيتم إرسال الرسالة إلى رقم الواتساب المسجل للعميل
                                </div>
                            </div>
                        </div>
                        
                        <!-- Phone Number Display -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone_display" class="form-label">رقم الواتساب</label>
                                <input type="text" class="form-control" id="phone_display" readonly 
                                       placeholder="سيظهر رقم الواتساب هنا">
                                <div class="form-text">
                                    <i class="fab fa-whatsapp text-success me-1"></i>
                                    تأكد من صحة الرقم قبل الإرسال
                                </div>
                            </div>
                        </div>
                        
                        <!-- Message Type -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="message_type" class="form-label">نوع الرسالة <span class="text-danger">*</span></label>
                                <select class="form-select" id="message_type" name="message_type" required>
                                    <option value="">اختر نوع الرسالة</option>
                                    <option value="payment_reminder">تذكير بالدفع</option>
                                    <option value="welcome">رسالة ترحيب</option>
                                    <option value="follow_up">متابعة</option>
                                    <option value="promotion">عرض ترويجي</option>
                                    <option value="custom">رسالة مخصصة</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Template Selection -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template_id" class="form-label">القالب</label>
                                <div class="input-group">
                                    <select class="form-select" id="template_id" name="template_id">
                                        <option value="">اختر قالب جاهز (اختياري)</option>
                                        {% for template in templates %}
                                        <option value="{{ template.id }}"
                                                data-content="{{ template.content }}"
                                                data-type="{{ template.type }}"
                                                {{ 'selected' if selected_template and selected_template.id == template.id }}>
                                            {{ template.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#quickTemplatesModal">
                                        <i class="fas fa-bolt"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-magic me-1"></i>
                                    يمكنك استخدام قالب جاهز أو كتابة رسالة مخصصة
                                </div>
                            </div>
                        </div>
                        
                        <!-- Message Content -->
                        <div class="col-12">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="message" class="form-label">نص الرسالة <span class="text-danger">*</span></label>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="insertVariable('{customer_name}')">
                                            اسم العميل
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="insertVariable('{amount}')">
                                            المبلغ
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="insertVariable('{today}')">
                                            التاريخ
                                        </button>
                                    </div>
                                </div>
                                <textarea class="form-control" id="message" name="message" rows="8" required
                                          placeholder="اكتب رسالتك هنا...">{{ selected_template.content if selected_template else '' }}</textarea>
                                <div class="d-flex justify-content-between align-items-center mt-2">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        استخدم المتغيرات لتخصيص الرسالة لكل عميل
                                    </div>
                                    <div class="char-counter">
                                        <span id="charCount" class="text-muted">0</span> / 1000 حرف
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Message Preview -->
                        <div class="col-12">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label">معاينة الرسالة</label>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="previewMessage()">
                                        <i class="fas fa-eye me-1"></i>
                                        معاينة
                                    </button>
                                </div>
                                <div id="messagePreview" class="border rounded p-3 bg-light" style="min-height: 100px;">
                                    <div class="text-muted text-center">
                                        <i class="fab fa-whatsapp fa-2x mb-2"></i><br>
                                        اختر عميل واكتب رسالة لرؤية المعاينة
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Send Options -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">وقت الإرسال</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="send_time" id="sendNow" value="now" checked>
                                    <label class="form-check-label" for="sendNow">
                                        <i class="fas fa-paper-plane me-1"></i>
                                        إرسال فوري
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="send_time" id="sendScheduled" value="scheduled">
                                    <label class="form-check-label" for="sendScheduled">
                                        <i class="fas fa-clock me-1"></i>
                                        جدولة الإرسال
                                    </label>
                                </div>
                                <div id="scheduledTimeGroup" class="mt-2" style="display: none;">
                                    <input type="datetime-local" class="form-control" name="scheduled_time" id="scheduledTime">
                                </div>
                            </div>
                        </div>

                        <!-- Save as Template -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="saveAsTemplate" name="save_as_template">
                                    <label class="form-check-label" for="saveAsTemplate">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ كقالب جديد
                                    </label>
                                </div>
                                <div id="templateNameGroup" class="mt-2" style="display: none;">
                                    <input type="text" class="form-control" name="template_name" placeholder="اسم القالب">
                                </div>
                            </div>
                        </div>

                        <!-- Variables Help -->
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>المتغيرات المتاحة:</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>معلومات العميل:</strong>
                                        <ul class="mb-0 small">
                                            <li><code>{customer_name}</code> - اسم العميل</li>
                                            <li><code>{phone}</code> - رقم الهاتف</li>
                                            <li><code>{email}</code> - البريد الإلكتروني</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>معلومات السيارة:</strong>
                                        <ul class="mb-0 small">
                                            <li><code>{car_details}</code> - تفاصيل السيارة</li>
                                            <li><code>{chassis_number}</code> - رقم الشاسيه</li>
                                            <li><code>{sale_price}</code> - سعر البيع</li>
                                            <li><code>{sale_type}</code> - نوع البيع</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>معلومات الأقساط:</strong>
                                        <ul class="mb-0 small">
                                            <li><code>{installment_number}</code> - رقم القسط</li>
                                            <li><code>{due_date}</code> - تاريخ الاستحقاق</li>
                                            <li><code>{amount}</code> - مبلغ القسط</li>
                                            <li><code>{days_overdue}</code> - أيام التأخير</li>
                                        </ul>
                                    </div>
                                </div>
                                <hr class="my-2">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>معلومات النظام:</strong>
                                        <ul class="mb-0 small">
                                            <li><code>{company_name}</code> - اسم الشركة</li>
                                            <li><code>{today}</code> - تاريخ اليوم</li>
                                            <li><code>{time}</code> - الوقت الحالي</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Send Options -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="send_time" class="form-label">وقت الإرسال</label>
                                <select class="form-select" id="send_time" name="send_time">
                                    <option value="now">الآن</option>
                                    <option value="scheduled">جدولة لوقت لاحق</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Scheduled Time -->
                        <div class="col-md-6" id="scheduledTimeField" style="display: none;">
                            <div class="mb-3">
                                <label for="scheduled_time" class="form-label">الوقت المجدول</label>
                                <input type="datetime-local" class="form-control" id="scheduled_time" name="scheduled_time">
                            </div>
                        </div>
                        
                        <!-- Save as Template -->
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="save_as_template" name="save_as_template">
                                <label class="form-check-label" for="save_as_template">
                                    حفظ كقالب للاستخدام المستقبلي
                                </label>
                            </div>
                            
                            <div class="mt-2" id="templateNameField" style="display: none;">
                                <input type="text" class="form-control" id="template_name" name="template_name" 
                                       placeholder="اسم القالب...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Preview and Actions -->
        <div class="col-lg-4">
            <!-- Message Preview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معاينة الرسالة</h5>
                </div>
                <div class="card-body">
                    <div class="whatsapp-preview">
                        <div class="chat-bubble">
                            <div class="message-content" id="messagePreview">
                                اكتب رسالتك لرؤية المعاينة...
                            </div>
                            <div class="message-time">
                                <small class="text-muted">الآن</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Customer Info -->
            <div class="card mb-4" id="customerInfoCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">معلومات العميل</h5>
                </div>
                <div class="card-body" id="customerInfo">
                    <!-- Customer info will be loaded here -->
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fab fa-whatsapp me-2"></i>
                            إرسال الرسالة
                        </button>
                        
                        <button type="button" class="btn btn-outline-primary" onclick="testMessage()">
                            <i class="fas fa-eye me-2"></i>
                            معاينة في واتساب
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="saveAsDraft()">
                            <i class="fas fa-save me-2"></i>
                            حفظ كمسودة
                        </button>
                        
                        <hr>
                        
                        <a href="{{ url_for('whatsapp.templates') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-file-alt me-2"></i>
                            إدارة القوالب
                        </a>
                        
                        <a href="{{ url_for('whatsapp.history') }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-history me-2"></i>
                            تاريخ الرسائل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Quick Templates Modal -->
<div class="modal fade" id="quickTemplatesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">القوالب السريعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>تذكيرات الدفع</h6>
                        <div class="list-group">
                            <button type="button" class="list-group-item list-group-item-action" 
                                    onclick="useQuickTemplate('payment_gentle')">
                                تذكير لطيف بالدفع
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" 
                                    onclick="useQuickTemplate('payment_urgent')">
                                تذكير عاجل بالدفع
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>رسائل عامة</h6>
                        <div class="list-group">
                            <button type="button" class="list-group-item list-group-item-action" 
                                    onclick="useQuickTemplate('welcome')">
                                رسالة ترحيب
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" 
                                    onclick="useQuickTemplate('thank_you')">
                                رسالة شكر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.whatsapp-preview {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
    padding: 20px;
    border-radius: 10px;
    min-height: 200px;
}

.chat-bubble {
    background: #DCF8C6;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 85%;
    margin-left: auto;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: relative;
}

.chat-bubble::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: #DCF8C6;
    border-bottom: 0;
    margin-left: -8px;
    margin-bottom: -8px;
}

.message-content {
    color: #303030;
    line-height: 1.4;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message-time {
    text-align: right;
    margin-top: 5px;
}

.message-time small {
    color: #667781;
    font-size: 11px;
}

.template-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.template-item:hover {
    background-color: #f8f9fa;
}

.char-counter {
    font-size: 0.875rem;
}

.char-counter.warning {
    color: #ffc107;
}

.char-counter.danger {
    color: #dc3545;
}

@media (max-width: 768px) {
    .whatsapp-preview {
        padding: 15px;
    }
    
    .chat-bubble {
        max-width: 95%;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update phone display when customer is selected
    $('#customer_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const phone = selectedOption.data('whatsapp') || selectedOption.data('phone');
        const customerId = $(this).val();
        
        $('#phone_display').val(phone || '');
        
        if (customerId) {
            loadCustomerInfo(customerId);
            $('#customerInfoCard').show();
        } else {
            $('#customerInfoCard').hide();
        }
        
        updatePreview();
    });
    
    // Update preview when message changes
    $('#message').on('input', function() {
        updatePreview();
        updateCharCount();
    });
    
    // Load template content
    $('#template_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const content = selectedOption.data('content');
        
        if (content) {
            $('#message').val(content);
            updatePreview();
            updateCharCount();
        }
    });
    
    // Show/hide scheduled time field
    $('#send_time').on('change', function() {
        if ($(this).val() === 'scheduled') {
            $('#scheduledTimeField').show();
            $('#scheduled_time').prop('required', true);
        } else {
            $('#scheduledTimeField').hide();
            $('#scheduled_time').prop('required', false);
        }
    });
    
    // Show/hide template name field
    $('#save_as_template').on('change', function() {
        if ($(this).is(':checked')) {
            $('#templateNameField').show();
            $('#template_name').prop('required', true);
        } else {
            $('#templateNameField').hide();
            $('#template_name').prop('required', false);
        }
    });
    
    // Auto-fill message type based on template
    $('#template_id').on('change', function() {
        const templateName = $(this).find('option:selected').text().toLowerCase();
        
        if (templateName.includes('دفع') || templateName.includes('تذكير')) {
            $('#message_type').val('payment_reminder');
        } else if (templateName.includes('ترحيب')) {
            $('#message_type').val('welcome');
        } else if (templateName.includes('متابعة')) {
            $('#message_type').val('follow_up');
        } else if (templateName.includes('عرض')) {
            $('#message_type').val('promotion');
        }
    });
    
    // Form validation
    $('#whatsappForm').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
});

function updatePreview() {
    let message = $('#message').val();
    const customerName = $('#customer_id option:selected').text().split(' - ')[0];
    
    // Replace variables
    message = message.replace(/{customer_name}/g, customerName || '{customer_name}');
    message = message.replace(/{company_name}/g, '{{ company_name }}');
    message = message.replace(/{today}/g, new Date().toLocaleDateString('ar-SA'));
    message = message.replace(/{time}/g, new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
    
    $('#messagePreview').text(message || 'اكتب رسالتك لرؤية المعاينة...');
}

function updateCharCount() {
    const message = $('#message').val();
    const count = message.length;
    const counter = $('#charCount');
    
    counter.text(count);
    
    if (count > 800) {
        counter.removeClass('text-muted text-warning').addClass('text-danger');
    } else if (count > 600) {
        counter.removeClass('text-muted text-danger').addClass('text-warning');
    } else {
        counter.removeClass('text-warning text-danger').addClass('text-muted');
    }
}

function loadCustomerInfo(customerId) {
    // This would typically load customer info via AJAX
    // For now, we'll use the data from the select option
    const selectedOption = $('#customer_id option:selected');
    const customerName = selectedOption.text().split(' - ')[0];
    const phone = selectedOption.data('whatsapp') || selectedOption.data('phone');
    
    const customerInfo = `
        <div class="d-flex align-items-center mb-3">
            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" 
                 style="width: 50px; height: 50px;">
                ${customerName.substring(0, 2).toUpperCase()}
            </div>
            <div>
                <h6 class="mb-1">${customerName}</h6>
                <small class="text-muted">${phone}</small>
            </div>
        </div>
        <div class="alert alert-success">
            <i class="fab fa-whatsapp me-2"></i>
            جاهز للإرسال
        </div>
    `;
    
    $('#customerInfo').html(customerInfo);
}

function testMessage() {
    const phone = $('#phone_display').val();
    const message = $('#message').val();
    
    if (!phone || !message) {
        showAlert('يرجى اختيار العميل وكتابة الرسالة أولاً', 'warning');
        return;
    }
    
    // Open WhatsApp with pre-filled message
    const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function saveAsDraft() {
    const formData = {
        customer_id: $('#customer_id').val(),
        message: $('#message').val(),
        message_type: $('#message_type').val()
    };
    
    // Save to localStorage as draft
    localStorage.setItem('whatsapp_draft', JSON.stringify(formData));
    showAlert('تم حفظ المسودة بنجاح', 'success');
}

function useQuickTemplate(templateType) {
    const templates = {
        'payment_gentle': 'مرحباً {customer_name}،\n\nنتمنى أن تكون بخير. نود تذكيرك بأن لديك قسط مستحق بمبلغ {amount} ريال.\n\nيرجى التواصل معنا لترتيب الدفع.\n\nشكراً لك\n{company_name}',
        'payment_urgent': 'عزيزي {customer_name}،\n\nنود إعلامك بأن القسط المستحق بمبلغ {amount} ريال قد تأخر عن موعده.\n\nيرجى المبادرة بالدفع لتجنب أي رسوم إضافية.\n\n{company_name}',
        'welcome': 'أهلاً وسهلاً {customer_name}،\n\nنرحب بك في {company_name}. نحن سعداء لخدمتك وتقديم أفضل الخدمات لك.\n\nلا تتردد في التواصل معنا في أي وقت.',
        'thank_you': 'شكراً لك {customer_name}،\n\nنقدر ثقتك في {company_name} ونتطلع لخدمتك مرة أخرى.\n\nتحياتنا'
    };
    
    if (templates[templateType]) {
        $('#message').val(templates[templateType]);
        updatePreview();
        updateCharCount();
        $('#quickTemplatesModal').modal('hide');
    }
}

function validateForm() {
    let isValid = true;
    
    if (!$('#customer_id').val()) {
        showFieldError($('#customer_id'), 'يجب اختيار العميل');
        isValid = false;
    }
    
    if (!$('#message_type').val()) {
        showFieldError($('#message_type'), 'يجب اختيار نوع الرسالة');
        isValid = false;
    }
    
    if (!$('#message').val().trim()) {
        showFieldError($('#message'), 'يجب كتابة نص الرسالة');
        isValid = false;
    }
    
    if ($('#message').val().length > 1000) {
        showFieldError($('#message'), 'الرسالة طويلة جداً (الحد الأقصى 1000 حرف)');
        isValid = false;
    }
    
    if ($('#send_time').val() === 'scheduled' && !$('#scheduled_time').val()) {
        showFieldError($('#scheduled_time'), 'يجب تحديد وقت الإرسال المجدول');
        isValid = false;
    }
    
    if ($('#save_as_template').is(':checked') && !$('#template_name').val().trim()) {
        showFieldError($('#template_name'), 'يجب إدخال اسم القالب');
        isValid = false;
    }
    
    return isValid;
}

// Load draft on page load
$(document).ready(function() {
    const draft = localStorage.getItem('whatsapp_draft');
    if (draft) {
        try {
            const draftData = JSON.parse(draft);
            $('#customer_id').val(draftData.customer_id).trigger('change');
            $('#message').val(draftData.message);
            $('#message_type').val(draftData.message_type);
            updatePreview();
            updateCharCount();
            
            showAlert('تم تحميل المسودة المحفوظة', 'info');
        } catch (e) {
            console.error('Error loading draft:', e);
        }
    }
});
</script>
{% endblock %}
