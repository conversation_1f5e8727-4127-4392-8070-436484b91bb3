{% extends "base.html" %}

{% block title %}عملية بيع جديدة - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">عملية بيع جديدة</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('sales.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<form method="POST" id="saleForm">
    <div class="row">
        <!-- Sale Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">بيانات المبيعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Customer Selection -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" 
                                            {{ 'selected' if request.args.get('customer_id') == customer.id|string }}
                                            data-phone="{{ customer.phone }}"
                                            data-national-id="{{ customer.national_id }}">
                                        {{ customer.full_name }} - {{ customer.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <a href="{{ url_for('customers.add') }}" target="_blank">إضافة عميل جديد</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Car Selection -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="car_id" class="form-label">السيارة <span class="text-danger">*</span></label>
                                <select class="form-select" id="car_id" name="car_id" required>
                                    <option value="">اختر السيارة</option>
                                    {% for car in cars %}
                                    <option value="{{ car.id }}" 
                                            {{ 'selected' if request.args.get('car_id') == car.id|string }}
                                            data-price="{{ car.price }}"
                                            data-chassis="{{ car.chassis_number }}">
                                        {{ car.brand }} {{ car.model }} {{ car.year }} - {{ car.price|currency }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <a href="{{ url_for('cars.add') }}" target="_blank">إضافة سيارة جديدة</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sale Type -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sale_type" class="form-label">نوع البيع <span class="text-danger">*</span></label>
                                <select class="form-select" id="sale_type" name="sale_type" required>
                                    <option value="">اختر نوع البيع</option>
                                    <option value="cash">نقدي</option>
                                    <option value="installment">تقسيط</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Sale Date -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sale_date" class="form-label">تاريخ البيع <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="sale_date" name="sale_date" 
                                       value="{{ current_datetime.strftime('%Y-%m-%d') }}" required>
                            </div>
                        </div>
                        
                        <!-- Sale Price -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sale_price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control currency-input" id="sale_price"
                                           name="sale_price" data-format="currency" required
                                           placeholder="أدخل سعر البيع" autocomplete="off">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">مثال: 50000 أو 50,000</div>
                            </div>
                        </div>
                        
                        <!-- Down Payment (for installment) -->
                        <div class="col-md-6" id="downPaymentField" style="display: none;">
                            <div class="mb-3">
                                <label for="down_payment" class="form-label">الدفعة المقدمة</label>
                                <div class="input-group">
                                    <input type="text" class="form-control currency-input" id="down_payment"
                                           name="down_payment" data-format="currency"
                                           placeholder="أدخل الدفعة المقدمة" autocomplete="off">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">اختياري - سيتم خصمها من إجمالي المبلغ</div>
                            </div>
                        </div>
                        
                        <!-- Installment Details (for installment) -->
                        <div id="installmentDetails" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installment_count" class="form-label">عدد الأقساط <span class="text-danger">*</span></label>
                                    <select class="form-select" id="installment_count" name="installment_count">
                                        <option value="">اختر عدد الأقساط</option>
                                        <option value="6">6 أقساط</option>
                                        <option value="12">12 قسط</option>
                                        <option value="18">18 قسط</option>
                                        <option value="24">24 قسط</option>
                                        <option value="36">36 قسط</option>
                                        <option value="48">48 قسط</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installment_amount" class="form-label">قيمة القسط</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="installment_amount"
                                               name="installment_amount" step="0.01" min="0" readonly>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_installment_date" class="form-label">تاريخ أول قسط <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="first_installment_date"
                                           name="first_installment_date">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="installment_frequency" class="form-label">تكرار الأقساط</label>
                                    <select class="form-select" id="installment_frequency" name="installment_frequency">
                                        <option value="monthly" selected>شهري</option>
                                        <option value="weekly">أسبوعي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="أضف أي ملاحظات حول المبيعة..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Summary -->
            <div class="card" id="summaryCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">ملخص المبيعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>العميل:</strong></td>
                                    <td id="summaryCustomer">-</td>
                                </tr>
                                <tr>
                                    <td><strong>السيارة:</strong></td>
                                    <td id="summaryCar">-</td>
                                </tr>
                                <tr>
                                    <td><strong>نوع البيع:</strong></td>
                                    <td id="summarySaleType">-</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>سعر البيع:</strong></td>
                                    <td id="summarySalePrice">-</td>
                                </tr>
                                <tr id="summaryDownPaymentRow" style="display: none;">
                                    <td><strong>الدفعة المقدمة:</strong></td>
                                    <td id="summaryDownPayment">-</td>
                                </tr>
                                <tr id="summaryMonthlyPaymentRow" style="display: none;">
                                    <td><strong>القسط الشهري:</strong></td>
                                    <td id="summaryMonthlyPayment">-</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">نصائح مهمة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                        <ul class="mb-0">
                            <li>تأكد من اختيار العميل والسيارة الصحيحين</li>
                            <li>للبيع بالتقسيط، حدد الدفعة المقدمة وعدد الأشهر</li>
                            <li>سيتم حساب القسط الشهري تلقائياً</li>
                            <li>يمكن تعديل سعر البيع إذا اختلف عن سعر السيارة</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                        <p class="mb-0">الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة.</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المبيعة
                        </button>
                        <a href="{{ url_for('sales.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <hr>
                        <a href="{{ url_for('customers.add') }}" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عميل جديد
                        </a>
                        <a href="{{ url_for('cars.add') }}" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-car me-2"></i>
                            إضافة سيارة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Ensure InputValidator is initialized for this form
    if (window.inputValidator) {
        window.inputValidator.setupCurrencyInputs();
    } else {
        // Fallback if InputValidator is not loaded
        $('.currency-input').on('input', function() {
            let value = $(this).val().replace(/[^\d.]/g, '');
            if (value) {
                $(this).val(value);
            }
        });
    }

    // Auto-fill sale price when car is selected
    $('#car_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const price = selectedOption.data('price');
        if (price) {
            $('#sale_price').val(price);
        }
        updateSummary();
    });
    
    // Show/hide installment fields based on sale type
    $('#sale_type').on('change', function() {
        const saleType = $(this).val();
        if (saleType === 'installment') {
            $('#downPaymentField').show();
            $('#installmentDetails').show();
        } else {
            $('#downPaymentField').hide();
            $('#installmentDetails').hide();
            $('#down_payment').val('');
            $('#installment_count').val('');
            $('#installment_amount').val('');
            $('#first_installment_date').val('');
        }
        updateSummary();
    });
    
    // Calculate installment amount
    $('#down_payment, #installment_count, #sale_price').on('input change', function() {
        calculateInstallmentAmount();
        updateSummary();
    });
    
    // Update summary when fields change
    $('#customer_id, #car_id, #sale_type, #sale_price').on('change', function() {
        updateSummary();
    });
    
    function calculateInstallmentAmount() {
        const salePrice = parseFloat($('#sale_price').val()) || 0;
        const downPayment = parseFloat($('#down_payment').val()) || 0;
        const installmentCount = parseInt($('#installment_count').val()) || 0;

        if (salePrice > 0 && installmentCount > 0) {
            const remainingAmount = salePrice - downPayment;
            const installmentAmount = remainingAmount / installmentCount;
            $('#installment_amount').val(installmentAmount.toFixed(2));
        } else {
            $('#installment_amount').val('');
        }
    }
    
    function updateSummary() {
        const customer = $('#customer_id option:selected').text();
        const car = $('#car_id option:selected').text();
        const saleType = $('#sale_type option:selected').text();
        const salePrice = $('#sale_price').val();
        const downPayment = $('#down_payment').val();
        const installmentAmount = $('#installment_amount').val();
        
        if (customer && car && saleType && salePrice) {
            $('#summaryCustomer').text(customer);
            $('#summaryCar').text(car);
            $('#summarySaleType').text(saleType);
            $('#summarySalePrice').text(salePrice ? formatCurrency(salePrice) : '-');
            
            if ($('#sale_type').val() === 'installment') {
                $('#summaryDownPaymentRow').show();
                $('#summaryMonthlyPaymentRow').show();
                $('#summaryDownPayment').text(downPayment ? formatCurrency(downPayment) : '-');
                $('#summaryMonthlyPayment').text(installmentAmount ? formatCurrency(installmentAmount) : '-');
            } else {
                $('#summaryDownPaymentRow').hide();
                $('#summaryMonthlyPaymentRow').hide();
            }
            
            $('#summaryCard').show();
        } else {
            $('#summaryCard').hide();
        }
    }
    
    function formatCurrency(amount) {
        return parseFloat(amount).toLocaleString('ar-SA') + ' ريال';
    }
    
    // Form validation
    $('#saleForm').on('submit', function(e) {
        if (!validateSaleForm()) {
            e.preventDefault();
        }
    });
    
    function validateSaleForm() {
        let isValid = true;
        
        // Check required fields
        if (!$('#customer_id').val()) {
            showFieldError($('#customer_id'), 'يجب اختيار العميل');
            isValid = false;
        }
        
        if (!$('#car_id').val()) {
            showFieldError($('#car_id'), 'يجب اختيار السيارة');
            isValid = false;
        }
        
        if (!$('#sale_type').val()) {
            showFieldError($('#sale_type'), 'يجب اختيار نوع البيع');
            isValid = false;
        }
        
        const salePrice = parseFloat($('#sale_price').val());
        if (!salePrice || salePrice <= 0) {
            showFieldError($('#sale_price'), 'سعر البيع يجب أن يكون أكبر من صفر');
            isValid = false;
        }
        
        // Validate installment fields
        if ($('#sale_type').val() === 'installment') {
            const downPayment = parseFloat($('#down_payment').val()) || 0;
            const installmentCount = parseInt($('#installment_count').val());
            const firstInstallmentDate = $('#first_installment_date').val();

            if (downPayment >= salePrice) {
                showFieldError($('#down_payment'), 'الدفعة المقدمة يجب أن تكون أقل من سعر البيع');
                isValid = false;
            }

            if (!installmentCount || installmentCount <= 0) {
                showFieldError($('#installment_count'), 'عدد الأقساط يجب أن يكون أكبر من صفر');
                isValid = false;
            }

            if (!firstInstallmentDate) {
                showFieldError($('#first_installment_date'), 'تاريخ أول قسط مطلوب للبيع بالتقسيط');
                isValid = false;
            }
        }
        
        return isValid;
    }

    function showFieldError(field, message) {
        field.addClass('is-invalid');

        // Remove existing error message
        field.siblings('.invalid-feedback').remove();

        // Add new error message
        field.after(`<div class="invalid-feedback">${message}</div>`);

        // Remove error styling after user starts typing
        field.one('input change', function() {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        });

        // Show alert
        showAlert(message, 'danger');
    }
});
</script>
{% endblock %}
