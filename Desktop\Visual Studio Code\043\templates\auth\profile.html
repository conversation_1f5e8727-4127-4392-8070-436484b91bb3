{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">الملف الشخصي</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
            <i class="fas fa-key me-2"></i>
            تغيير كلمة المرور
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>الاسم الكامل:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.full_name }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>اسم المستخدم:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.username }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>البريد الإلكتروني:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.email }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>رقم الهاتف:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.phone or 'غير محدد' }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>الدور:</strong>
                    </div>
                    <div class="col-sm-9">
                        {% if user.role == 'manager' %}
                            <span class="badge bg-danger">مدير</span>
                        {% elif user.role == 'sales' %}
                            <span class="badge bg-success">موظف مبيعات</span>
                        {% elif user.role == 'accountant' %}
                            <span class="badge bg-info">محاسب</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>تاريخ الإنشاء:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.created_at.strftime('%Y/%m/%d %H:%M') }}
                    </div>
                </div>
                
                {% if user.last_login %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>آخر تسجيل دخول:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.last_login.strftime('%Y/%m/%d %H:%M') }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الصلاحيات</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% if user.role == 'manager' %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            جميع الصلاحيات
                        </div>
                    {% else %}
                        {% if user.has_permission('view_cars') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            عرض السيارات
                        </div>
                        {% endif %}
                        
                        {% if user.has_permission('edit_cars') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            تعديل السيارات
                        </div>
                        {% endif %}
                        
                        {% if user.has_permission('view_customers') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            عرض العملاء
                        </div>
                        {% endif %}
                        
                        {% if user.has_permission('edit_customers') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            تعديل العملاء
                        </div>
                        {% endif %}
                        
                        {% if user.has_permission('create_sales') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            إنشاء المبيعات
                        </div>
                        {% endif %}
                        
                        {% if user.has_permission('payments') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            إدارة المدفوعات
                        </div>
                        {% endif %}
                        
                        {% if user.has_permission('reports') %}
                        <div class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            عرض التقارير
                        </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
