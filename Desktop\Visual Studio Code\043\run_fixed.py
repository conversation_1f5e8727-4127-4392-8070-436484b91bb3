#!/usr/bin/env python3
"""
تشغيل النظام مع الإصلاحات النهائية
Run system with final fixes
"""

import os
import sys

def main():
    """Run the system with all fixes applied"""
    print("🎉 تشغيل النظام مع الإصلاحات النهائية")
    print("=" * 50)
    
    # Set environment variables for optimal performance
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'true'
    os.environ['FORCE_RTL'] = 'true'
    os.environ['ULTIMATE_FIX'] = 'true'
    
    try:
        from app import create_app
        app = create_app()
        
        print("✅ تم تحميل النظام مع جميع الإصلاحات")
        print("🔧 تم تفعيل الإصلاح النهائي للتخطيط")
        print("🌐 النظام متاح على:")
        print("   • الصفحة الرئيسية: http://localhost:5000")
        print("   • اختبار التخطيط: http://localhost:5000/layout-test")
        print("   • الإصلاح الطارئ: http://localhost:5000/emergency-fix")
        print("   • التشخيص: http://localhost:5000/debug-layout")
        print("   • اختبار الأرقام: http://localhost:5000/arabic-numbers-test")
        print("")
        print("👤 بيانات الدخول:")
        print("   • المستخدم: admin")
        print("   • كلمة المرور: admin123")
        print("")
        print("🎯 الميزات المتاحة:")
        print("   ✅ تخطيط RTL مثالي")
        print("   ✅ شريط جانبي متجاوب")
        print("   ✅ أرقام عربية")
        print("   ✅ إدارة السيارات والعملاء")
        print("   ✅ أرقام السيارات القطرية")
        print("   ✅ نظام العقود والتقارير")
        print("")
        print("🛑 اضغط Ctrl+C للإيقاف")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ: {str(e)}")
        print("\n💡 جرب:")
        print("   python final_fix.py")
        print("   python run_fixed.py")

if __name__ == '__main__':
    main()
