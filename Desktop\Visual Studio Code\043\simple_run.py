import os
import sys

# Change to the script directory
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# Set demo mode by default
os.environ['WHATSAPP_DEMO_MODE'] = 'true'

def print_banner():
    print("=" * 60)
    print("🚗 معرض بوخليفة للسيارات - نظام إدارة شامل")
    print("🎯 نظام قوالب الواتساب المحسن")
    print("=" * 60)
    print()

def print_urls():
    print("📱 روابط نظام الواتساب:")
    print("- الصفحة الرئيسية: http://localhost:5000")
    print("- قوالب الواتساب المحسنة: http://localhost:5000/whatsapp/templates_enhanced")
    print("- إرسال رسالة: http://localhost:5000/whatsapp/send_message")
    print("- إعدادات الواتساب: http://localhost:5000/whatsapp/settings_enhanced")
    print()

def print_login_info():
    print("🔐 معلومات تسجيل الدخول:")
    print("- اسم المستخدم: admin")
    print("- كلمة المرور: admin123")
    print()

def print_demo_mode_info():
    print("⚠️  الوضع التجريبي مفعل:")
    print("- يمكنك استخدام جميع القوالب والوظائف")
    print("- لن يتم إرسال رسائل فعلية للعملاء")
    print("- الرسائل ستظهر في سجل الخادم للمراجعة")
    print("- لتفعيل الإرسال الحقيقي، اذهب للإعدادات وأوقف الوضع التجريبي")
    print()

def print_features():
    print("✨ الميزات المتاحة:")
    print("- 6 قوالب جاهزة للواتساب")
    print("- نظام متغيرات ذكي")
    print("- معاينة فورية للرسائل")
    print("- إرسال سريع من صفحة العملاء")
    print("- إعدادات متقدمة للواتساب")
    print()

try:
    print_banner()

    print("🔄 جاري تحميل النظام...")
    from app import create_app

    print("✅ تم تحميل التطبيق بنجاح")
    app = create_app()

    print("🚀 جاري تشغيل الخادم...")
    print()

    print_urls()
    print_login_info()
    print_demo_mode_info()
    print_features()

    print("📖 للمساعدة، راجع ملف: WHATSAPP_QUICK_START.md")
    print()
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("📦 يرجى تثبيت المتطلبات: pip install flask flask-sqlalchemy flask-login")
    input("اضغط Enter للمتابعة...")
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
    input("اضغط Enter للمتابعة...")
