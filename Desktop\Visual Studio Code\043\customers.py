from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Customer, Sale
from werkzeug.utils import secure_filename
import os
from datetime import datetime, date

customers_bp = Blueprint('customers', __name__)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@customers_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Customer.query
    
    if search:
        query = query.filter(
            Customer.full_name.contains(search) |
            Customer.national_id.contains(search) |
            Customer.phone.contains(search) |
            Customer.email.contains(search)
        )
    
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    from datetime import date

    return render_template('customers/index.html',
                         customers=customers,
                         search=search,
                         current_date=date.today())

@customers_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if not current_user.has_permission('edit_customers'):
        flash('ليس لديك صلاحية لإضافة العملاء', 'error')
        return redirect(url_for('customers.index'))
    
    if request.method == 'POST':
        # Get form data
        full_name = request.form.get('full_name', '').strip()
        national_id = request.form.get('national_id', '').strip()
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip()
        address = request.form.get('address', '').strip()
        date_of_birth = request.form.get('date_of_birth')
        occupation = request.form.get('occupation', '').strip()
        monthly_income = request.form.get('monthly_income', type=float)
        whatsapp_number = request.form.get('whatsapp_number', '').strip()
        notes = request.form.get('notes', '').strip()
        
        # Guarantor data
        guarantor_name = request.form.get('guarantor_name', '').strip()
        guarantor_id = request.form.get('guarantor_id', '').strip()
        guarantor_phone = request.form.get('guarantor_phone', '').strip()
        guarantor_address = request.form.get('guarantor_address', '').strip()
        
        # Validation
        if not all([full_name, national_id, phone]):
            flash('الحقول المطلوبة: الاسم الكامل، رقم الهوية، رقم الهاتف', 'error')
            return render_template('customers/add.html')
        
        if len(national_id) != 10 or not national_id.isdigit():
            flash('رقم الهوية يجب أن يكون 10 أرقام', 'error')
            return render_template('customers/add.html')
        
        # Check if national ID already exists
        if Customer.query.filter_by(national_id=national_id).first():
            flash('رقم الهوية موجود بالفعل', 'error')
            return render_template('customers/add.html')
        
        # Parse date of birth
        dob = None
        if date_of_birth:
            try:
                dob = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ الميلاد غير صحيح', 'error')
                return render_template('customers/add.html')
        
        # Handle file uploads
        id_copy_path = None
        photo_path = None
        guarantor_id_copy_path = None
        
        # Customer ID copy
        id_copy_file = request.files.get('id_copy')
        if id_copy_file and id_copy_file.filename and allowed_file(id_copy_file.filename):
            filename = secure_filename(id_copy_file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = f"id_{timestamp}{filename}"
            
            upload_path = os.path.join('static/uploads/customers', filename)
            id_copy_file.save(upload_path)
            id_copy_path = f'uploads/customers/{filename}'
        
        # Customer photo
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename and allowed_file(photo_file.filename):
            filename = secure_filename(photo_file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = f"photo_{timestamp}{filename}"
            
            upload_path = os.path.join('static/uploads/customers', filename)
            photo_file.save(upload_path)
            photo_path = f'uploads/customers/{filename}'
        
        # Guarantor ID copy
        guarantor_id_copy_file = request.files.get('guarantor_id_copy')
        if guarantor_id_copy_file and guarantor_id_copy_file.filename and allowed_file(guarantor_id_copy_file.filename):
            filename = secure_filename(guarantor_id_copy_file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = f"guarantor_id_{timestamp}{filename}"
            
            upload_path = os.path.join('static/uploads/customers', filename)
            guarantor_id_copy_file.save(upload_path)
            guarantor_id_copy_path = f'uploads/customers/{filename}'
        
        # Create new customer
        customer = Customer(
            full_name=full_name,
            national_id=national_id,
            phone=phone,
            email=email or None,
            address=address,
            date_of_birth=dob,
            occupation=occupation,
            monthly_income=monthly_income,
            whatsapp_number=whatsapp_number,
            notes=notes,
            guarantor_name=guarantor_name or None,
            guarantor_id=guarantor_id or None,
            guarantor_phone=guarantor_phone or None,
            guarantor_address=guarantor_address or None,
            id_copy_path=id_copy_path,
            photo_path=photo_path,
            guarantor_id_copy_path=guarantor_id_copy_path,
            created_by=current_user.id
        )
        
        try:
            db.session.add(customer)
            db.session.commit()
            flash(f'تم إضافة العميل {full_name} بنجاح', 'success')
            return redirect(url_for('customers.view', customer_id=customer.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة العميل', 'error')
    
    return render_template('customers/add.html')

@customers_bp.route('/<int:customer_id>')
@login_required
def view(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    
    # Get customer's sales
    sales = Sale.query.filter_by(customer_id=customer_id).order_by(Sale.created_at.desc()).all()
    
    return render_template('customers/view.html', customer=customer, sales=sales)

@customers_bp.route('/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(customer_id):
    if not current_user.has_permission('edit_customers'):
        flash('ليس لديك صلاحية لتعديل العملاء', 'error')
        return redirect(url_for('customers.view', customer_id=customer_id))
    
    customer = Customer.query.get_or_404(customer_id)
    
    if request.method == 'POST':
        # Get form data
        customer.full_name = request.form.get('full_name', '').strip()
        national_id = request.form.get('national_id', '').strip()
        customer.phone = request.form.get('phone', '').strip()
        customer.email = request.form.get('email', '').strip() or None
        customer.address = request.form.get('address', '').strip()
        date_of_birth = request.form.get('date_of_birth')
        customer.occupation = request.form.get('occupation', '').strip()
        customer.monthly_income = request.form.get('monthly_income', type=float)
        customer.whatsapp_number = request.form.get('whatsapp_number', '').strip()
        customer.notes = request.form.get('notes', '').strip()
        
        # Guarantor data
        customer.guarantor_name = request.form.get('guarantor_name', '').strip() or None
        customer.guarantor_id = request.form.get('guarantor_id', '').strip() or None
        customer.guarantor_phone = request.form.get('guarantor_phone', '').strip() or None
        customer.guarantor_address = request.form.get('guarantor_address', '').strip() or None
        
        # Validation
        if not all([customer.full_name, national_id, customer.phone]):
            flash('الحقول المطلوبة: الاسم الكامل، رقم الهوية، رقم الهاتف', 'error')
            return render_template('customers/edit.html', customer=customer)
        
        if len(national_id) != 10 or not national_id.isdigit():
            flash('رقم الهوية يجب أن يكون 10 أرقام', 'error')
            return render_template('customers/edit.html', customer=customer)
        
        # Check if national ID already exists (excluding current customer)
        existing_customer = Customer.query.filter(
            Customer.national_id == national_id,
            Customer.id != customer_id
        ).first()
        if existing_customer:
            flash('رقم الهوية موجود بالفعل', 'error')
            return render_template('customers/edit.html', customer=customer)
        
        customer.national_id = national_id
        
        # Parse date of birth
        if date_of_birth:
            try:
                customer.date_of_birth = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ الميلاد غير صحيح', 'error')
                return render_template('customers/edit.html', customer=customer)
        else:
            customer.date_of_birth = None
        
        # Handle file uploads
        # Customer ID copy
        id_copy_file = request.files.get('id_copy')
        if id_copy_file and id_copy_file.filename and allowed_file(id_copy_file.filename):
            # Delete old file
            if customer.id_copy_path:
                old_path = os.path.join('static', customer.id_copy_path)
                if os.path.exists(old_path):
                    os.remove(old_path)
            
            filename = secure_filename(id_copy_file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = f"id_{timestamp}{filename}"
            
            upload_path = os.path.join('static/uploads/customers', filename)
            id_copy_file.save(upload_path)
            customer.id_copy_path = f'uploads/customers/{filename}'
        
        # Customer photo
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename and allowed_file(photo_file.filename):
            # Delete old file
            if customer.photo_path:
                old_path = os.path.join('static', customer.photo_path)
                if os.path.exists(old_path):
                    os.remove(old_path)
            
            filename = secure_filename(photo_file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = f"photo_{timestamp}{filename}"
            
            upload_path = os.path.join('static/uploads/customers', filename)
            photo_file.save(upload_path)
            customer.photo_path = f'uploads/customers/{filename}'
        
        # Guarantor ID copy
        guarantor_id_copy_file = request.files.get('guarantor_id_copy')
        if guarantor_id_copy_file and guarantor_id_copy_file.filename and allowed_file(guarantor_id_copy_file.filename):
            # Delete old file
            if customer.guarantor_id_copy_path:
                old_path = os.path.join('static', customer.guarantor_id_copy_path)
                if os.path.exists(old_path):
                    os.remove(old_path)
            
            filename = secure_filename(guarantor_id_copy_file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = f"guarantor_id_{timestamp}{filename}"
            
            upload_path = os.path.join('static/uploads/customers', filename)
            guarantor_id_copy_file.save(upload_path)
            customer.guarantor_id_copy_path = f'uploads/customers/{filename}'
        
        customer.updated_at = datetime.utcnow()
        
        try:
            db.session.commit()
            flash(f'تم تحديث بيانات العميل {customer.full_name} بنجاح', 'success')
            return redirect(url_for('customers.view', customer_id=customer.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث البيانات', 'error')
    
    return render_template('customers/edit.html', customer=customer)

@customers_bp.route('/<int:customer_id>/delete', methods=['POST'])
@login_required
def delete(customer_id):
    if not current_user.has_permission('delete_all'):
        flash('ليس لديك صلاحية لحذف العملاء', 'error')
        return redirect(url_for('customers.view', customer_id=customer_id))
    
    customer = Customer.query.get_or_404(customer_id)
    
    # Check if customer has sales
    if customer.sales:
        flash('لا يمكن حذف العميل لأنه مرتبط بعمليات بيع', 'error')
        return redirect(url_for('customers.view', customer_id=customer_id))
    
    try:
        # Delete customer files
        files_to_delete = [
            customer.id_copy_path,
            customer.photo_path,
            customer.guarantor_id_copy_path
        ]
        
        for file_path in files_to_delete:
            if file_path:
                full_path = os.path.join('static', file_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
        
        db.session.delete(customer)
        db.session.commit()
        flash(f'تم حذف العميل {customer.full_name} بنجاح', 'success')
        return redirect(url_for('customers.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف العميل', 'error')
        return redirect(url_for('customers.view', customer_id=customer_id))

@customers_bp.route('/api/search')
@login_required
def api_search():
    """API endpoint for customer search (for AJAX)"""
    query = request.args.get('q', '')
    
    customers = Customer.query.filter(
        Customer.full_name.contains(query) |
        Customer.national_id.contains(query) |
        Customer.phone.contains(query)
    ).limit(10).all()
    
    results = []
    for customer in customers:
        results.append({
            'id': customer.id,
            'full_name': customer.full_name,
            'national_id': customer.national_id,
            'phone': customer.phone,
            'display_name': f"{customer.full_name} - {customer.national_id}"
        })
    
    return jsonify(results)
