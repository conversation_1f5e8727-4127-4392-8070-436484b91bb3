{% extends "base.html" %}

{% block title %}إدارة العملاء - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة العملاء</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if current_user.has_permission('edit_customers') %}
        <a href="{{ url_for('customers.add') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            إضافة عميل جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search -->
<div class="row mb-4">
    <div class="col-md-8">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" value="{{ search }}" 
                   placeholder="البحث بالاسم أو رقم الهوية أو الهاتف..." aria-label="البحث">
            <button class="btn btn-outline-primary" type="submit">
                <i class="fas fa-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times"></i>
            </a>
            {% endif %}
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة العملاء</h5>
    </div>
    <div class="card-body">
        {% if customers.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم الكامل</th>
                        <th>رقم الهوية</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>تاريخ التسجيل</th>
                        <th>المبيعات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if customer.photo_path %}
                                <img src="{{ url_for('static', filename=customer.photo_path) }}" 
                                     class="rounded-circle me-2" width="40" height="40" 
                                     style="object-fit: cover;" alt="صورة العميل">
                                {% else %}
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ customer.full_name }}</strong>
                                    {% if customer.guarantor_name %}
                                    <br><small class="text-muted">
                                        <i class="fas fa-user-shield me-1"></i>
                                        ضامن: {{ customer.guarantor_name }}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>{{ customer.national_id }}</code>
                        </td>
                        <td>
                            <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                {{ customer.phone }}
                            </a>
                            {% if customer.whatsapp_number %}
                            <br><a href="https://wa.me/{{ customer.whatsapp_number.replace('+', '') }}" 
                                   target="_blank" class="text-success text-decoration-none">
                                <i class="fab fa-whatsapp me-1"></i>
                                واتساب
                            </a>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.email %}
                            <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                {{ customer.email }}
                            </a>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ customer.created_at.strftime('%Y/%m/%d') }}
                            <br><small class="text-muted">{{ customer.created_at.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            {% set sales_count = customer.sales|length %}
                            {% if sales_count > 0 %}
                            <span class="badge bg-success">{{ sales_count }} مبيعة</span>
                            {% else %}
                            <span class="badge bg-secondary">لا توجد مبيعات</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('customers.view', customer_id=customer.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                {% if current_user.has_permission('edit_customers') %}
                                <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                
                                {% if current_user.has_permission('create_sales') %}
                                <a href="{{ url_for('sales.add') }}?customer_id={{ customer.id }}" 
                                   class="btn btn-sm btn-outline-success" title="إنشاء مبيعة">
                                    <i class="fas fa-handshake"></i>
                                </a>
                                {% endif %}
                                
                                {% if customer.whatsapp_number or customer.phone %}
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle" 
                                            data-bs-toggle="dropdown" title="واتساب">
                                        <i class="fab fa-whatsapp"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item"
                                               href="{{ url_for('whatsapp.send_message') }}?customer_id={{ customer.id }}">
                                                <i class="fas fa-edit me-2"></i>
                                                رسالة مخصصة
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">قوالب سريعة</h6></li>
                                        <li>
                                            <a class="dropdown-item"
                                               href="{{ url_for('whatsapp.send_quick_message', customer_id=customer.id, template_type='welcome') }}">
                                                <i class="fas fa-handshake me-2 text-success"></i>
                                                رسالة ترحيب
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item"
                                               href="{{ url_for('whatsapp.send_quick_message', customer_id=customer.id, template_type='payment_reminder') }}">
                                                <i class="fas fa-money-bill-wave me-2 text-warning"></i>
                                                تذكير بالدفع
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item"
                                               href="{{ url_for('whatsapp.send_quick_message', customer_id=customer.id, template_type='follow_up') }}">
                                                <i class="fas fa-phone me-2 text-info"></i>
                                                متابعة
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item"
                                               href="https://wa.me/{{ (customer.whatsapp_number or customer.phone).replace('+', '') }}"
                                               target="_blank">
                                                <i class="fab fa-whatsapp me-2 text-success"></i>
                                                فتح واتساب مباشرة
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if customers.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if customers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('customers.index', page=customers.prev_num, search=search) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in customers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != customers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers.index', page=page_num, search=search) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if customers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('customers.index', page=customers.next_num, search=search) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5>لا يوجد عملاء</h5>
            {% if search %}
            <p class="text-muted">لم يتم العثور على عملاء يطابقون البحث "{{ search }}".</p>
            <a href="{{ url_for('customers.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-times me-2"></i>
                إلغاء البحث
            </a>
            {% else %}
            <p class="text-muted">لم يتم إضافة أي عملاء بعد.</p>
            {% if current_user.has_permission('edit_customers') %}
            <a href="{{ url_for('customers.add') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                إضافة أول عميل
            </a>
            {% endif %}
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي العملاء</h5>
                <h2 class="text-primary">{{ customers.total }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">عملاء لديهم مبيعات</h5>
                <h2 class="text-success">
                    {{ customers.items|selectattr('sales')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">عملاء جدد هذا الشهر</h5>
                <h2 class="text-info">
                    {% set this_month_count = 0 %}
                    {% for customer in customers.items %}
                        {% if customer.created_at and customer.created_at.month == current_date.month and customer.created_at.year == current_date.year %}
                            {% set this_month_count = this_month_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ this_month_count }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">عملاء لديهم ضامن</h5>
                <h2 class="text-warning">
                    {{ customers.items|selectattr('guarantor_name')|list|length }}
                </h2>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add tooltips
    $('[title]').tooltip();
    
    // Animate cards
    $('.card').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
    
    // Search with debounce
    let searchTimeout;
    $('input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        
        if (searchTerm.length >= 2) {
            searchTimeout = setTimeout(function() {
                // Auto-submit search form
                $('form').submit();
            }, 500);
        }
    });
});
</script>
{% endblock %}
