{% extends "base.html" %}

{% block title %}إدارة السيارات - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة السيارات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if current_user.has_permission('edit_cars') %}
        <a href="{{ url_for('cars.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة سيارة جديدة
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-12">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" name="search" value="{{ search }}" 
                       placeholder="البحث في السيارات..." aria-label="البحث">
            </div>
            <div class="col-md-2">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="available" {{ 'selected' if status == 'available' }}>متاحة</option>
                    <option value="sold" {{ 'selected' if status == 'sold' }}>مباعة</option>
                    <option value="reserved" {{ 'selected' if status == 'reserved' }}>محجوزة</option>
                    <option value="maintenance" {{ 'selected' if status == 'maintenance' }}>صيانة</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="brand" class="form-select">
                    <option value="">جميع الماركات</option>
                    {% for brand_name in brands %}
                    <option value="{{ brand_name }}" {{ 'selected' if brand == brand_name }}>{{ brand_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
            </div>
            <div class="col-md-2">
                <a href="{{ url_for('cars.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Cars Grid -->
{% if cars.items %}
<div class="row">
    {% for car in cars.items %}
    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-2">
        <div class="card h-100 car-card">
            <!-- Car Image -->
            <div class="position-relative">
                {% if car.images %}
                    {% set images = car.images|from_json %}
                    {% if images %}
                    <img src="{{ url_for('static', filename=images[0]) }}" class="card-img-top"
                         style="height: 120px; object-fit: cover;" alt="{{ car.name }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 120px;">
                        <i class="fas fa-car fa-lg text-muted"></i>
                    </div>
                    {% endif %}
                {% else %}
                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 120px;">
                    <i class="fas fa-car fa-lg text-muted"></i>
                </div>
                {% endif %}
                
                <!-- Status Badge -->
                <span class="position-absolute top-0 start-0 m-1 badge badge-xs
                    {% if car.status == 'available' %}bg-success
                    {% elif car.status == 'sold' %}bg-danger
                    {% elif car.status == 'reserved' %}bg-warning text-dark
                    {% else %}bg-secondary{% endif %}">
                    {% if car.status == 'available' %}متاحة
                    {% elif car.status == 'sold' %}مباعة
                    {% elif car.status == 'reserved' %}محجوزة
                    {% else %}صيانة{% endif %}
                </span>

                <!-- Car Type Badge -->
                {% if car.car_type %}
                <span class="position-absolute top-0 end-0 m-1 badge badge-xs bg-{{ 'success' if car.car_type == 'new' else 'primary' }}">
                    {{ 'جديدة' if car.car_type == 'new' else 'مستعملة' }}
                </span>
                {% endif %}
            </div>
            
            <div class="card-body p-1">
                <h6 class="card-title mb-1 fs-7">{{ car.brand }} {{ car.model }}</h6>
                <p class="card-text mb-1">
                    <small class="fw-bold">{{ car.name }}</small><br>
                    <small class="text-muted">{{ car.year }} - {{ car.color or 'غير محدد' }}</small>
                </p>

                <div class="text-center mb-1">
                    <small class="text-success fw-bold d-block">{{ car.price|currency }}</small>
                    <small class="text-muted">{{ car.mileage|number_format if car.mileage else 'غير محدد' }} كم</small>
                </div>

                {% if car.condition_rating %}
                <div class="text-center mb-1">
                    <span class="badge badge-xs bg-{% if car.condition_rating == 'excellent' %}success{% elif car.condition_rating == 'very_good' %}info{% elif car.condition_rating == 'good' %}primary{% elif car.condition_rating == 'fair' %}warning{% else %}danger{% endif %}">
                        {% if car.condition_rating == 'excellent' %}ممتاز
                        {% elif car.condition_rating == 'very_good' %}جيد جداً
                        {% elif car.condition_rating == 'good' %}جيد
                        {% elif car.condition_rating == 'fair' %}مقبول
                        {% elif car.condition_rating == 'poor' %}ضعيف
                        {% else %}{{ car.condition_rating }}{% endif %}
                    </span>
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer bg-transparent p-1">
                <div class="btn-group w-100" role="group">
                    <a href="{{ url_for('cars.view', car_id=car.id) }}"
                       class="btn btn-outline-primary btn-xs" title="عرض">
                        <i class="fas fa-eye"></i>
                    </a>

                    {% if current_user.has_permission('edit_cars') %}
                    <a href="{{ url_for('cars.edit', car_id=car.id) }}"
                       class="btn btn-outline-warning btn-xs" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    {% endif %}

                    {% if car.status == 'available' and current_user.has_permission('create_sales') %}
                    <a href="{{ url_for('sales.add') }}?car_id={{ car.id }}"
                       class="btn btn-outline-success btn-xs" title="بيع">
                        <i class="fas fa-handshake"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if cars.pages > 1 %}
<nav aria-label="تنقل الصفحات">
    <ul class="pagination justify-content-center">
        {% if cars.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('cars.index', page=cars.prev_num, search=search, status=status, brand=brand) }}">السابق</a>
            </li>
        {% endif %}
        
        {% for page_num in cars.iter_pages() %}
            {% if page_num %}
                {% if page_num != cars.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('cars.index', page=page_num, search=search, status=status, brand=brand) }}">{{ page_num }}</a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if cars.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('cars.index', page=cars.next_num, search=search, status=status, brand=brand) }}">التالي</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-car fa-3x text-muted mb-3"></i>
    <h5>لا توجد سيارات</h5>
    {% if search or status or brand %}
    <p class="text-muted">لم يتم العثور على سيارات تطابق معايير البحث.</p>
    <a href="{{ url_for('cars.index') }}" class="btn btn-outline-primary">
        <i class="fas fa-times me-2"></i>
        إلغاء البحث
    </a>
    {% else %}
    <p class="text-muted">لم يتم إضافة أي سيارات بعد.</p>
    {% if current_user.has_permission('edit_cars') %}
    <a href="{{ url_for('cars.add') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة أول سيارة
    </a>
    {% endif %}
    {% endif %}
</div>
{% endif %}

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي السيارات</h5>
                <h2 class="text-primary">{{ cars.total }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">متاحة للبيع</h5>
                <h2 class="text-success">
                    {{ cars.items|selectattr('status', 'equalto', 'available')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">مباعة</h5>
                <h2 class="text-danger">
                    {{ cars.items|selectattr('status', 'equalto', 'sold')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">قيمة المخزون</h5>
                <h2 class="text-info">
                    {{ (cars.items|selectattr('status', 'equalto', 'available')|sum(attribute='price'))|currency }}
                </h2>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.car-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    font-size: 0.9rem;
}

.car-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
}

.card-img-top {
    transition: transform 0.3s ease;
}

.car-card:hover .card-img-top {
    transform: scale(1.03);
}

.badge-sm {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

.badge-xs {
    font-size: 0.6rem;
    padding: 0.2em 0.4em;
}

.car-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
}

.car-card .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.car-card .btn-xs {
    padding: 0.15rem 0.3rem;
    font-size: 0.7rem;
}

.fs-7 {
    font-size: 0.85rem;
}

/* Ultra compact mode for very large screens */
@media (min-width: 1600px) {
    .col-xl-2 {
        flex: 0 0 auto;
        width: 16.66666667%; /* 6 columns per row */
    }
}

@media (min-width: 1920px) {
    .col-xl-2 {
        width: 14.28571429%; /* 7 columns per row */
    }
}

/* Extra compact mode for large screens */
@media (min-width: 1400px) {
    .car-card {
        font-size: 0.8rem;
    }

    .car-card .card-title {
        font-size: 0.8rem;
    }

    .car-card .card-body {
        padding: 0.5rem !important;
    }

    .card-img-top {
        height: 100px !important;
    }
}

@media (max-width: 576px) {
    .car-card {
        font-size: 0.8rem;
    }

    .car-card .card-title {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add animation to cards
    $('.car-card').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
    
    // Add tooltips
    $('[title]').tooltip();
});
</script>
{% endblock %}
