#!/usr/bin/env python3
"""
تشغيل طارئ لحل مشاكل التخطيط
Emergency start to fix layout issues
"""

import os
import sys

def main():
    """Emergency start with layout fixes"""
    print("🚨 التشغيل الطارئ - إصلاح مشاكل التخطيط")
    print("=" * 50)
    
    # Set emergency environment variables
    os.environ['EMERGENCY_MODE'] = 'true'
    os.environ['FORCE_RTL'] = 'true'
    os.environ['DISABLE_CACHE'] = 'true'
    
    try:
        from app import create_app
        app = create_app()
        
        print("✅ تم إنشاء التطبيق في الوضع الطارئ")
        print("🔧 تم تفعيل إصلاحات التخطيط")
        print("🌐 الروابط المتاحة:")
        print("   • الصفحة الرئيسية: http://localhost:5000")
        print("   • الإصلاح الطارئ: http://localhost:5000/emergency-fix")
        print("   • التشخيص: http://localhost:5000/debug-layout")
        print("")
        print("👤 بيانات الدخول:")
        print("   • المستخدم: admin")
        print("   • كلمة المرور: admin123")
        print("")
        print("🛑 اضغط Ctrl+C للإيقاف")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ: {str(e)}")
        print("\n💡 جرب الوصول مباشرة إلى:")
        print("   http://localhost:5000/emergency-fix")

if __name__ == '__main__':
    main()
