{% extends "base.html" %}

{% block title %}إدارة العقود - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة العقود</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-8">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" value="{{ search }}" 
                   placeholder="البحث في العقود..." aria-label="البحث">
            <button class="btn btn-outline-primary" type="submit">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
</div>

<!-- Contracts Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة العقود</h5>
    </div>
    <div class="card-body">
        {% if sales.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم العملية</th>
                        <th>العميل</th>
                        <th>السيارة</th>
                        <th>نوع البيع</th>
                        <th>السعر</th>
                        <th>تاريخ البيع</th>
                        <th>حالة العقد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales.items %}
                    <tr>
                        <td>
                            <a href="{{ url_for('sales.view', sale_id=sale.id) }}" class="text-decoration-none">
                                #{{ sale.id }}
                            </a>
                        </td>
                        <td>
                            <a href="{{ url_for('customers.view', customer_id=sale.customer.id) }}" class="text-decoration-none">
                                {{ sale.customer.full_name }}
                            </a>
                            <br>
                            <small class="text-muted">{{ sale.customer.national_id }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('cars.view', car_id=sale.car.id) }}" class="text-decoration-none">
                                {{ sale.car.brand }} {{ sale.car.model }}
                            </a>
                            <br>
                            <small class="text-muted">{{ sale.car.year }}</small>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                            </span>
                        </td>
                        <td>{{ sale.sale_price|currency }}</td>
                        <td>{{ sale.sale_date|arabic_date }}</td>
                        <td>
                            {% if sale.contract_path %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    تم إنشاؤه
                                </span>
                            {% else %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock"></i>
                                    لم ينشأ بعد
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                {% if sale.contract_path %}
                                    <a href="{{ url_for('contracts.download_contract', sale_id=sale.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="تحميل العقد">
                                        <i class="fas fa-download"></i>
                                    </a>
                                {% endif %}
                                
                                <a href="{{ url_for('contracts.preview_contract', sale_id=sale.id) }}" 
                                   class="btn btn-sm btn-outline-info" title="معاينة العقد">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" 
                                            data-bs-toggle="dropdown" title="إنشاء عقد">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('contracts.generate_pdf', sale_id=sale.id) }}">
                                                <i class="fas fa-file-pdf me-2"></i>
                                                عقد PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('contracts.generate_word', sale_id=sale.id) }}">
                                                <i class="fas fa-file-word me-2"></i>
                                                عقد Word
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                
                                {% if sale.customer.whatsapp_number or sale.customer.phone %}
                                <a href="{{ url_for('whatsapp.send_contract_notification', sale_id=sale.id) }}" 
                                   class="btn btn-sm btn-outline-success" title="إرسال إشعار واتساب">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if sales.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if sales.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('contracts.index', page=sales.prev_num, search=search) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in sales.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != sales.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('contracts.index', page=page_num, search=search) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if sales.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('contracts.index', page=sales.next_num, search=search) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
            <h5>لا توجد عقود</h5>
            <p class="text-muted">لم يتم العثور على أي عقود.</p>
            {% if search %}
            <a href="{{ url_for('contracts.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-times me-2"></i>
                إلغاء البحث
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي العقود</h5>
                <h2 class="text-primary">{{ sales.total }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">عقود مكتملة</h5>
                <h2 class="text-success">
                    {{ sales.items|selectattr('contract_path')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">عقود معلقة</h5>
                <h2 class="text-warning">
                    {{ sales.items|rejectattr('contract_path')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">هذا الشهر</h5>
                <h2 class="text-info">
                    {{ sales.items|selectattr('sale_date')|list|length }}
                </h2>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add tooltips
    $('[title]').tooltip();
    
    // Confirm contract generation
    $('.dropdown-item').on('click', function(e) {
        const action = $(this).text().trim();
        if (!confirm(`هل تريد إنشاء ${action}؟`)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
