{% extends "base.html" %}

{% block title %}إدارة المبيعات - {{ company_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة المبيعات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if current_user.has_permission('create_sales') %}
        <a href="{{ url_for('sales.add') }}" class="btn btn-primary">
            <i class="fas fa-handshake me-2"></i>
            عملية بيع جديدة
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" value="{{ search }}" 
                   placeholder="البحث بالعميل أو السيارة..." aria-label="البحث">
            <button class="btn btn-outline-primary" type="submit">
                <i class="fas fa-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('sales.index') }}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times"></i>
            </a>
            {% endif %}
        </form>
    </div>
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <select name="status" class="form-select me-2" onchange="this.form.submit()">
                <option value="">جميع الحالات</option>
                <option value="active" {{ 'selected' if status == 'active' }}>نشط</option>
                <option value="completed" {{ 'selected' if status == 'completed' }}>مكتمل</option>
                <option value="cancelled" {{ 'selected' if status == 'cancelled' }}>ملغي</option>
            </select>
            <select name="sale_type" class="form-select" onchange="this.form.submit()">
                <option value="">جميع الأنواع</option>
                <option value="cash" {{ 'selected' if sale_type == 'cash' }}>نقدي</option>
                <option value="installment" {{ 'selected' if sale_type == 'installment' }}>تقسيط</option>
            </select>
        </form>
    </div>
</div>

<!-- Sales Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المبيعات</h5>
    </div>
    <div class="card-body">
        {% if sales.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم البيع</th>
                        <th>العميل</th>
                        <th>السيارة</th>
                        <th>نوع البيع</th>
                        <th>السعر</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>تاريخ البيع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales.items %}
                    <tr>
                        <td>
                            <strong>#{{ sale.id }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if sale.customer.photo_path %}
                                <img src="{{ url_for('static', filename=sale.customer.photo_path) }}" 
                                     class="rounded-circle me-2" width="30" height="30" 
                                     style="object-fit: cover;" alt="صورة العميل">
                                {% else %}
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" 
                                     style="width: 30px; height: 30px; font-size: 0.7rem;">
                                    {{ sale.customer.full_name[:2].upper() }}
                                </div>
                                {% endif %}
                                <div>
                                    <a href="{{ url_for('customers.view', customer_id=sale.customer.id) }}" 
                                       class="text-decoration-none">
                                        {{ sale.customer.full_name }}
                                    </a>
                                    <br><small class="text-muted">{{ sale.customer.phone }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="{{ url_for('cars.view', car_id=sale.car.id) }}" class="text-decoration-none">
                                {{ sale.car.brand }} {{ sale.car.model }}
                                <br><small class="text-muted">{{ sale.car.year }}</small>
                            </a>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if sale.sale_type == 'cash' else 'info' }}">
                                {{ 'نقدي' if sale.sale_type == 'cash' else 'تقسيط' }}
                            </span>
                        </td>
                        <td>{{ sale.sale_price|currency }}</td>
                        <td>
                            {% set total_paid = (sale.payments|sum(attribute='amount') or 0) + (sale.down_payment or 0) %}
                            {{ total_paid|currency }}
                        </td>
                        <td>
                            {% set remaining = sale.sale_price - total_paid %}
                            <span class="text-{{ 'success' if remaining <= 0 else 'warning' }}">
                                {{ remaining|currency }}
                            </span>
                        </td>
                        <td>
                            {{ sale.sale_date.strftime('%Y/%m/%d') }}
                            <br><small class="text-muted">{{ sale.sale_date.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <span class="badge bg-{% if sale.status == 'active' %}success{% elif sale.status == 'completed' %}primary{% else %}danger{% endif %}">
                                {% if sale.status == 'active' %}نشط
                                {% elif sale.status == 'completed' %}مكتمل
                                {% elif sale.status == 'cancelled' %}ملغي
                                {% else %}{{ sale.status }}{% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('sales.view', sale_id=sale.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                {% if current_user.has_permission('edit_sales') %}
                                <button type="button" class="btn btn-sm btn-outline-warning" title="تعديل" disabled>
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% endif %}
                                
                                {% if sale.sale_type == 'installment' %}
                                <a href="{{ url_for('sales.installments', sale_id=sale.id) }}" 
                                   class="btn btn-sm btn-outline-info" title="الأقساط">
                                    <i class="fas fa-calendar-alt"></i>
                                </a>
                                {% endif %}
                                
                                <a href="{{ url_for('contracts.preview_contract', sale_id=sale.id) }}" 
                                   class="btn btn-sm btn-outline-success" title="العقد">
                                    <i class="fas fa-file-contract"></i>
                                </a>
                                
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown" title="المزيد">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% if sale.customer.whatsapp_number or sale.customer.phone %}
                                        <li>
                                            <a class="dropdown-item" 
                                               href="{{ url_for('whatsapp.send_message') }}?customer_id={{ sale.customer.id }}">
                                                <i class="fab fa-whatsapp me-2"></i>
                                                إرسال رسالة
                                            </a>
                                        </li>
                                        {% endif %}
                                        <li>
                                            <button class="dropdown-item" disabled>
                                                <i class="fas fa-receipt me-2"></i>
                                                طباعة إيصال
                                            </button>
                                        </li>
                                        {% if sale.status == 'active' and current_user.has_permission('edit_sales') %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger" disabled>
                                                <i class="fas fa-times me-2"></i>
                                                إلغاء المبيعة
                                            </button>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if sales.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if sales.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('sales.index', page=sales.prev_num, search=search, status=status, sale_type=sale_type) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in sales.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != sales.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('sales.index', page=page_num, search=search, status=status, sale_type=sale_type) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if sales.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('sales.index', page=sales.next_num, search=search, status=status, sale_type=sale_type) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
            <h5>لا توجد مبيعات</h5>
            {% if search or status or sale_type %}
            <p class="text-muted">لم يتم العثور على مبيعات تطابق المعايير المحددة.</p>
            <a href="{{ url_for('sales.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-times me-2"></i>
                إلغاء الفلترة
            </a>
            {% else %}
            <p class="text-muted">لم يتم إجراء أي مبيعات بعد.</p>
            {% if current_user.has_permission('create_sales') %}
            <a href="{{ url_for('sales.add') }}" class="btn btn-primary">
                <i class="fas fa-handshake me-2"></i>
                إجراء أول مبيعة
            </a>
            {% endif %}
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي المبيعات</h5>
                <h2 class="text-primary">{{ sales.total }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">المبيعات النقدية</h5>
                <h2 class="text-success">
                    {{ sales.items|selectattr('sale_type', 'equalto', 'cash')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">مبيعات التقسيط</h5>
                <h2 class="text-info">
                    {{ sales.items|selectattr('sale_type', 'equalto', 'installment')|list|length }}
                </h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">إجمالي القيمة</h5>
                <h2 class="text-warning">
                    {{ sales.items|sum(attribute='sale_price')|currency }}
                </h2>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add tooltips
    $('[title]').tooltip();
    
    // Animate cards
    $('.card').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
    
    // Search with debounce
    let searchTimeout;
    $('input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        
        if (searchTerm.length >= 2) {
            searchTimeout = setTimeout(function() {
                // Auto-submit search form
                $('form').first().submit();
            }, 500);
        }
    });
});
</script>
{% endblock %}
