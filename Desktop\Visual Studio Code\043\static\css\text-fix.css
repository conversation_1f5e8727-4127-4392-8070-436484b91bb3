/* TEXT AND FONT FIX - حل مشكلة النصوص والخطوط */

/* Force text to be visible and readable */
* {
    font-size: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
}

html {
    font-size: 16px !important;
    line-height: 1.5 !important;
}

body {
    font-family: 'Segoe <PERSON>I', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #212529 !important;
    font-weight: 400 !important;
}

/* Force all text elements to be visible */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    margin-bottom: 0.5rem !important;
    color: inherit !important;
}

h1 { font-size: 2.5rem !important; }
h2 { font-size: 2rem !important; }
h3 { font-size: 1.75rem !important; }
h4 { font-size: 1.5rem !important; }
h5 { font-size: 1.25rem !important; }
h6 { font-size: 1rem !important; }

p {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
    color: inherit !important;
}

/* Sidebar text fixes */
.sidebar {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: white !important;
}

.sidebar h4 {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: white !important;
    margin-bottom: 0.5rem !important;
}

.sidebar small {
    font-size: 0.875rem !important;
    color: rgba(255,255,255,0.8) !important;
}

.sidebar .nav-link {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    color: rgba(255,255,255,0.8) !important;
    text-decoration: none !important;
    display: block !important;
    padding: 12px 20px !important;
    margin: 2px 10px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.sidebar .nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1) !important;
}

.sidebar .nav-link i {
    font-size: 1rem !important;
    width: 20px !important;
    text-align: center !important;
    margin-left: 10px !important;
    color: inherit !important;
}

/* Card text fixes */
.card-header {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: white !important;
}

.card-header h3,
.card-header h4,
.card-header h5 {
    color: white !important;
    font-weight: 600 !important;
    margin-bottom: 0 !important;
}

.card-body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

/* Button text fixes */
.btn {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 8px !important;
}

.btn i {
    font-size: 1rem !important;
    margin-left: 8px !important;
}

/* Form text fixes */
.form-label {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: #212529 !important;
    margin-bottom: 0.5rem !important;
}

.form-control,
.form-select {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    color: #212529 !important;
}

/* Table text fixes */
.table {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    color: #212529 !important;
}

.table th {
    font-weight: 600 !important;
    font-size: 1rem !important;
    color: #495057 !important;
}

.table td {
    font-size: 1rem !important;
    color: #212529 !important;
}

/* Alert text fixes */
.alert {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
}

/* Badge text fixes */
.badge {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

/* Navigation text fixes */
.navbar-brand {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
}

/* Utility text classes */
.text-white {
    color: white !important;
}

.text-dark {
    color: #212529 !important;
}

.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* Font weight utilities */
.fw-bold {
    font-weight: 700 !important;
}

.fw-normal {
    font-weight: 400 !important;
}

.fw-light {
    font-weight: 300 !important;
}

/* Font size utilities */
.fs-1 { font-size: 2.5rem !important; }
.fs-2 { font-size: 2rem !important; }
.fs-3 { font-size: 1.75rem !important; }
.fs-4 { font-size: 1.5rem !important; }
.fs-5 { font-size: 1.25rem !important; }
.fs-6 { font-size: 1rem !important; }

/* Small text */
small,
.small {
    font-size: 0.875rem !important;
}

/* Large text */
.lead {
    font-size: 1.25rem !important;
    font-weight: 300 !important;
}

/* Display headings */
.display-1 { font-size: 6rem !important; }
.display-2 { font-size: 5.5rem !important; }
.display-3 { font-size: 4.5rem !important; }
.display-4 { font-size: 3.5rem !important; }
.display-5 { font-size: 3rem !important; }
.display-6 { font-size: 2.5rem !important; }

/* Force visibility for all text */
* {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure icons are visible */
i,
.fa,
.fas,
.far,
.fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
    font-size: inherit !important;
    color: inherit !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force text to be selectable and visible */
* {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    -webkit-text-size-adjust: 100% !important;
    -ms-text-size-adjust: 100% !important;
}

/* Ensure proper text rendering */
* {
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* Fix any potential text hiding */
* {
    text-indent: 0 !important;
    letter-spacing: normal !important;
    word-spacing: normal !important;
    white-space: normal !important;
}

/* Override any potential hiding styles */
* {
    display: inherit !important;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

/* Ensure text contrast */
.sidebar * {
    color: white !important;
}

.card-header * {
    color: white !important;
}

.main-content * {
    color: #212529 !important;
}

.text-white,
.text-white * {
    color: white !important;
}
