{% extends "base.html" %}

{% block title %}إضافة رقم لوحة جديد{% endblock %}

{% block extra_css %}
<style>
.plate-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 2rem;
    margin-bottom: 20px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.plate-preview.vip {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.plate-preview.special {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.plate-preview.regular {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.features-display {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.feature-badge {
    display: inline-block;
    margin: 2px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.auction-section {
    background: #e8f5e8;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    margin-top: 15px;
    display: none;
}

.price-suggestion {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
}

.form-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.section-title {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Form -->
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-plus-circle me-2"></i>إضافة رقم لوحة جديد</h2>
                    <p class="text-muted">أضف رقم لوحة سيارة جديد للبيع أو المزايدة</p>
                </div>
                <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>

            <form method="POST" enctype="multipart/form-data" id="plateForm">
                <!-- Basic Information -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="number" class="form-label">رقم اللوحة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="number" name="number" required 
                                       placeholder="مثال: 123456 أو A-12345">
                                <div class="form-text">أدخل رقم اللوحة كما هو مكتوب</div>
                                <div id="numberStatus" class="mt-2"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="vip">VIP - أرقام مميزة جداً</option>
                                    <option value="special">مميزة - أرقام خاصة</option>
                                    <option value="regular">عادية - أرقام عادية</option>
                                    <option value="custom">مخصصة - أرقام مخصصة</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="type" class="form-label">النوع <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="private">خاصة</option>
                                    <option value="taxi">تاكسي</option>
                                    <option value="transport">نقل</option>
                                    <option value="government">حكومية</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="code_letter" class="form-label">الحرف</label>
                                <input type="text" class="form-control" id="code_letter" name="code_letter" 
                                       placeholder="A, B, C..." maxlength="5">
                                <div class="form-text">الحرف المرافق للرقم (اختياري)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="series" class="form-label">السلسلة</label>
                                <input type="text" class="form-control" id="series" name="series" 
                                       placeholder="مثال: 2024">
                                <div class="form-text">سلسلة الإصدار (اختياري)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="2"
                                          placeholder="وصف مختصر عن رقم اللوحة..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Special Features -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-star me-2"></i>المميزات الخاصة</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_sequential" name="is_sequential">
                                <label class="form-check-label" for="is_sequential">
                                    <strong>أرقام متسلسلة</strong> - مثل 1234، 5678
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_repeated" name="is_repeated">
                                <label class="form-check-label" for="is_repeated">
                                    <strong>أرقام مكررة</strong> - مثل 1111، 7777
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_mirror" name="is_mirror">
                                <label class="form-check-label" for="is_mirror">
                                    <strong>أرقام مرآة</strong> - مثل 1221، 3443
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_birthday" name="is_birthday">
                                <label class="form-check-label" for="is_birthday">
                                    <strong>تاريخ ميلاد</strong> - أرقام تمثل تواريخ
                                </label>
                            </div>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="is_lucky" name="is_lucky">
                                <label class="form-check-label" for="is_lucky">
                                    <strong>أرقام محظوظة</strong> - أرقام تجلب الحظ
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="featuresDisplay" class="features-display" style="display: none;">
                        <strong>المميزات المحددة:</strong>
                        <div id="selectedFeatures"></div>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-money-bill-wave me-2"></i>التسعير</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" required 
                                           min="0" step="100" placeholder="0">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div id="priceSuggestion" class="price-suggestion" style="display: none;">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>اقتراح السعر:</strong> <span id="suggestedPrice"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                           min="0" step="100" placeholder="0">
                                    <span class="input-group-text">ريال قطري</span>
                                </div>
                                <div class="form-text">اختياري - للتقارير المالية</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auction Settings -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-gavel me-2"></i>إعدادات المزايدة</h5>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_auction" name="is_auction">
                        <label class="form-check-label" for="is_auction">
                            <strong>عرض للمزايدة</strong> - سيتم بيع هذا الرقم عن طريق المزايدة
                        </label>
                    </div>
                    
                    <div id="auctionSection" class="auction-section">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="starting_bid" class="form-label">سعر البداية</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="starting_bid" name="starting_bid" 
                                               min="0" step="100" placeholder="0">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="reserve_price" class="form-label">السعر المحجوز</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="reserve_price" name="reserve_price" 
                                               min="0" step="100" placeholder="0">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    <div class="form-text">الحد الأدنى للبيع</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="auction_start_date" class="form-label">تاريخ بداية المزايدة</label>
                                    <input type="datetime-local" class="form-control" id="auction_start_date" name="auction_start_date">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="auction_end_date" class="form-label">تاريخ نهاية المزايدة</label>
                                    <input type="datetime-local" class="form-control" id="auction_end_date" name="auction_end_date">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images -->
                <div class="form-section">
                    <h5 class="section-title"><i class="fas fa-images me-2"></i>الصور</h5>
                    
                    <div class="mb-3">
                        <label for="images" class="form-label">صور رقم اللوحة</label>
                        <input type="file" class="form-control" id="images" name="images" multiple accept="image/*">
                        <div class="form-text">يمكنك اختيار عدة صور (PNG, JPG, JPEG, GIF)</div>
                        <div id="imagePreview" class="mt-3 row"></div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>حفظ رقم اللوحة
                        </button>
                        <button type="button" class="btn btn-outline-info ms-2" onclick="fillSampleData()">
                            <i class="fas fa-magic me-2"></i>بيانات تجريبية
                        </button>
                    </div>
                    <div>
                        <button type="reset" class="btn btn-outline-warning me-2">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 20px;">
                <!-- Plate Preview -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة اللوحة</h5>
                    </div>
                    <div class="card-body">
                        <div id="platePreview" class="plate-preview">
                            أدخل رقم اللوحة
                        </div>
                        <div id="analysisResults"></div>
                    </div>
                </div>

                <!-- Help Panel -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>نصائح مهمة</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>إرشادات</h6>
                            <ul class="mb-0 small">
                                <li>تأكد من صحة رقم اللوحة قبل الحفظ</li>
                                <li>اختر الفئة المناسبة لتحديد السعر</li>
                                <li>استخدم المميزات الخاصة لزيادة القيمة</li>
                                <li>أضف صور واضحة للوحة</li>
                                <li>حدد إعدادات المزايدة بعناية</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h6>
                            <p class="mb-0 small">الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة ولا يمكن تركها فارغة.</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('plate_numbers.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-2"></i>عرض جميع الأرقام
                            </a>
                            <a href="{{ url_for('plate_numbers.auctions') }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-gavel me-2"></i>المزايدات النشطة
                            </a>
                            <a href="{{ url_for('plate_numbers.search') }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-search me-2"></i>البحث المتقدم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Real-time plate number analysis
    $('#number').on('input', function() {
        const number = $(this).val().trim();
        updatePlatePreview(number);
        
        if (number.length >= 2) {
            checkNumberExists(number);
            analyzeNumber(number);
        }
    });
    
    // Category change
    $('#category').on('change', function() {
        updatePlatePreview($('#number').val());
        suggestPrice();
    });
    
    // Feature checkboxes
    $('input[type="checkbox"][name^="is_"]').on('change', function() {
        updateFeaturesDisplay();
        suggestPrice();
    });
    
    // Auction toggle
    $('#is_auction').on('change', function() {
        if ($(this).is(':checked')) {
            $('#auctionSection').slideDown();
            $('#price').prop('disabled', true);
        } else {
            $('#auctionSection').slideUp();
            $('#price').prop('disabled', false);
        }
    });
    
    // Image preview
    $('#images').on('change', function() {
        previewImages(this.files);
    });
});

function updatePlatePreview(number) {
    const preview = $('#platePreview');
    const category = $('#category').val() || 'regular';
    
    preview.removeClass('vip special regular custom')
           .addClass(category)
           .text(number || 'أدخل رقم اللوحة');
}

function checkNumberExists(number) {
    $.get('/plate-numbers/api/check-number', { number: number })
        .done(function(data) {
            const status = $('#numberStatus');
            if (data.exists) {
                status.html('<div class="alert alert-danger alert-sm">⚠️ هذا الرقم موجود مسبقاً</div>');
            } else {
                status.html('<div class="alert alert-success alert-sm">✅ الرقم متاح</div>');
            }
        });
}

function analyzeNumber(number) {
    $.get('/plate-numbers/api/analyze-number', { number: number })
        .done(function(data) {
            const features = data.features;
            const results = $('#analysisResults');
            
            let html = '<div class="mt-3"><strong>تحليل الرقم:</strong><br>';
            html += `<span class="badge bg-info">عدد الأرقام: ${features.digits_count}</span> `;
            
            if (features.is_sequential) html += '<span class="badge bg-success">متسلسل</span> ';
            if (features.is_repeated) html += '<span class="badge bg-warning">مكرر</span> ';
            if (features.is_mirror) html += '<span class="badge bg-info">مرآة</span> ';
            
            html += `<br><small class="text-muted">الفئة المقترحة: ${getCategoryName(features.suggested_category)}</small>`;
            html += '</div>';
            
            results.html(html);
            
            // Auto-select suggested category
            if ($('#category').val() === '') {
                $('#category').val(features.suggested_category);
                updatePlatePreview(number);
            }
            
            // Auto-check features
            $('#is_sequential').prop('checked', features.is_sequential);
            $('#is_repeated').prop('checked', features.is_repeated);
            $('#is_mirror').prop('checked', features.is_mirror);
            
            updateFeaturesDisplay();
            suggestPrice();
        });
}

function getCategoryName(category) {
    const names = {
        'vip': 'VIP',
        'special': 'مميزة',
        'regular': 'عادية',
        'custom': 'مخصصة'
    };
    return names[category] || category;
}

function updateFeaturesDisplay() {
    const features = [];
    
    if ($('#is_sequential').is(':checked')) features.push('<span class="feature-badge bg-success text-white">متسلسل</span>');
    if ($('#is_repeated').is(':checked')) features.push('<span class="feature-badge bg-warning text-dark">مكرر</span>');
    if ($('#is_mirror').is(':checked')) features.push('<span class="feature-badge bg-info text-white">مرآة</span>');
    if ($('#is_birthday').is(':checked')) features.push('<span class="feature-badge bg-primary text-white">تاريخ ميلاد</span>');
    if ($('#is_lucky').is(':checked')) features.push('<span class="feature-badge bg-danger text-white">محظوظ</span>');
    
    if (features.length > 0) {
        $('#selectedFeatures').html(features.join(' '));
        $('#featuresDisplay').show();
    } else {
        $('#featuresDisplay').hide();
    }
}

function suggestPrice() {
    const category = $('#category').val();
    const digits = $('#number').val().replace(/\D/g, '').length;
    const hasSpecialFeatures = $('input[type="checkbox"][name^="is_"]:checked').length > 0;
    
    let basePrice = 1000;
    
    // Category multiplier
    const multipliers = {
        'vip': 10,
        'special': 5,
        'regular': 1,
        'custom': 3
    };
    
    basePrice *= (multipliers[category] || 1);
    
    // Digits count factor
    if (digits <= 3) basePrice *= 5;
    else if (digits <= 4) basePrice *= 3;
    else if (digits <= 5) basePrice *= 2;
    
    // Special features bonus
    if (hasSpecialFeatures) basePrice *= 2;
    
    if (basePrice > 1000) {
        $('#suggestedPrice').text(basePrice.toLocaleString() + ' ريال');
        $('#priceSuggestion').show();
        
        if ($('#price').val() === '' || $('#price').val() == 0) {
            $('#price').val(basePrice);
        }
    } else {
        $('#priceSuggestion').hide();
    }
}

function previewImages(files) {
    const preview = $('#imagePreview');
    preview.empty();
    
    Array.from(files).forEach((file, index) => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.append(`
                    <div class="col-md-6 mb-2">
                        <img src="${e.target.result}" class="img-thumbnail" style="max-height: 100px;">
                        <small class="d-block text-muted">${file.name}</small>
                    </div>
                `);
            };
            reader.readAsDataURL(file);
        }
    });
}

function fillSampleData() {
    if (confirm('هل تريد ملء النموذج ببيانات تجريبية؟ سيتم استبدال البيانات الحالية.')) {
        $('#number').val('12345').trigger('input');
        $('#category').val('special');
        $('#type').val('private');
        $('#code_letter').val('A');
        $('#series').val('2024');
        $('#description').val('رقم لوحة مميز بأرقام متسلسلة، مناسب للسيارات الفاخرة');
        $('#price').val('25000');
        $('#cost_price').val('20000');
        $('#is_sequential').prop('checked', true);
        
        updatePlatePreview('12345');
        updateFeaturesDisplay();
        
        showNotification('تم ملء البيانات التجريبية بنجاح', 'success');
    }
}

// Form validation
$('#plateForm').on('submit', function(e) {
    const number = $('#number').val().trim();
    const price = parseFloat($('#price').val());
    const isAuction = $('#is_auction').is(':checked');
    
    if (!number) {
        e.preventDefault();
        showNotification('يجب إدخال رقم اللوحة', 'error');
        return;
    }
    
    if (!isAuction && (!price || price <= 0)) {
        e.preventDefault();
        showNotification('يجب إدخال سعر صحيح', 'error');
        return;
    }
    
    if (isAuction) {
        const startingBid = parseFloat($('#starting_bid').val());
        if (!startingBid || startingBid <= 0) {
            e.preventDefault();
            showNotification('يجب إدخال سعر بداية صحيح للمزايدة', 'error');
            return;
        }
    }
});
</script>
{% endblock %}
