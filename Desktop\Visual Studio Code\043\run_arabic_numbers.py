#!/usr/bin/env python3
"""
تشغيل النظام مع دعم الأرقام العربية والكتابة العشوائية
Run system with Arabic numbers and random typing support
"""

import os
import sys
import subprocess
import time

# Set environment variables
os.environ['WHATSAPP_DEMO_MODE'] = 'true'
os.environ['ARABIC_NUMBERS_ENABLED'] = 'true'
os.environ['RANDOM_TYPING_ENABLED'] = 'true'

def check_requirements():
    """Check if all required files exist"""
    print("🔍 فحص المتطلبات...")
    
    required_files = [
        'static/js/arabic-numbers.js',
        'static/css/arabic-numbers.css',
        'templates/arabic-numbers-test.html',
        'templates/base.html',
        'app.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print("\n❌ الملفات التالية مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        print("\n💡 قم بتشغيل: python update_arabic_numbers.py")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def show_features():
    """Show Arabic numbers features"""
    print("\n🔢 ميزات الأرقام العربية:")
    print("=" * 40)
    print("✨ تحويل تلقائي للأرقام الإنجليزية → العربية")
    print("✨ تحويل عكسي للأرقام العربية → الإنجليزية")
    print("✨ تنسيق ذكي للعملة القطرية")
    print("✨ تأثيرات الكتابة العشوائية")
    print("✨ تحريك الأرقام والإحصائيات")
    print("✨ أرقام نابضة ومتوهجة")
    print("✨ عد تنازلي للمزايدات")
    print("✨ أرقام متحركة في الخلفية")
    print("✨ دعم كامل للوحات السيارات القطرية")

def show_examples():
    """Show usage examples"""
    print("\n📚 أمثلة الاستخدام:")
    print("=" * 30)
    print("🔢 تحويل الأرقام:")
    print("   123456 → ١٢٣٤٥٦")
    print("   123.45 → ١٢٣٫٤٥")
    print("   1,234,567 → ١،٢٣٤،٥٦٧")
    print("")
    print("💰 تنسيق العملة:")
    print("   123456 → ١٢٣،٤٥٦ ر.ق")
    print("   50000 → ٥٠،٠٠٠ ريال قطري")
    print("")
    print("🚗 أرقام اللوحات:")
    print("   A-1234 → A-١٢٣٤")
    print("   B-7777 → B-٧٧٧٧")
    print("   C-999 → C-٩٩٩")

def test_conversion():
    """Test Arabic number conversion"""
    print("\n🧪 اختبار التحويل:")
    print("=" * 25)
    
    # Test data
    test_cases = [
        ("123", "١٢٣"),
        ("456.78", "٤٥٦٫٧٨"),
        ("1,234,567", "١،٢٣٤،٥٦٧"),
        ("0", "٠"),
        ("999", "٩٩٩")
    ]
    
    # Arabic digits mapping
    arabic_digits = {
        '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
        '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩',
        '.': '٫', ',': '،'
    }
    
    def to_arabic(text):
        for english, arabic in arabic_digits.items():
            text = text.replace(english, arabic)
        return text
    
    all_passed = True
    for english, expected in test_cases:
        result = to_arabic(english)
        status = "✅" if result == expected else "❌"
        print(f"   {status} {english} → {result}")
        if result != expected:
            all_passed = False
    
    if all_passed:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    return all_passed

def start_server():
    """Start the Flask server"""
    print("\n🚀 بدء تشغيل الخادم...")
    
    try:
        from app import create_app
        app = create_app()
        
        print("✅ تم إنشاء التطبيق بنجاح")
        print("🔢 نظام الأرقام العربية: مفعل")
        print("✨ الكتابة العشوائية: مفعلة")
        print("🎨 التأثيرات البصرية: مفعلة")
        
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {str(e)}")
        return None

def show_urls():
    """Show important URLs"""
    print("\n🌐 الروابط المهمة:")
    print("=" * 25)
    print("🏠 الصفحة الرئيسية:")
    print("   http://localhost:5000")
    print("")
    print("🔢 اختبار الأرقام العربية:")
    print("   http://localhost:5000/arabic-numbers-test")
    print("")
    print("🚗 أرقام السيارات:")
    print("   http://localhost:5000/plate-numbers")
    print("")
    print("📊 لوحة التحكم:")
    print("   http://localhost:5000/dashboard")
    print("")
    print("👤 بيانات الدخول:")
    print("   المستخدم: admin")
    print("   كلمة المرور: admin123")

def show_browser_tips():
    """Show browser compatibility tips"""
    print("\n🌐 نصائح المتصفح:")
    print("=" * 20)
    print("✅ أفضل أداء: Chrome 60+ أو Firefox 55+")
    print("✅ دعم كامل: Safari 12+ أو Edge 79+")
    print("❌ غير مدعوم: Internet Explorer")
    print("")
    print("📱 الأجهزة المحمولة:")
    print("✅ iOS Safari 12+")
    print("✅ Chrome Mobile 60+")
    print("✅ Samsung Internet 8+")

def main():
    """Main function"""
    print("🔢 نظام الأرقام العربية والكتابة العشوائية")
    print("=" * 60)
    print("🎯 نظام شامل لتحويل الأرقام وإضافة التأثيرات البصرية")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ لا يمكن تشغيل النظام بدون الملفات المطلوبة")
        sys.exit(1)
    
    # Show features
    show_features()
    
    # Show examples
    show_examples()
    
    # Test conversion
    if not test_conversion():
        print("\n⚠️ تحذير: بعض اختبارات التحويل فشلت")
    
    # Start server
    app = start_server()
    if not app:
        print("\n❌ فشل في تشغيل الخادم")
        sys.exit(1)
    
    # Show URLs
    show_urls()
    
    # Show browser tips
    show_browser_tips()
    
    print("\n" + "=" * 60)
    print("🎉 النظام جاهز للاستخدام!")
    print("🔢 الأرقام العربية مفعلة في جميع أنحاء النظام")
    print("✨ تأثيرات الكتابة العشوائية متاحة")
    print("🎨 التحريك البصري للأرقام مفعل")
    print("\n🛑 اضغط Ctrl+C للإيقاف")
    print("=" * 60)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
        print("🙏 شكراً لاستخدام نظام الأرقام العربية")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
