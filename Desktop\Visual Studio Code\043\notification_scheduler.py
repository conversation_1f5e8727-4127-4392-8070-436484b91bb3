#!/usr/bin/env python3
"""
Notification Scheduler for Qatar Car Dealership System
Automatically checks for overdue payments and creates notifications
"""

import os
import sys
from datetime import datetime, date, timedelta
import schedule
import time

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Installment, Sale, Customer, Notification, User
from notifications import NotificationManager, check_overdue_payments

class NotificationScheduler:
    def __init__(self):
        self.app = create_app()
        
    def check_daily_notifications(self):
        """Daily check for overdue payments and reminders"""
        with self.app.app_context():
            try:
                print(f"🔍 فحص الإشعارات اليومية - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Check overdue payments
                success = check_overdue_payments()
                
                if success:
                    print("✅ تم فحص الأقساط المتأخرة بنجاح")
                else:
                    print("❌ حدث خطأ أثناء فحص الأقساط المتأخرة")
                
                # Additional checks
                self.check_upcoming_payments()
                self.check_monthly_reports()
                self.cleanup_old_notifications()
                
                print("🎉 تم إكمال فحص الإشعارات اليومية")
                
            except Exception as e:
                print(f"❌ خطأ في فحص الإشعارات اليومية: {e}")
    
    def check_upcoming_payments(self):
        """Check for payments due in next 3 days"""
        try:
            today = date.today()
            upcoming_date = today + timedelta(days=3)
            
            upcoming_installments = Installment.query.join(Sale).join(Customer).filter(
                Installment.due_date == upcoming_date,
                Installment.status == 'pending'
            ).all()
            
            for installment in upcoming_installments:
                # Check if reminder already sent today
                existing_notification = Notification.query.filter(
                    Notification.title.contains(f"تذكير بقسط مستحق - {installment.sale.customer.full_name}"),
                    Notification.created_at >= today
                ).first()
                
                if not existing_notification:
                    NotificationManager.create_payment_reminder(installment)
                    print(f"📅 تم إنشاء تذكير للعميل: {installment.sale.customer.full_name}")
            
            print(f"📊 تم فحص {len(upcoming_installments)} قسط مستحق خلال 3 أيام")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الأقساط المستحقة: {e}")
    
    def check_monthly_reports(self):
        """Check if monthly reports need to be generated"""
        try:
            today = date.today()
            
            # Check if it's the first day of the month
            if today.day == 1:
                # Create monthly report notification
                title = f"تقرير شهري - {today.strftime('%B %Y')}"
                message = f"""
🗓️ تقرير الشهر السابق متاح الآن

يرجى مراجعة التقارير التالية:
• تقرير المبيعات الشهرية
• تقرير الأقساط المحصلة
• تقرير الأقساط المتأخرة
• تقرير أداء الموظفين

📊 للوصول للتقارير: قسم التقارير > التقارير الشهرية
"""
                
                NotificationManager.create_system_notification(title, message, 'info')
                print("📊 تم إنشاء إشعار التقرير الشهري")
        
        except Exception as e:
            print(f"❌ خطأ في فحص التقارير الشهرية: {e}")
    
    def cleanup_old_notifications(self):
        """Clean up old read notifications (older than 30 days)"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            deleted_count = Notification.query.filter(
                Notification.is_read == True,
                Notification.created_at < cutoff_date
            ).delete()
            
            db.session.commit()
            
            if deleted_count > 0:
                print(f"🧹 تم حذف {deleted_count} إشعار قديم")
        
        except Exception as e:
            print(f"❌ خطأ في تنظيف الإشعارات القديمة: {e}")
    
    def generate_statistics_report(self):
        """Generate daily statistics"""
        with self.app.app_context():
            try:
                today = date.today()
                
                # Count overdue installments
                overdue_count = Installment.query.filter(
                    Installment.due_date < today,
                    Installment.status == 'pending'
                ).count()
                
                # Count today's payments
                today_payments = Installment.query.filter(
                    Installment.paid_date == today
                ).count()
                
                # Count active sales
                active_sales = Sale.query.filter(
                    Sale.status == 'active',
                    Sale.sale_type == 'installment'
                ).count()
                
                # Count unread notifications
                unread_notifications = Notification.query.filter(
                    Notification.is_read == False
                ).count()
                
                print("📊 إحصائيات اليوم:")
                print(f"   • الأقساط المتأخرة: {overdue_count}")
                print(f"   • المدفوعات اليوم: {today_payments}")
                print(f"   • المبيعات النشطة: {active_sales}")
                print(f"   • الإشعارات غير المقروءة: {unread_notifications}")
                
                # Create daily summary notification for managers
                if overdue_count > 0 or today_payments > 0:
                    title = f"ملخص يومي - {today.strftime('%Y/%m/%d')}"
                    message = f"""
📊 ملخص أنشطة اليوم:

💰 المدفوعات:
• تم تحصيل {today_payments} قسط اليوم

⚠️ الأقساط المتأخرة:
• {overdue_count} قسط متأخر يحتاج متابعة

📈 المبيعات النشطة:
• {active_sales} عملية بيع بالتقسيط نشطة

يرجى مراجعة التفاصيل في قسم المبيعات والتقارير.
"""
                    
                    # Send to managers only
                    managers = User.query.filter(User.role.in_(['manager', 'admin'])).all()
                    for manager in managers:
                        NotificationManager.create_notification(
                            manager.id, title, message, 'info'
                        )
                    
                    print(f"📧 تم إرسال الملخص اليومي لـ {len(managers)} مدير")
                
            except Exception as e:
                print(f"❌ خطأ في إنشاء تقرير الإحصائيات: {e}")
    
    def run_scheduler(self):
        """Run the notification scheduler"""
        print("🚀 بدء تشغيل جدولة الإشعارات...")
        print("=" * 50)
        
        # Schedule daily checks
        schedule.every().day.at("09:00").do(self.check_daily_notifications)
        schedule.every().day.at("14:00").do(self.check_daily_notifications)
        schedule.every().day.at("18:00").do(self.generate_statistics_report)
        
        # Schedule hourly checks for urgent notifications
        schedule.every().hour.do(self.check_urgent_notifications)
        
        print("⏰ تم جدولة المهام:")
        print("   • فحص يومي: 09:00, 14:00")
        print("   • تقرير إحصائيات: 18:00")
        print("   • فحص عاجل: كل ساعة")
        print()
        
        # Run initial check
        print("🔄 تشغيل فحص أولي...")
        self.check_daily_notifications()
        
        # Keep running
        print("✅ الجدولة تعمل... (اضغط Ctrl+C للإيقاف)")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف الجدولة")
    
    def check_urgent_notifications(self):
        """Check for urgent notifications every hour"""
        with self.app.app_context():
            try:
                today = date.today()
                
                # Check for installments overdue by more than 7 days
                urgent_date = today - timedelta(days=7)
                
                urgent_installments = Installment.query.join(Sale).join(Customer).filter(
                    Installment.due_date <= urgent_date,
                    Installment.status == 'pending'
                ).all()
                
                for installment in urgent_installments:
                    days_overdue = (today - installment.due_date).days
                    
                    # Check if urgent notification already sent today
                    existing_urgent = Notification.query.filter(
                        Notification.title.contains(f"عاجل - قسط متأخر {days_overdue} يوم"),
                        Notification.created_at >= today
                    ).first()
                    
                    if not existing_urgent:
                        title = f"🚨 عاجل - قسط متأخر {days_overdue} يوم - {installment.sale.customer.full_name}"
                        message = f"""
🚨 تنبيه عاجل - قسط متأخر بشدة

العميل: {installment.sale.customer.full_name}
رقم الهاتف: {installment.sale.customer.phone}
رقم القسط: {installment.installment_number}
تاريخ الاستحقاق: {installment.due_date.strftime('%Y/%m/%d')}
عدد أيام التأخير: {days_overdue} يوم
المبلغ: {installment.amount:,.0f} ريال قطري

⚠️ يتطلب إجراء فوري:
• اتصال عاجل بالعميل
• تحديد خطة سداد
• إجراءات قانونية إذا لزم الأمر
"""
                        
                        # Send to all managers and admin
                        managers = User.query.filter(User.role.in_(['manager', 'admin'])).all()
                        for manager in managers:
                            NotificationManager.create_notification(
                                manager.id, title, message, 'error'
                            )
                
                if urgent_installments:
                    print(f"🚨 تم إنشاء {len(urgent_installments)} إشعار عاجل")
                
            except Exception as e:
                print(f"❌ خطأ في فحص الإشعارات العاجلة: {e}")
    
    def run_once(self):
        """Run notification check once"""
        print("🔄 تشغيل فحص الإشعارات مرة واحدة...")
        self.check_daily_notifications()
        self.generate_statistics_report()
        print("✅ تم إكمال الفحص")

def main():
    """Main function"""
    print("🔔 جدولة الإشعارات - معرض بوخليفة للسيارات")
    print("=" * 50)
    
    scheduler = NotificationScheduler()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        scheduler.run_once()
    else:
        scheduler.run_scheduler()

if __name__ == "__main__":
    main()
